const User = require('../models/User');
require('dotenv').config();

const fixDemoUsers = async () => {
  try {
    const db = require('../db');
    await db.connect();
    console.log('Connected to database');

    // Update demo users to be verified and approved
    const demoEmails = [
      '<EMAIL>',
      '<EMAIL>', 
      '<EMAIL>',
      '<EMAIL>'
    ];

    for (const email of demoEmails) {
      const result = await User.updateOne(
        { email: email },
        {
          $set: {
            emailVerified: true,
            isApproved: true,
            status: 'approved'
          }
        }
      );
      
      if (result.matchedCount > 0) {
        console.log(`✅ Updated user: ${email}`);
      } else {
        console.log(`❌ User not found: ${email}`);
      }
    }

    console.log('\nDemo users verification status updated!');

  } catch (error) {
    console.error('Error fixing demo users:', error);
  } finally {
    process.exit(0);
  }
};

fixDemoUsers();
