const bcrypt = require('bcryptjs');
const User = require('../models/User');
const mongoose = require('mongoose');
require('dotenv').config();

const createDemoUsers = async () => {
  try {
    const db = require('../db');
    await db.connect();
    console.log('Connected to database');

    // Check if demo users already exist
    const existingUser = await User.findOne({ email: '<EMAIL>' });
    if (existingUser) {
      console.log('Demo users already exist');
      return;
    }

    const hashedPassword = await bcrypt.hash('demo123', 10);

    // Create demo users
    const demoUsers = [
      {
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'individual',
        profile: {
          name: 'أحمد محمد العلي',
          phone: '+966501234567',
          city: 'الرياض',
          address: 'حي النخيل، شارع الملك فهد',
          nationalId: '1234567890'
        },
        isVerified: true,
        isApproved: true,
        status: 'approved'
      },
      {
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'company',
        profile: {
          name: 'شركة التقنية المتقدمة',
          phone: '+966112345678',
          city: 'الرياض',
          address: 'حي العليا، برج الفيصلية',
          commercialRegister: 'CR1234567890',
          companyType: 'تقنية المعلومات',
          establishedYear: 2015,
          employeeCount: 50
        },
        isVerified: true,
        isApproved: true,
        status: 'approved'
      },
      {
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'government',
        profile: {
          name: 'أمانة منطقة الرياض',
          phone: '+966114567890',
          city: 'الرياض',
          address: 'حي الملز، مجمع الوزارات',
          department: 'إدارة المشاريع',
          officialId: 'GOV123456'
        },
        isVerified: true,
        isApproved: true,
        status: 'approved'
      },
      {
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'admin',
        profile: {
          name: 'مدير النظام',
          phone: '+966501111111',
          city: 'الرياض',
          address: 'مقر الشركة'
        },
        isVerified: true,
        isApproved: true,
        status: 'approved'
      }
    ];

    // Insert demo users
    const createdUsers = await User.insertMany(demoUsers);
    console.log(`Created ${createdUsers.length} demo users:`);
    
    createdUsers.forEach(user => {
      console.log(`- ${user.profile.name} (${user.email}) - ${user.role}`);
    });

    console.log('\nDemo user credentials:');
    console.log('Individual User: <EMAIL> / demo123');
    console.log('Company User: <EMAIL> / demo123');
    console.log('Government User: <EMAIL> / demo123');
    console.log('Admin User: <EMAIL> / demo123');

  } catch (error) {
    console.error('Error creating demo users:', error);
  } finally {
    process.exit(0);
  }
};

createDemoUsers();
