const User = require('../models/User');
const Auction = require('../models/Auction');
const Tender = require('../models/Tender');
require('dotenv').config();

const createDemoData = async () => {
  try {
    const db = require('../db');
    await db.connect();
    console.log('Connected to database');

    // Get demo users
    const individualUser = await User.findOne({ email: '<EMAIL>' });
    const companyUser = await User.findOne({ email: '<EMAIL>' });
    const governmentUser = await User.findOne({ email: '<EMAIL>' });

    if (!individualUser || !companyUser || !governmentUser) {
      console.log('Demo users not found. Please run create-demo-users.js first');
      return;
    }

    // Check if demo data already exists
    const existingAuction = await Auction.findOne({ title: /BMW X5/ });
    if (existingAuction) {
      console.log('Demo data already exists');
      return;
    }

    // Create demo auctions
    const demoAuctions = [
      {
        title: 'سيارة BMW X5 موديل 2020',
        description: 'سيارة فاخرة بحالة ممتازة، قطعت 45,000 كم فقط. صيانة دورية منتظمة، جميع الأوراق سليمة.',
        category: 'vehicles',
        startingPrice: 150000,
        currentPrice: 180000,
        reservePrice: 200000,
        startDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // Started 2 days ago
        endDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // Ends in 5 days
        seller: companyUser._id,
        auctioneer: companyUser._id,
        status: 'active',
        images: [
          {
            url: 'https://images.unsplash.com/photo-1555215695-3004980ad54e?w=800',
            caption: 'BMW X5 - المظهر الخارجي',
            isMain: true
          },
          {
            url: 'https://images.unsplash.com/photo-1552519507-da3b142c6e3d?w=800',
            caption: 'BMW X5 - المظهر الداخلي'
          }
        ],
        location: {
          city: 'الرياض',
          region: 'منطقة الرياض'
        },
        condition: 'excellent',
        specifications: {
          year: 2020,
          mileage: 45000,
          color: 'أسود',
          transmission: 'أوتوماتيك',
          fuelType: 'بنزين'
        },
        bids: [
          {
            bidder: individualUser._id,
            amount: 160000,
            timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
          },
          {
            bidder: individualUser._id,
            amount: 180000,
            timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000)
          }
        ]
      },
      {
        title: 'لوحة فنية أصلية للفنان محمد الشمراني',
        description: 'لوحة زيتية نادرة من مجموعة خاصة، مقاس 80x60 سم، موقعة من الفنان.',
        category: 'art_collectibles',
        startingPrice: 15000,
        currentPrice: 25000,
        reservePrice: 30000,
        startDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
        endDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
        seller: individualUser._id,
        auctioneer: individualUser._id,
        status: 'active',
        images: [
          {
            url: 'https://images.unsplash.com/photo-1541961017774-22349e4a1262?w=800',
            caption: 'لوحة فنية أصلية',
            isMain: true
          }
        ],
        location: {
          city: 'جدة',
          region: 'منطقة مكة المكرمة'
        },
        condition: 'excellent',
        specifications: {
          artist: 'محمد الشمراني',
          year: 2018,
          medium: 'زيت على قماش',
          size: '80x60 سم'
        }
      },
      {
        title: 'ساعة رولكس أصلية',
        description: 'ساعة رولكس سابمارينر بحالة ممتازة مع الضمان والعلبة الأصلية.',
        category: 'jewelry',
        startingPrice: 35000,
        currentPrice: 45000,
        reservePrice: 50000,
        startDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
        endDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
        seller: companyUser._id,
        auctioneer: companyUser._id,
        status: 'active',
        images: [
          {
            url: 'https://images.unsplash.com/photo-1523170335258-f5c6c6bd6eaf?w=800',
            caption: 'ساعة رولكس سابمارينر',
            isMain: true
          }
        ],
        location: {
          city: 'الدمام',
          region: 'المنطقة الشرقية'
        },
        condition: 'excellent',
        specifications: {
          brand: 'رولكس',
          model: 'سابمارينر',
          year: 2019,
          material: 'ستانلس ستيل'
        }
      }
    ];

    // Create demo tenders
    const demoTenders = [
      {
        title: 'مشروع إنشاء مجمع تجاري في الرياض',
        description: 'مناقصة لإنشاء مجمع تجاري متكامل بمساحة 50,000 متر مربع يشمل محلات تجارية ومطاعم ومواقف سيارات.',
        category: 'construction',
        budget: *********,
        startDate: new Date(), // Start now
        deadline: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        location: 'الرياض',
        organizer: governmentUser._id,
        status: 'open',
        requirements: [
          'خبرة لا تقل عن 10 سنوات في مشاريع مماثلة',
          'رأس مال لا يقل عن 50 مليون ريال',
          'فريق عمل متخصص في الإنشاءات التجارية',
          'شهادات الجودة المطلوبة'
        ],
        documents: [
          'السجل التجاري',
          'شهادة الزكاة والضريبة',
          'خطابات الخبرة',
          'البيانات المالية للسنوات الثلاث الماضية'
        ],
        evaluationCriteria: {
          technical: 40,
          financial: 40,
          experience: 20
        },
        terms: 'يجب الالتزام بالمواصفات الفنية والجودة المطلوبة وتسليم المشروع في الموعد المحدد.',
        deliveryTerms: 'التسليم خلال 24 شهر من تاريخ توقيع العقد',
        paymentTerms: 'الدفع على دفعات حسب مراحل الإنجاز'
      },
      {
        title: 'توريد أجهزة حاسوب للمدارس',
        description: 'مناقصة توريد 5000 جهاز حاسوب للمدارس الحكومية مع البرامج والصيانة.',
        category: 'it_technology',
        budget: 25000000,
        startDate: new Date(),
        deadline: new Date(Date.now() + 20 * 24 * 60 * 60 * 1000),
        location: 'جميع أنحاء المملكة',
        organizer: governmentUser._id,
        status: 'open',
        requirements: [
          'أجهزة حاسوب حديثة بمواصفات محددة',
          'ضمان لمدة 3 سنوات',
          'تدريب المستخدمين',
          'صيانة دورية'
        ],
        documents: [
          'كتالوج المنتجات',
          'شهادات الجودة',
          'خطة التدريب',
          'عقد الصيانة'
        ],
        evaluationCriteria: {
          technical: 50,
          financial: 30,
          experience: 20
        }
      },
      {
        title: 'خدمات النظافة للمستشفيات',
        description: 'مناقصة تقديم خدمات النظافة والتعقيم للمستشفيات الحكومية لمدة سنتين.',
        category: 'services',
        budget: 12000000,
        startDate: new Date(),
        deadline: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000),
        location: 'الرياض والمنطقة الشرقية',
        organizer: governmentUser._id,
        status: 'open',
        requirements: [
          'خبرة في تنظيف المستشفيات',
          'استخدام مواد تعقيم معتمدة',
          'فريق عمل مدرب',
          'توفر على مدار الساعة'
        ],
        documents: [
          'شهادات التدريب',
          'قائمة المواد المستخدمة',
          'خطة العمل',
          'التأمين على العمال'
        ],
        evaluationCriteria: {
          technical: 35,
          financial: 45,
          experience: 20
        }
      }
    ];

    // Insert demo auctions
    const createdAuctions = await Auction.insertMany(demoAuctions);
    console.log(`Created ${createdAuctions.length} demo auctions`);

    // Insert demo tenders
    const createdTenders = await Tender.insertMany(demoTenders);
    console.log(`Created ${createdTenders.length} demo tenders`);

    console.log('\nDemo data created successfully!');
    console.log('You can now test the application with realistic data.');

  } catch (error) {
    console.error('Error creating demo data:', error);
  } finally {
    process.exit(0);
  }
};

createDemoData();
