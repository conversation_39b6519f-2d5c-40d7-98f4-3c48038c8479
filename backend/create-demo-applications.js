const mongoose = require('mongoose');
const Tender = require('./models/Tender');
const User = require('./models/User');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/auction_platform')
  .then(async () => {
    console.log('Connected to MongoDB');

    // First, create demo company users
    const company1 = new User({
      email: '<EMAIL>',
      password: '$2a$10$dummy.hash.for.demo.purposes.only',
      role: 'company',
      emailVerified: true,
      status: 'active',
      profile: {
        fullName: 'أحمد محمد العلي',
        companyName: 'شركة التقنية المتقدمة',
        commercialRegister: '1010123456',
        authorizedSignatory: 'أحمد محمد العلي',
        phone: '0501234567',
        city: 'الرياض',
        address: 'شارع الملك فهد، الرياض'
      }
    });

    const company2 = new User({
      email: '<EMAIL>',
      password: '$2a$10$dummy.hash.for.demo.purposes.only',
      role: 'company',
      emailVerified: true,
      status: 'active',
      profile: {
        fullName: 'سارة أحمد الخالد',
        companyName: 'شركة الإبداع للحلول الذكية',
        commercialRegister: '2020567890',
        authorizedSignatory: 'سارة أحمد الخالد',
        phone: '0509876543',
        city: 'جدة',
        address: 'شارع التحلية، جدة'
      }
    });

    // Save users (if they don't exist)
    let user1, user2;
    try {
      user1 = await User.findOne({ email: '<EMAIL>' }) || await company1.save();
      user2 = await User.findOne({ email: '<EMAIL>' }) || await company2.save();
      console.log('Demo users created/found');
    } catch (error) {
      console.log('Users might already exist, continuing...');
      user1 = await User.findOne({ email: '<EMAIL>' });
      user2 = await User.findOne({ email: '<EMAIL>' });
    }

    // Find the tender
    const tender = await Tender.findById('687150821be4922db2f3d42b');
    if (!tender) {
      console.error('Tender not found!');
      process.exit(1);
    }

    // Create demo proposals
    const proposals = [
      {
        proposer: user1._id,
        amount: 700000,
        proposal: 'نحن شركة متخصصة في تطوير المنصات الحكومية الذكية مع خبرة تزيد عن 10 سنوات في هذا المجال. نقترح استخدام أحدث التقنيات لتطوير منصة متطورة تلبي جميع المتطلبات. نضمن التسليم في الوقت المحدد مع أعلى معايير الجودة.',
        documents: [],
        status: 'pending',
        submittedAt: new Date()
      },
      {
        proposer: user2._id,
        amount: 680000,
        proposal: 'نتميز بفريق عمل متخصص في تطوير المنصات الذكية باستخدام أحدث التقنيات مثل React وNode.js. نضمن تسليم منصة عالية الجودة تتميز بسهولة الاستخدام والأمان. عرض مالي مميز مع ضمان الجودة والصيانة لمدة سنة كاملة.',
        documents: [],
        status: 'pending',
        submittedAt: new Date(Date.now() - 24 * 60 * 60 * 1000) // 1 day ago
      }
    ];

    // Add proposals to tender
    tender.proposals.push(...proposals);
    await tender.save();

    console.log(`Added ${proposals.length} demo proposals to tender`);
    console.log('\nCreated proposals:');
    proposals.forEach((proposal, index) => {
      const companyName = index === 0 ? 'شركة التقنية المتقدمة' : 'شركة الإبداع للحلول الذكية';
      console.log(`- ${companyName}: ${proposal.amount} SAR (${proposal.status})`);
    });

    process.exit(0);
  })
  .catch(err => {
    console.error('Error:', err);
    process.exit(1);
  });
