{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:08:31:831"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:08:31:831"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:09:00:90"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:10:00:100"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:10:34:1034"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:10:34:1034"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:10:43:1043"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:10:43:1043"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:10:55:1055"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:10:55:1055"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:11:00:110"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:12:00:120"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:12:59:1259"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:12:59:1259"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:13:00:130"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:13:17:1317"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:13:17:1317"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:14:00:140"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:14:06:146"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:14:06:146"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:14:36:1436"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:14:36:1436"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:15:00:150"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:16:00:160"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 14:16:35:1635","url":"/api/user/dashboard/stats","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/profile\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:85:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"PUT","timestamp":"2025-07-11 14:16:36:1636","url":"/api/user/profile","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/company/applications\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 14:16:36:1636","url":"/api/company/applications","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/dashboard\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 14:16:37:1637","url":"/api/government/dashboard","userAgent":"axios/1.10.0"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:18:00:180"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:18:09:189"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:18:09:189"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:18:43:1843"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:18:43:1843"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:19:00:190"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:19:18:1918"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:19:18:1918"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:20:00:200"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 14:20:14:2014","url":"/api/user/dashboard/stats","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/profile\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:85:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"PUT","timestamp":"2025-07-11 14:20:14:2014","url":"/api/user/profile","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/company/applications\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 14:20:15:2015","url":"/api/company/applications","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/dashboard\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 14:20:15:2015","url":"/api/government/dashboard","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/admin/users/6870f3660040fd0e29176f8c/approve\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"PUT","timestamp":"2025-07-11 14:20:16:2016","url":"/api/admin/users/6870f3660040fd0e29176f8c/approve","userAgent":"axios/1.10.0"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:22:00:220"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:23:00:230"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:24:00:240"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:25:00:250"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:26:00:260"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:27:00:270"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:28:00:280"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/company/applications\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 14:28:33:2833","url":"/api/company/applications","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/dashboard\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 14:28:33:2833","url":"/api/government/dashboard","userAgent":"axios/1.10.0"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:29:00:290"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:30:00:300"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:31:00:310"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:31:47:3147"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:31:47:3147"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:32:00:320"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:32:09:329"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:32:09:329"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:32:49:3249"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:32:49:3249"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:33:00:330"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:33:04:334"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:33:04:334"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:33:20:3320"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:33:20:3320"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:33:31:3331"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:33:31:3331"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:33:41:3341"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:33:41:3341"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:34:00:340"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:35:00:350"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:36:00:360"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:37:00:370"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:39:00:390"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:40:00:400"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:40:41:4041"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:40:41:4041"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:41:00:410"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:42:00:420"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:43:00:430"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:44:00:440"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:45:00:450"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:46:00:460"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:48:02:482"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:48:02:482"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:49:00:490"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:50:00:500"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:51:00:510"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:52:00:520"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:52:00:520"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:52:00:520"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:53:00:530"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:53:39:5339"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:53:39:5339"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:54:00:540"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:54:12:5412"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:54:12:5412"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:54:29:5429"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:54:29:5429"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:55:00:550"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:56:00:560"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:57:00:570"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:58:00:580"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:59:00:590"}
{"level":"info","message":"Running account activation timeout check...","timestamp":"2025-07-11 15:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:01:00:10"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:02:00:20"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:03:00:30"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:04:00:40"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:05:00:50"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:06:00:60"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:07:00:70"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:08:00:80"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:09:00:90"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:10:00:100"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:11:00:110"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:12:00:120"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:13:00:130"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:14:00:140"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:15:00:150"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:16:00:160"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:18:00:180"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:19:00:190"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:20:00:200"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:22:00:220"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:23:00:230"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:24:00:240"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:25:00:250"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:26:00:260"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:27:00:270"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:28:00:280"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:29:00:290"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:30:00:300"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:31:00:310"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:32:00:320"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:33:00:330"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:34:00:340"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:35:00:350"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:36:00:360"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:37:00:370"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:39:01:391"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:40:00:400"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:41:00:410"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:42:00:420"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:43:00:430"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 15:43:48:4348"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 15:43:48:4348"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:44:00:440"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:45:00:450"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 15:45:14:4514"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 15:45:14:4514"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:46:00:460"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 15:46:19:4619"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 15:46:19:4619"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:47:00:470"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 15:47:16:4716"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 15:47:16:4716"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/tenders/687107e023168d085301922c/apply\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at Function.handle (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:47:12)","method":"POST","timestamp":"2025-07-11 15:47:28:4728","url":"/api/tenders/687107e023168d085301922c/apply","userAgent":"axios/1.10.0"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:48:00:480"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:49:00:490"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:50:00:500"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:51:00:510"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:52:00:520"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:53:00:530"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/6871093b23168d085301924c/applications\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 15:53:15:5315","url":"/api/government/tenders/6871093b23168d085301924c/applications","userAgent":"axios/1.10.0"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:54:00:540"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:55:00:550"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:56:00:560"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:57:00:570"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 15:57:24:5724"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 15:57:24:5724"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 15:57:40:5740"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 15:57:40:5740"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:58:00:580"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 15:58:02:582"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 15:58:02:582"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:59:00:590"}
{"level":"info","message":"Running account activation timeout check...","timestamp":"2025-07-11 16:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:00:00:00"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 16:00:22:022"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 16:00:22:022"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:01:00:10"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:02:00:20"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/68710b4f07a04ce831926d6d/applications\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 16:02:07:27","url":"/api/government/tenders/68710b4f07a04ce831926d6d/applications","userAgent":"axios/1.10.0"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:03:00:30"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:04:00:40"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:05:00:50"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/68710c1e07a04ce831926ea9/applications\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 16:05:34:534","url":"/api/government/tenders/68710c1e07a04ce831926ea9/applications","userAgent":"axios/1.10.0"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:06:00:60"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 16:07:24:724"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 16:07:24:724"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:08:00:80"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:09:00:90"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:10:00:100"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:11:00:110"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:12:00:120"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:13:00:130"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:14:00:140"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:15:00:150"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:16:00:160"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:18:00:180"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:19:00:190"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:20:00:200"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 16:20:57:2057"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 16:20:57:2057"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:22:00:220"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:23:00:230"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/applications?limit=100\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 16:23:26:2326","url":"/api/government/applications?limit=100","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/applications?limit=100\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 16:23:26:2326","url":"/api/government/applications?limit=100","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:24:00:240"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/notifications?limit=5\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 16:24:33:2433","url":"/api/notifications?limit=5","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/notifications?limit=5\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 16:24:33:2433","url":"/api/notifications?limit=5","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:25:00:250"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/notifications?limit=5\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 16:25:01:251","url":"/api/notifications?limit=5","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/notifications?limit=5\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 16:25:01:251","url":"/api/notifications?limit=5","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:26:00:260"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:27:00:270"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:28:00:280"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:29:00:290"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:30:00:300"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:31:00:310"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:32:00:320"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:33:00:330"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:34:00:340"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 16:34:29:3429"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 16:34:29:3429"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:35:00:350"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 16:35:28:3528"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 16:35:28:3528"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:36:00:360"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 16:36:09:369"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 16:36:09:369"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 16:36:42:3642"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 16:36:42:3642"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 16:36:55:3655"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 16:36:55:3655"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:37:00:370"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 16:37:09:379"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 16:37:09:379"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 16:37:43:3743"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 16:37:43:3743"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:39:00:390"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:40:00:400"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:41:00:410"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:42:00:420"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 16:42:10:4210"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 16:42:10:4210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:43:00:430"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:44:00:440"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:45:00:450"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:46:00:460"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:47:00:470"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:48:00:480"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:49:00:490"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:50:00:500"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:51:00:510"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:52:00:520"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:53:00:530"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:54:00:540"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:55:00:550"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:56:00:560"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:57:00:570"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:58:00:580"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:59:00:590"}
{"level":"info","message":"Running account activation timeout check...","timestamp":"2025-07-11 17:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:01:00:10"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:02:00:20"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:03:00:30"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:04:00:40"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:05:00:50"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:06:00:60"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:07:00:70"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:08:00:80"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:09:00:90"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:10:00:100"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:11:00:110"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:12:00:120"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:13:00:130"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:14:00:140"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:15:00:150"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:16:00:160"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:18:00:180"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:19:00:190"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:20:00:200"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:22:00:220"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:23:00:230"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:24:00:240"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:25:00:250"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:26:00:260"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:27:00:270"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:28:00:280"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:29:00:290"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:30:00:300"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:31:00:310"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:32:00:320"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:33:00:330"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:34:00:340"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:35:00:350"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:36:00:360"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:37:00:370"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:39:00:390"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:40:00:400"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:41:00:410"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:42:00:420"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:43:00:430"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:44:00:440"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:45:00:450"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:46:00:460"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:47:00:470"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:48:00:480"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:49:00:490"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:50:00:500"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:51:00:510"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:52:00:520"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:53:00:530"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:54:00:540"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:55:00:550"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:56:00:560"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:57:00:570"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:58:00:580"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:59:00:590"}
{"level":"info","message":"Running account activation timeout check...","timestamp":"2025-07-11 18:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:01:00:10"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:02:00:20"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:03:00:30"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:04:00:40"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:05:00:50"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:06:00:60"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 18:06:57:657","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 18:06:57:657","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:07:00:70"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:08:00:80"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:09:00:90"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:10:00:100"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:11:00:110"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:12:00:120"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:13:00:130"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:14:00:140"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:15:00:150"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:16:00:160"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:18:00:180"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:19:00:190"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:20:00:200"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:22:00:220"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:23:00:230"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:24:00:240"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:25:00:250"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 18:26:46:2646"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 18:26:46:2646"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:27:00:270"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/admin/users/68712d53fd8732605b267a80\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 18:27:36:2736","url":"/api/admin/users/68712d53fd8732605b267a80","userAgent":"axios/1.10.0"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:28:00:280"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:29:00:290"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:30:00:300"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:31:00:310"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:32:00:320"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/admin/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 18:32:19:3219","url":"/api/admin/stats","userAgent":"axios/1.10.0"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:33:00:330"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:34:00:340"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:35:00:350"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/admin/reports/user-activity\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 18:35:20:3520","url":"/api/admin/reports/user-activity","userAgent":"axios/1.10.0"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:36:00:360"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/admin/performance\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 18:36:02:362","url":"/api/admin/performance","userAgent":"axios/1.10.0"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:37:00:370"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/admin/moderation/flagged\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 18:37:43:3743","url":"/api/admin/moderation/flagged","userAgent":"axios/1.10.0"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:39:00:390"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:40:00:400"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 18:40:57:4057"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 18:40:57:4057"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:41:00:410"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:42:00:420"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:43:00:430"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:44:00:440"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:45:00:450"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:46:00:460"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:47:00:470"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:48:00:480"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:49:00:490"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:50:00:500"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:51:00:510"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:52:00:520"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:53:00:530"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:54:00:540"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:55:00:550"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:56:00:560"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:57:00:570"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:58:00:580"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:59:00:590"}
{"level":"info","message":"Running account activation timeout check...","timestamp":"2025-07-11 19:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:01:00:10"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/payment/methods\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:01:54:154","url":"/api/payment/methods","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/payment/methods\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:01:54:154","url":"/api/payment/methods","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:02:00:20"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/payment/methods\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:02:03:23","url":"/api/payment/methods","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/payment/methods\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:02:03:23","url":"/api/payment/methods","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/balance\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:02:13:213","url":"/api/wallet/balance","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/transactions\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:02:13:213","url":"/api/wallet/transactions","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/balance\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:02:13:213","url":"/api/wallet/balance","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/transactions\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:02:13:213","url":"/api/wallet/transactions","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/system/status\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:02:44:244","url":"/api/system/status","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/system/status\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:02:44:244","url":"/api/system/status","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:03:00:30"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/billing/invoices\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:03:11:311","url":"/api/billing/invoices","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/billing/invoices\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:03:11:311","url":"/api/billing/invoices","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:04:00:40"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:05:00:50"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/billing/invoices/2/pay\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:103:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"POST","timestamp":"2025-07-11 19:05:21:521","url":"/api/billing/invoices/2/pay","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/system/status\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:05:25:525","url":"/api/system/status","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/system/status\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:05:25:525","url":"/api/system/status","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/balance\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:05:28:528","url":"/api/wallet/balance","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/transactions\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:05:28:528","url":"/api/wallet/transactions","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/transactions\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:05:28:528","url":"/api/wallet/transactions","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/balance\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:05:28:528","url":"/api/wallet/balance","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/payment/methods\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:05:29:529","url":"/api/payment/methods","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/payment/methods\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:05:29:529","url":"/api/payment/methods","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:06:00:60"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:06:10:610","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:06:10:610","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:07:01:71"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:08:00:80"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:09:00:90"}
{"ip":"::1","level":"error","message":"Error: Not found - /favicon.ico\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:09:41:941","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:10:00:100"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 19:10:42:1042"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 19:10:42:1042"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:11:00:110"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 19:11:27:1127"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 19:11:27:1127"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:12:00:120"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 19:12:04:124"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 19:12:04:124"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:13:00:130"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:14:00:140"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:15:00:150"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:16:00:160"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:18:00:180"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 19:18:18:1818"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 19:18:18:1818"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 19:18:33:1833"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 19:18:33:1833"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 19:18:47:1847"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 19:18:47:1847"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:19:00:190"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 19:19:59:1959"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 19:19:59:1959"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:20:00:200"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 19:20:13:2013"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 19:20:13:2013"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 19:20:26:2026"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 19:20:26:2026"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 19:20:40:2040"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 19:20:40:2040"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:22:00:220"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 19:22:03:223"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 19:22:03:223"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 19:22:27:2227"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 19:22:27:2227"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 19:22:49:2249"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 19:22:49:2249"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:23:00:230"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 19:23:48:2348"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 19:23:48:2348"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:24:00:240"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 19:24:11:2411"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 19:24:11:2411"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 19:24:28:2428"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 19:24:28:2428"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:25:00:250"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:26:00:260"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:27:00:270"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:28:00:280"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:29:00:290"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:30:00:300"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:31:00:310"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:32:00:320"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:33:00:330"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 19:33:19:3319"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 19:33:19:3319"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:34:00:340"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:35:00:350"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:35:25:3525","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:35:25:3525","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:36:00:360"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:37:00:370"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/balance\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:37:47:3747","url":"/api/wallet/balance","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/transactions\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:37:47:3747","url":"/api/wallet/transactions","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/balance\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:37:47:3747","url":"/api/wallet/balance","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/transactions\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:37:47:3747","url":"/api/wallet/transactions","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:39:00:390"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:40:00:400"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:41:00:410"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:42:00:420"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:43:00:430"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:44:00:440"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:45:00:450"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:46:00:460"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:47:00:470"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:48:00:480"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:49:00:490"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:50:00:500"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:51:00:510"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:52:00:520"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:53:00:530"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:54:00:540"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:55:00:550"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:56:00:560"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:57:00:570"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:58:00:580"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:59:00:590"}
{"level":"info","message":"Running account activation timeout check...","timestamp":"2025-07-11 20:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:01:00:10"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:02:00:20"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:03:00:30"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:04:00:40"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:05:00:50"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:06:00:60"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:07:00:70"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:08:00:80"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:09:00:90"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:10:00:100"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:11:00:110"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:12:00:120"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:13:00:130"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:14:00:140"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:15:00:150"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:16:00:160"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:18:00:180"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:19:00:190"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:20:00:200"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:22:00:220"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:23:00:230"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:24:00:240"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:25:00:250"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:26:00:260"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:27:00:270"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:28:00:280"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:29:00:290"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:30:00:300"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:31:00:310"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:32:00:320"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:33:00:330"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:34:00:340"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:35:00:350"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:36:00:360"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:37:00:370"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:39:00:390"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:40:00:400"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:41:00:410"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:42:00:420"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:43:00:430"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:44:00:440"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:45:00:450"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:46:00:460"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:47:00:470"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:48:00:480"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:49:00:490"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:50:00:500"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:51:00:510"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:52:00:520"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:53:00:530"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:54:00:540"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:55:00:550"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:56:00:560"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:57:00:570"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:58:00:580"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:59:00:590"}
{"level":"info","message":"Running account activation timeout check...","timestamp":"2025-07-11 21:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:00:00:00"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/6871501b1be4922db2f3d424\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:00:35:035","url":"/api/government/tenders/6871501b1be4922db2f3d424","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/6871501b1be4922db2f3d424\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:00:35:035","url":"/api/government/tenders/6871501b1be4922db2f3d424","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/687150821be4922db2f3d42b\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:00:50:050","url":"/api/government/tenders/687150821be4922db2f3d42b","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/687150821be4922db2f3d42b\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:00:51:051","url":"/api/government/tenders/687150821be4922db2f3d42b","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:01:00:10"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/687150821be4922db2f3d42b\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:01:32:132","url":"/api/government/tenders/687150821be4922db2f3d42b","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/687150821be4922db2f3d42b\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:01:32:132","url":"/api/government/tenders/687150821be4922db2f3d42b","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:02:00:20"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/687150821be4922db2f3d42b\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:02:02:22","url":"/api/government/tenders/687150821be4922db2f3d42b","userAgent":"curl/8.7.1"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:03:00:30"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:04:00:40"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/687150821be4922db2f3d42b\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:04:02:42","url":"/api/government/tenders/687150821be4922db2f3d42b","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/687150821be4922db2f3d42b\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:04:02:42","url":"/api/government/tenders/687150821be4922db2f3d42b","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:05:00:50"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:06:00:60"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:07:00:70"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:08:00:80"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:09:00:90"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:10:00:100"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:11:00:110"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:12:00:120"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/687150821be4922db2f3d42b\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:12:10:1210","url":"/api/government/tenders/687150821be4922db2f3d42b","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/687150821be4922db2f3d42b\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:12:10:1210","url":"/api/government/tenders/687150821be4922db2f3d42b","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/applications?limit=100\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:12:18:1218","url":"/api/government/applications?limit=100","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/applications?limit=100\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:12:18:1218","url":"/api/government/applications?limit=100","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:13:00:130"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:14:00:140"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:15:00:150"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/687150821be4922db2f3d42b\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:15:53:1553","url":"/api/government/tenders/687150821be4922db2f3d42b","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/687150821be4922db2f3d42b\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:15:53:1553","url":"/api/government/tenders/687150821be4922db2f3d42b","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:16:00:160"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/687150821be4922db2f3d42b\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:16:53:1653","url":"/api/government/tenders/687150821be4922db2f3d42b","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/687150821be4922db2f3d42b\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:16:54:1654","url":"/api/government/tenders/687150821be4922db2f3d42b","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:18:00:180"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:19:00:190"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:20:00:200"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/6871501b1be4922db2f3d424\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"DELETE","timestamp":"2025-07-11 21:20:16:2016","url":"/api/government/tenders/6871501b1be4922db2f3d424","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:22:00:220"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/6871501b1be4922db2f3d424\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"DELETE","timestamp":"2025-07-11 21:22:26:2226","url":"/api/government/tenders/6871501b1be4922db2f3d424","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:23:00:230"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:24:00:240"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:25:00:250"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:26:00:260"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:27:00:270"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:28:00:280"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:29:00:290"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:30:00:300"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:31:00:310"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:32:00:320"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:33:00:330"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:34:00:340"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:35:00:350"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:36:00:360"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:37:00:370"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:39:00:390"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:40:00:400"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:41:00:410"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:42:00:420"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:43:00:430"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:44:00:440"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:45:00:450"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:46:00:460"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:47:00:470"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:48:00:480"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:49:00:490"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:50:00:500"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:51:00:510"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:52:00:520"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:53:00:530"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:54:00:540"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:55:00:550"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:56:00:560"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:57:00:570"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:58:00:580"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:59:00:590"}
{"level":"info","message":"Running account activation timeout check...","timestamp":"2025-07-11 22:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:01:00:10"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:02:00:20"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:03:00:30"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:04:00:40"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:05:00:50"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:06:00:60"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:07:00:70"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:08:00:80"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:09:00:90"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:10:00:100"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:11:00:110"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:12:01:121"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:13:00:130"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:14:00:140"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:15:00:150"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:16:00:160"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:18:00:180"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:19:00:190"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:20:00:200"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:22:00:220"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:23:00:230"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:24:00:240"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:25:00:250"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:26:00:260"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:27:00:270"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:28:00:280"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:29:00:290"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:30:00:300"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:31:00:310"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 22:31:35:3135"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 22:31:35:3135"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:32:00:320"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:33:00:330"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:34:00:340"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:35:00:350"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:36:00:360"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:37:00:370"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:39:00:390"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:40:00:400"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:41:00:410"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:42:00:420"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:43:00:430"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:44:00:440"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:45:00:450"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:46:00:460"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:47:00:470"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:48:00:480"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:49:00:490"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:50:00:500"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/applications/68716592746ef9155eefe3a9\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 22:50:53:5053","url":"/api/government/applications/68716592746ef9155eefe3a9","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/applications/68716592746ef9155eefe3a9\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 22:50:53:5053","url":"/api/government/applications/68716592746ef9155eefe3a9","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:51:00:510"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/applications/68716592746ef9155eefe3a9\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 22:51:17:5117","url":"/api/government/applications/68716592746ef9155eefe3a9","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/applications/68716592746ef9155eefe3a9\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 22:51:17:5117","url":"/api/government/applications/68716592746ef9155eefe3a9","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:52:00:520"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 22:52:08:528"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 22:52:08:528"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:53:00:530"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 22:53:16:5316"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 22:53:16:5316"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:54:00:540"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:55:00:550"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:56:00:560"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/applications/68716592746ef9155eefe3a9/status\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"PATCH","timestamp":"2025-07-11 22:56:37:5637","url":"/api/government/applications/68716592746ef9155eefe3a9/status","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:57:00:570"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 22:57:03:573"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 22:57:03:573"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 22:57:54:5754"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 22:57:54:5754"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:58:00:580"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 22:58:04:584"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 22:58:04:584"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:59:00:590"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 22:59:16:5916"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 22:59:16:5916"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 22:59:29:5929"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 22:59:29:5929"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:01:00:10"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:02:00:20"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:03:00:30"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:04:00:40"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:05:00:50"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:06:00:60"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:07:00:70"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:08:00:80"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:09:00:90"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:10:00:100"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:11:00:110"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:12:00:120"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:13:00:130"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:14:00:140"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:15:00:150"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:16:00:160"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:18:00:180"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:19:00:190"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:20:00:200"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:22:00:220"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 23:22:30:2230"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 23:22:30:2230"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:23:00:230"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 23:23:47:2347"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 23:23:47:2347"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:24:00:240"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:25:00:250"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:26:00:260"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:27:00:270"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 23:27:35:2735"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 23:27:35:2735"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 23:27:52:2752"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 23:27:52:2752"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:28:00:280"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 23:28:05:285"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 23:28:05:285"}
