const express = require('express');
const router = express.Router();
const Favorite = require('../models/Favorite');
const { authenticate } = require('../middleware/auth');
const { body, validationResult } = require('express-validator');

// Get user's favorites
router.get('/', authenticate, async (req, res) => {
  try {
    const favorites = await Favorite.getUserFavorites(req.user._id);
    res.json({
      success: true,
      data: {
        favorites: favorites || []
      }
    });
  } catch (error) {
    console.error('Error fetching favorites:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Add item to favorites
router.post('/add', [
  authenticate,
  body('itemType').isIn(['auction', 'tender']).withMessage('Item type must be auction or tender'),
  body('itemId').isMongoId().withMessage('Valid item ID is required'),
  body('notes').optional().isString().isLength({ max: 500 }).withMessage('Notes must be a string with max 500 characters'),
  body('notifyOnUpdate').optional().isBoolean().withMessage('Notify on update must be a boolean'),
  body('notifyOnStatusChange').optional().isBoolean().withMessage('Notify on status change must be a boolean')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { itemType, itemId, notes, notifyOnUpdate, notifyOnStatusChange } = req.body;

    const favorite = await Favorite.addFavorite({
      userId: req.user._id,
      itemType,
      itemId,
      notes,
      notifyOnUpdate,
      notifyOnStatusChange
    });

    res.status(201).json({
      message: 'Item added to favorites',
      favorite
    });
  } catch (error) {
    console.error('Error adding to favorites:', error);
    if (error.message === 'Item already in favorites') {
      return res.status(400).json({ message: error.message });
    }
    res.status(500).json({ message: 'Server error' });
  }
});

// Remove item from favorites
router.delete('/remove', [
  authenticate,
  body('itemType').isIn(['auction', 'tender']).withMessage('Item type must be auction or tender'),
  body('itemId').isMongoId().withMessage('Valid item ID is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { itemType, itemId } = req.body;

    const result = await Favorite.removeFavorite(req.user._id, itemType, itemId);

    if (!result) {
      return res.status(404).json({ message: 'Favorite not found' });
    }

    res.json({ message: 'Item removed from favorites' });
  } catch (error) {
    console.error('Error removing from favorites:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Update favorite settings
router.put('/update/:id', [
  authenticate,
  body('notes').optional().isString().isLength({ max: 500 }).withMessage('Notes must be a string with max 500 characters'),
  body('notifyOnUpdate').optional().isBoolean().withMessage('Notify on update must be a boolean'),
  body('notifyOnStatusChange').optional().isBoolean().withMessage('Notify on status change must be a boolean')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { notes, notifyOnUpdate, notifyOnStatusChange } = req.body;

    const favorite = await Favorite.findOneAndUpdate(
      { _id: req.params.id, user: req.user._id },
      { 
        ...(notes !== undefined && { notes }),
        ...(notifyOnUpdate !== undefined && { notifyOnUpdate }),
        ...(notifyOnStatusChange !== undefined && { notifyOnStatusChange }),
        updatedAt: new Date()
      },
      { new: true }
    ).populate('itemId');

    if (!favorite) {
      return res.status(404).json({ message: 'Favorite not found' });
    }

    res.json({
      message: 'Favorite updated successfully',
      favorite
    });
  } catch (error) {
    console.error('Error updating favorite:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Check if item is in favorites
router.get('/check/:itemType/:itemId', authenticate, async (req, res) => {
  try {
    const { itemType, itemId } = req.params;

    if (!['auction', 'tender'].includes(itemType)) {
      return res.status(400).json({ message: 'Invalid item type' });
    }

    const favorite = await Favorite.findOne({
      user: req.user._id,
      itemType,
      itemId
    });

    res.json({
      isFavorite: !!favorite,
      favorite: favorite || null
    });
  } catch (error) {
    console.error('Error checking favorite status:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
