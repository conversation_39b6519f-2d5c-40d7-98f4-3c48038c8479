const express = require('express');
const { body, validationResult } = require('express-validator');
const User = require('../models/User');
const Tender = require('../models/Tender');
const { authenticate, requireRole, authorize } = require('../middleware/auth');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticate);

// @route   GET /api/government/dashboard
// @desc    Get government dashboard overview
// @access  Private (Government users)
router.get('/dashboard', requireRole('government'), async (req, res) => {
  try {
    const governmentId = req.user._id;

    // Get basic dashboard data
    const [
      totalTenders,
      activeTenders,
      completedTenders,
      totalApplications
    ] = await Promise.all([
      Tender.countDocuments({ organizer: governmentId }),
      Tender.countDocuments({ organizer: governmentId, status: 'open' }),
      Tender.countDocuments({ organizer: governmentId, status: 'closed' }),
      Tender.aggregate([
        { $match: { organizer: governmentId } },
        { $project: {
          applicationCount: {
            $cond: {
              if: { $isArray: '$proposals' },
              then: { $size: '$proposals' },
              else: 0
            }
          }
        }},
        { $group: { _id: null, total: { $sum: '$applicationCount' } } }
      ])
    ]);

    const totalApps = totalApplications.length > 0 ? totalApplications[0].total : 0;

    res.json({
      success: true,
      data: {
        overview: {
          totalTenders,
          activeTenders,
          completedTenders,
          totalApplications: totalApps
        }
      }
    });
  } catch (error) {
    console.error('Government dashboard error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching government dashboard'
    });
  }
});

// @route   GET /api/government/dashboard/stats
// @desc    Get government dashboard statistics
// @access  Private (Government users)
router.get('/dashboard/stats', requireRole('government'), async (req, res) => {
  try {
    const governmentId = req.user._id;
    
    // Get government's tenders
    const governmentTenders = await Tender.find({ organizer: governmentId });
    
    // Calculate stats
    const activeTenders = governmentTenders.filter(tender => tender.status === 'active').length;
    const completedTenders = governmentTenders.filter(tender => tender.status === 'completed').length;
    
    // Calculate total applications across all tenders
    const totalApplications = governmentTenders.reduce((total, tender) => {
      return total + (tender.proposals ? tender.proposals.length : 0);
    }, 0);

    // Calculate approved applications
    const approvedApplications = governmentTenders.reduce((total, tender) => {
      if (tender.proposals) {
        return total + tender.proposals.filter(app => app.status === 'approved').length;
      }
      return total;
    }, 0);

    // Calculate total budget
    const totalBudget = governmentTenders.reduce((total, tender) => total + tender.budget, 0);

    // Calculate pending reviews
    const pendingReviews = governmentTenders.reduce((total, tender) => {
      if (tender.proposals) {
        return total + tender.proposals.filter(app => app.status === 'pending').length;
      }
      return total;
    }, 0);
    
    // Calculate total projects (completed tenders)
    const totalProjects = completedTenders;
    
    // Calculate average tender value
    const averageTenderValue = governmentTenders.length > 0 ? totalBudget / governmentTenders.length : 0;
    
    // Calculate success rate (completed vs total)
    const successRate = governmentTenders.length > 0 ? Math.round((completedTenders / governmentTenders.length) * 100) : 0;
    
    res.json({
      success: true,
      data: {
        activeTenders,
        completedTenders,
        totalApplications,
        approvedApplications,
        totalBudget,
        pendingReviews,
        totalProjects,
        averageTenderValue,
        successRate
      }
    });
  } catch (error) {
    console.error('Government dashboard stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching government dashboard stats'
    });
  }
});

// @route   GET /api/government/tenders
// @desc    Get government's tenders with pagination
// @access  Private (Government users)
router.get('/tenders', requireRole('government'), async (req, res) => {
  try {
    const governmentId = req.user._id;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    const sort = req.query.sort || '-createdAt';
    
    const tenders = await Tender.find({ organizer: governmentId })
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .populate('proposals.proposer', 'profile.companyName profile.fullName email');
    
    const total = await Tender.countDocuments({ organizer: governmentId });
    
    res.json({
      success: true,
      data: {
        tenders,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Government tenders error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching government tenders'
    });
  }
});

// @route   GET /api/government/applications/recent
// @desc    Get recent applications on government's tenders
// @access  Private (Government users)
router.get('/applications/recent', requireRole('government'), async (req, res) => {
  try {
    const governmentId = req.user._id;
    const limit = parseInt(req.query.limit) || 10;
    
    // Get government's tenders with recent applications
    const tenders = await Tender.find({
      organizer: governmentId,
      'proposals.0': { $exists: true } // Only tenders with proposals
    })
    .populate('proposals.proposer', 'profile.companyName profile.fullName email')
    .sort({ 'proposals.submittedAt': -1 })
    .limit(limit);
    
    // Extract recent applications with tender context
    let recentApplications = [];
    tenders.forEach(tender => {
      if (tender.proposals && tender.proposals.length > 0) {
        tender.proposals.forEach(proposal => {
          recentApplications.push({
            _id: proposal._id,
            applicant: {
              name: proposal.proposer.profile.companyName || proposal.proposer.profile.fullName,
              email: proposal.proposer.email
            },
            submittedAt: proposal.submittedAt,
            status: proposal.status,
            amount: proposal.amount,
            proposal: proposal.proposal,
            tender: {
              _id: tender._id,
              title: tender.title
            }
          });
        });
      }
    });
    
    // Sort by submission date and limit
    recentApplications.sort((a, b) => new Date(b.submittedAt) - new Date(a.submittedAt));
    recentApplications = recentApplications.slice(0, limit);
    
    res.json({
      success: true,
      data: {
        applications: recentApplications
      }
    });
  } catch (error) {
    console.error('Government recent applications error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching recent applications'
    });
  }
});

// @route   GET /api/government/notifications
// @desc    Get government notifications
// @access  Private (Government users)
router.get('/notifications', requireRole('government'), async (req, res) => {
  try {
    const governmentId = req.user._id;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    
    // For now, return sample notifications
    // In a real app, you'd have a Notification model
    const sampleNotifications = [
      {
        _id: '1',
        type: 'new_application',
        title: 'طلب جديد',
        message: 'تم تقديم طلب جديد على مناقصة بناء المستشفى',
        createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
        read: false
      },
      {
        _id: '2',
        type: 'tender_deadline',
        title: 'اقتراب موعد الإغلاق',
        message: 'مناقصة تطوير البرمجيات تنتهي خلال 24 ساعة',
        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        read: false
      },
      {
        _id: '3',
        type: 'application_approved',
        title: 'تم الموافقة على طلب',
        message: 'تم الموافقة على طلب شركة البناء العربية',
        createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
        read: true
      }
    ];
    
    const notifications = sampleNotifications.slice((page - 1) * limit, page * limit);
    const total = sampleNotifications.length;
    
    res.json({
      success: true,
      data: {
        notifications,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Government notifications error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching notifications'
    });
  }
});

// @route   GET /api/government/applications/:id
// @desc    Get a specific application details
// @access  Private (Government users)
router.get('/applications/:id', requireRole('government'), async (req, res) => {
  try {
    const governmentId = req.user._id;
    const applicationId = req.params.id;

    // Find the tender that contains this application and belongs to this government
    const tender = await Tender.findOne({
      organizer: governmentId,
      'proposals._id': applicationId
    }).populate('proposals.proposer', 'profile.companyName profile.fullName email');

    if (!tender) {
      return res.status(404).json({
        success: false,
        message: 'Application not found or access denied'
      });
    }

    // Find the specific proposal/application
    const application = tender.proposals.find(p => p._id.toString() === applicationId);

    if (!application) {
      return res.status(404).json({
        success: false,
        message: 'Application not found'
      });
    }

    // Format the response
    const applicationDetails = {
      _id: application._id,
      tender: {
        _id: tender._id,
        title: tender.title,
        description: tender.description,
        budget: tender.budget,
        deadline: tender.deadline,
        category: tender.category
      },
      applicant: {
        _id: application.proposer._id,
        name: application.proposer.profile.companyName || application.proposer.profile.fullName,
        email: application.proposer.email,
        profile: application.proposer.profile
      },
      proposal: application.proposal,
      amount: application.amount,
      submittedAt: application.submittedAt,
      status: application.status,
      score: application.score || null,
      documents: application.documents || []
    };

    res.json({
      success: true,
      data: applicationDetails
    });
  } catch (error) {
    console.error('Government application details error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching application details'
    });
  }
});

// @route   PUT /api/government/applications/:id/status
// @desc    Update application status (approve/reject/review)
// @access  Private (Government users)
router.put('/applications/:id/status', requireRole('government'), async (req, res) => {
  try {
    const governmentId = req.user._id;
    const applicationId = req.params.id;
    const { status, reviewNotes, score } = req.body;

    // Validate status
    const validStatuses = ['pending', 'accepted', 'rejected'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid status. Must be one of: pending, accepted, rejected'
      });
    }

    // Find the tender that contains this application and belongs to this government
    const tender = await Tender.findOne({
      organizer: governmentId,
      'proposals._id': applicationId
    });

    if (!tender) {
      return res.status(404).json({
        success: false,
        message: 'Application not found or access denied'
      });
    }

    // Find and update the specific proposal/application
    const proposalIndex = tender.proposals.findIndex(p => p._id.toString() === applicationId);

    if (proposalIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Application not found'
      });
    }

    const currentProposal = tender.proposals[proposalIndex];
    const currentStatus = currentProposal.status;

    // Business logic validation for status transitions
    const validTransitions = {
      'pending': ['accepted', 'rejected'], // From pending, can go to accepted or rejected
      'accepted': ['pending'], // From accepted, can only go back to pending for review
      'rejected': ['pending']  // From rejected, can only go back to pending for review
    };

    if (!validTransitions[currentStatus]?.includes(status)) {
      return res.status(400).json({
        success: false,
        message: `Invalid status transition from '${currentStatus}' to '${status}'. Valid transitions from '${currentStatus}' are: ${validTransitions[currentStatus]?.join(', ') || 'none'}`
      });
    }

    // Update the proposal status and other fields
    tender.proposals[proposalIndex].status = status;

    if (reviewNotes) {
      tender.proposals[proposalIndex].reviewNotes = reviewNotes;
    }

    if (score !== undefined && score !== null) {
      tender.proposals[proposalIndex].score = score;
    }

    // Save the updated tender
    await tender.save();

    res.json({
      success: true,
      message: 'Application status updated successfully',
      data: {
        applicationId,
        status,
        reviewNotes,
        score
      }
    });
  } catch (error) {
    console.error('Government application status update error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while updating application status'
    });
  }
});

// @route   PATCH /api/government/applications/:id/status
// @desc    Update application status (approve/reject/review) - PATCH version
// @access  Private (Government users)
router.patch('/applications/:id/status', requireRole('government'), async (req, res) => {
  try {
    const governmentId = req.user._id;
    const applicationId = req.params.id;
    const { status, reviewNotes, score } = req.body;

    // Validate status
    const validStatuses = ['pending', 'accepted', 'rejected'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid status. Must be one of: pending, accepted, rejected'
      });
    }

    // Find the tender that contains this application and belongs to this government
    const tender = await Tender.findOne({
      organizer: governmentId,
      'proposals._id': applicationId
    });

    if (!tender) {
      return res.status(404).json({
        success: false,
        message: 'Application not found or access denied'
      });
    }

    // Find and update the specific proposal/application
    const proposalIndex = tender.proposals.findIndex(p => p._id.toString() === applicationId);

    if (proposalIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Application not found'
      });
    }

    const currentProposal = tender.proposals[proposalIndex];
    const currentStatus = currentProposal.status;

    // Business logic validation for status transitions
    const validTransitions = {
      'pending': ['accepted', 'rejected'], // From pending, can go to accepted or rejected
      'accepted': ['pending'], // From accepted, can only go back to pending for review
      'rejected': ['pending']  // From rejected, can only go back to pending for review
    };

    if (!validTransitions[currentStatus]?.includes(status)) {
      return res.status(400).json({
        success: false,
        message: `Invalid status transition from '${currentStatus}' to '${status}'. Valid transitions from '${currentStatus}' are: ${validTransitions[currentStatus]?.join(', ') || 'none'}`
      });
    }

    // Update the proposal status and other fields
    tender.proposals[proposalIndex].status = status;

    if (reviewNotes) {
      tender.proposals[proposalIndex].reviewNotes = reviewNotes;
    }

    if (score !== undefined && score !== null) {
      tender.proposals[proposalIndex].score = score;
    }

    // Save the updated tender
    await tender.save();

    res.json({
      success: true,
      message: 'Application status updated successfully',
      data: {
        applicationId,
        status,
        reviewNotes,
        score
      }
    });
  } catch (error) {
    console.error('Government application status update error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while updating application status'
    });
  }
});

// @route   PUT /api/government/applications/:id/score
// @desc    Score a tender application/proposal
// @access  Private (Government users)
router.put('/applications/:id/score', requireRole('government'), [
  body('technicalScore').isFloat({ min: 0, max: 100 }),
  body('financialScore').isFloat({ min: 0, max: 100 }),
  body('experienceScore').isFloat({ min: 0, max: 100 }),
  body('comments').optional().trim().isLength({ max: 1000 }),
  body('recommendation').isIn(['recommended', 'conditional', 'not_recommended'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { technicalScore, financialScore, experienceScore, comments, recommendation } = req.body;
    const proposalId = req.params.id;
    const governmentId = req.user._id;

    // Find the tender that contains this proposal
    const tender = await Tender.findOne({
      'proposals._id': proposalId,
      organizer: governmentId
    });

    if (!tender) {
      return res.status(404).json({
        success: false,
        message: 'Proposal not found or not authorized'
      });
    }

    // Find the specific proposal
    const proposal = tender.proposals.id(proposalId);
    if (!proposal) {
      return res.status(404).json({
        success: false,
        message: 'Proposal not found'
      });
    }

    // Calculate overall score (weighted average)
    const overallScore = Math.round(
      (technicalScore * 0.4) + (financialScore * 0.3) + (experienceScore * 0.3)
    );

    // Add scoring data to the proposal
    proposal.scoring = {
      technicalScore,
      financialScore,
      experienceScore,
      overallScore,
      comments,
      recommendation,
      scoredBy: governmentId,
      scoredAt: new Date()
    };

    await tender.save();

    res.json({
      success: true,
      message: 'Proposal scored successfully',
      data: {
        proposalId: proposal._id,
        scoring: proposal.scoring
      }
    });
  } catch (error) {
    console.error('Score proposal error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while scoring proposal'
    });
  }
});

// @route   GET /api/government/tenders/:id/evaluation-report
// @desc    Generate evaluation report for a tender
// @access  Private (Government users)
router.get('/tenders/:id/evaluation-report', requireRole('government'), async (req, res) => {
  try {
    const tenderId = req.params.id;
    const governmentId = req.user._id;

    const tender = await Tender.findOne({
      _id: tenderId,
      organizer: governmentId
    }).populate('proposals.proposer', 'profile.companyName profile.fullName email');

    if (!tender) {
      return res.status(404).json({
        success: false,
        message: 'Tender not found or not authorized'
      });
    }

    // Generate evaluation report
    const scoredProposals = tender.proposals.filter(p => p.scoring && p.scoring.overallScore !== undefined);
    const unscoredProposals = tender.proposals.filter(p => !p.scoring || p.scoring.overallScore === undefined);

    // Sort by overall score (highest first)
    scoredProposals.sort((a, b) => b.scoring.overallScore - a.scoring.overallScore);

    const report = {
      tender: {
        id: tender._id,
        title: tender.title,
        budget: tender.budget,
        deadline: tender.deadline,
        status: tender.status
      },
      summary: {
        totalProposals: tender.proposals.length,
        scoredProposals: scoredProposals.length,
        unscoredProposals: unscoredProposals.length,
        averageScore: scoredProposals.length > 0 ?
          Math.round(scoredProposals.reduce((sum, p) => sum + p.scoring.overallScore, 0) / scoredProposals.length) : 0,
        recommendedCount: scoredProposals.filter(p => p.scoring.recommendation === 'recommended').length
      },
      applications: scoredProposals.map(proposal => ({
        id: proposal._id,
        proposer: {
          name: proposal.proposer.profile.companyName || proposal.proposer.profile.fullName,
          email: proposal.proposer.email
        },
        amount: proposal.amount,
        scoring: proposal.scoring,
        submittedAt: proposal.submittedAt
      })),
      unscoredApplications: unscoredProposals.map(proposal => ({
        id: proposal._id,
        proposer: {
          name: proposal.proposer.profile.companyName || proposal.proposer.profile.fullName,
          email: proposal.proposer.email
        },
        amount: proposal.amount,
        submittedAt: proposal.submittedAt
      })),
      generatedAt: new Date()
    };

    res.json({
      success: true,
      data: {
        report
      }
    });
  } catch (error) {
    console.error('Generate evaluation report error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while generating evaluation report'
    });
  }
});

// @route   GET /api/government/analytics/charts
// @desc    Get government analytics data for charts
// @access  Private (Government users)
router.get('/analytics/charts', requireRole('government'), async (req, res) => {
  try {
    const governmentId = req.user._id;

    // Get current date and 6 months ago
    const now = new Date();
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(now.getMonth() - 6);

    // Get government's tenders in the last 6 months
    const governmentTenders = await Tender.find({
      organizer: governmentId,
      createdAt: { $gte: sixMonthsAgo }
    });

    // Generate budget over time data (monthly)
    const budgetOverTime = [];
    const monthNames = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];

    for (let i = 5; i >= 0; i--) {
      const monthDate = new Date();
      monthDate.setMonth(now.getMonth() - i);
      const monthStart = new Date(monthDate.getFullYear(), monthDate.getMonth(), 1);
      const monthEnd = new Date(monthDate.getFullYear(), monthDate.getMonth() + 1, 0);

      const monthTenders = governmentTenders.filter(tender => {
        const tenderDate = new Date(tender.createdAt);
        return tenderDate >= monthStart && tenderDate <= monthEnd;
      });

      const budget = monthTenders.reduce((total, tender) => total + tender.budget, 0);
      const tenders = monthTenders.length;

      budgetOverTime.push({
        month: monthNames[monthDate.getMonth()],
        budget,
        tenders
      });
    }

    // Generate tender performance data
    const statusCounts = {
      completed: governmentTenders.filter(t => t.status === 'completed').length,
      active: governmentTenders.filter(t => t.status === 'active').length,
      review: governmentTenders.filter(t => t.status === 'review').length,
      cancelled: governmentTenders.filter(t => t.status === 'cancelled').length
    };

    const tenderPerformance = [
      { status: 'مكتملة', count: statusCounts.completed, color: '#10B981' },
      { status: 'نشطة', count: statusCounts.active, color: '#059669' },
      { status: 'قيد المراجعة', count: statusCounts.review, color: '#F59E0B' },
      { status: 'ملغية', count: statusCounts.cancelled, color: '#EF4444' }
    ].filter(item => item.count > 0);

    // Generate category distribution
    const categoryDistribution = {};
    governmentTenders.forEach(tender => {
      const category = tender.category || 'أخرى';
      categoryDistribution[category] = (categoryDistribution[category] || 0) + 1;
    });

    const categoryColors = ['#059669', '#7C3AED', '#10B981', '#F59E0B', '#EF4444', '#06B6D4'];
    const categoryDistributionArray = Object.entries(categoryDistribution).map(([name, value], index) => ({
      name: getCategoryNameInArabic(name),
      value: Math.round((value / governmentTenders.length) * 100),
      color: categoryColors[index % categoryColors.length]
    }));

    // Generate application trends data (weekly)
    const applicationTrends = [];
    const dayNames = ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'];

    for (let i = 0; i < 7; i++) {
      const dayDate = new Date();
      dayDate.setDate(dayDate.getDate() - (6 - i));
      const dayStart = new Date(dayDate.getFullYear(), dayDate.getMonth(), dayDate.getDate());
      const dayEnd = new Date(dayStart.getTime() + 24 * 60 * 60 * 1000);

      let applications = 0;
      let approved = 0;

      governmentTenders.forEach(tender => {
        if (tender.proposals) {
          tender.proposals.forEach(proposal => {
            const appDate = new Date(proposal.submittedAt);
            if (appDate >= dayStart && appDate < dayEnd) {
              applications++;
              if (proposal.status === 'approved') {
                approved++;
              }
            }
          });
        }
      });

      applicationTrends.push({
        day: dayNames[dayDate.getDay()],
        applications,
        approved
      });
    }

    res.json({
      success: true,
      data: {
        budgetOverTime,
        tenderPerformance,
        categoryDistribution: categoryDistributionArray,
        applicationTrends
      }
    });
  } catch (error) {
    console.error('Government analytics charts error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching analytics data'
    });
  }
});

// Helper function to translate category names to Arabic
function getCategoryNameInArabic(category) {
  const translations = {
    'construction': 'إنشاءات',
    'it_technology': 'تقنية المعلومات',
    'consulting': 'استشارات',
    'healthcare': 'رعاية صحية',
    'education': 'تعليم',
    'transportation': 'نقل ومواصلات',
    'real_estate': 'عقارات',
    'manufacturing': 'تصنيع',
    'energy': 'طاقة',
    'other': 'أخرى'
  };
  return translations[category] || category;
}

// @route   PATCH /api/government/notifications/:id/read
// @desc    Mark notification as read
// @access  Private (Government users)
router.patch('/notifications/:id/read', requireRole('government'), async (req, res) => {
  try {
    // In a real app, you'd update the notification in the database
    res.json({
      success: true,
      message: 'Notification marked as read'
    });
  } catch (error) {
    console.error('Mark notification read error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while marking notification as read'
    });
  }
});

// @route   PATCH /api/government/notifications/mark-all-read
// @desc    Mark all notifications as read
// @access  Private (Government users)
router.patch('/notifications/mark-all-read', requireRole('government'), async (req, res) => {
  try {
    // In a real app, you'd update all notifications for the user in the database
    res.json({
      success: true,
      message: 'All notifications marked as read'
    });
  } catch (error) {
    console.error('Mark all notifications read error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while marking all notifications as read'
    });
  }
});

// @route   DELETE /api/government/notifications/:id
// @desc    Delete notification
// @access  Private (Government users)
router.delete('/notifications/:id', requireRole('government'), async (req, res) => {
  try {
    // In a real app, you'd delete the notification from the database
    res.json({
      success: true,
      message: 'Notification deleted successfully'
    });
  } catch (error) {
    console.error('Delete notification error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while deleting notification'
    });
  }
});

module.exports = router;
