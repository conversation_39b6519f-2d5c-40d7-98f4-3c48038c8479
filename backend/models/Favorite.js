const mongoose = require('mongoose');

const favoriteSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  itemType: {
    type: String,
    enum: ['auction', 'tender'],
    required: true
  },
  itemId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true
  },
  notes: {
    type: String,
    maxlength: 500
  },
  isActive: {
    type: Boolean,
    default: true
  },
  notificationPreferences: {
    priceChange: {
      type: Boolean,
      default: true
    },
    timeRemaining: {
      type: Boolean,
      default: true
    },
    statusUpdate: {
      type: <PERSON>olean,
      default: true
    }
  }
}, {
  timestamps: true
});

// Indexes
favoriteSchema.index({ user: 1, itemType: 1 });
favoriteSchema.index({ user: 1, itemId: 1, itemType: 1 }, { unique: true });
favoriteSchema.index({ itemId: 1, itemType: 1 });
favoriteSchema.index({ isActive: 1 });

// Note: Manual population is done in getUserFavorites method
// since refPath doesn't work with lowercase itemType values

// Static method to add to favorites
favoriteSchema.statics.addToFavorites = async function(userId, itemType, itemId, notes = '') {
  try {
    const favorite = await this.findOneAndUpdate(
      { user: userId, itemType, itemId },
      { 
        isActive: true,
        notes,
        $setOnInsert: {
          user: userId,
          itemType,
          itemId,
          notificationPreferences: {
            priceChange: true,
            timeRemaining: true,
            statusUpdate: true
          }
        }
      },
      { upsert: true, new: true }
    );
    return favorite;
  } catch (error) {
    throw error;
  }
};

// Static method to remove from favorites
favoriteSchema.statics.removeFromFavorites = async function(userId, itemType, itemId) {
  return await this.findOneAndUpdate(
    { user: userId, itemType, itemId },
    { isActive: false },
    { new: true }
  );
};

// Static method to add favorite (used by routes)
favoriteSchema.statics.addFavorite = async function(data) {
  const { userId, itemType, itemId, notes, notifyOnUpdate, notifyOnStatusChange } = data;

  // Check if already exists
  const existing = await this.findOne({ user: userId, itemType, itemId });
  if (existing && existing.isActive) {
    throw new Error('Item already in favorites');
  }

  if (existing) {
    // Reactivate existing favorite
    existing.isActive = true;
    existing.notes = notes || existing.notes;
    existing.notificationPreferences = {
      priceChange: notifyOnUpdate !== undefined ? notifyOnUpdate : existing.notificationPreferences.priceChange,
      timeRemaining: notifyOnStatusChange !== undefined ? notifyOnStatusChange : existing.notificationPreferences.timeRemaining,
      statusUpdate: notifyOnStatusChange !== undefined ? notifyOnStatusChange : existing.notificationPreferences.statusUpdate
    };
    return await existing.save();
  } else {
    // Create new favorite
    return await this.create({
      user: userId,
      itemType,
      itemId,
      notes: notes || '',
      isActive: true,
      notificationPreferences: {
        priceChange: notifyOnUpdate !== undefined ? notifyOnUpdate : true,
        timeRemaining: notifyOnStatusChange !== undefined ? notifyOnStatusChange : true,
        statusUpdate: notifyOnStatusChange !== undefined ? notifyOnStatusChange : true
      }
    });
  }
};

// Static method to remove favorite (used by routes)
favoriteSchema.statics.removeFavorite = async function(userId, itemType, itemId) {
  const favorite = await this.findOne({ user: userId, itemType, itemId, isActive: true });
  if (!favorite) {
    return null;
  }

  favorite.isActive = false;
  return await favorite.save();
};

// Static method to get user favorites (used by routes)
favoriteSchema.statics.getUserFavorites = async function(userId) {
  try {
    const favorites = await this.find({ user: userId, isActive: true })
      .populate('user', 'profile')
      .sort({ createdAt: -1 });

    // Manually populate items based on itemType
    const populatedFavorites = [];

    for (const favorite of favorites) {
      let populatedFavorite = favorite.toObject();

      try {
        if (favorite.itemType === 'auction') {
          const Auction = require('./Auction');
          const auction = await Auction.findById(favorite.itemId).populate('organizer', 'profile');
          populatedFavorite.item = auction;
        } else if (favorite.itemType === 'tender') {
          const Tender = require('./Tender');
          const tender = await Tender.findById(favorite.itemId).populate('organizer', 'profile');
          populatedFavorite.item = tender;
        }

        // Add type field for frontend
        populatedFavorite.type = favorite.itemType;
        populatedFavorite.addedAt = favorite.createdAt;

        populatedFavorites.push(populatedFavorite);
      } catch (itemError) {
        console.error(`Error populating ${favorite.itemType} ${favorite.itemId}:`, itemError);
        // Skip items that can't be populated (might be deleted)
      }
    }

    return populatedFavorites;
  } catch (error) {
    console.error('Error in getUserFavorites:', error);
    throw error;
  }
};

// Static method to get user's favorites
favoriteSchema.statics.getUserFavorites = function(userId, itemType = null, options = {}) {
  const query = { user: userId, isActive: true };
  if (itemType) {
    query.itemType = itemType;
  }
  
  return this.find(query)
    .populate('item')
    .sort({ createdAt: -1 })
    .limit(options.limit || 50)
    .skip(options.skip || 0);
};

// Static method to check if item is favorited
favoriteSchema.statics.isFavorited = async function(userId, itemType, itemId) {
  const favorite = await this.findOne({ 
    user: userId, 
    itemType, 
    itemId, 
    isActive: true 
  });
  return !!favorite;
};

// Static method to get favorites count for an item
favoriteSchema.statics.getFavoritesCount = function(itemType, itemId) {
  return this.countDocuments({ itemType, itemId, isActive: true });
};

module.exports = mongoose.model('Favorite', favoriteSchema);
