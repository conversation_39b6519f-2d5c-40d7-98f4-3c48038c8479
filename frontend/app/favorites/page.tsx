'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useToast } from '@/components/ui/use-toast'
import DashboardLayout from '@/components/DashboardLayout'
import { useRouter } from 'next/navigation'
import api from '@/lib/api'
import { 
  Heart, 
  HeartOff,
  Gavel, 
  FileText,
  Clock, 
  DollarSign, 
  Users, 
  Eye, 
  Calendar,
  MapPin,
  Tag,
  Search,
  Filter,
  RefreshCw,
  Trash2,
  Building
} from 'lucide-react'

interface FavoriteItem {
  _id: string
  item: {
    _id: string
    title: string
    description: string
    currentBid?: number
    budget?: number
    endTime?: string
    deadline?: string
    status: string
    category: string
    location: string
    organizer: {
      _id: string
      profile: {
        companyName?: string
        fullName?: string
        governmentEntity?: string
      }
    }
    bidsCount?: number
    applicationsCount?: number
    viewsCount: number
    createdAt: string
  }
  type: 'auction' | 'tender'
  addedAt: string
}

export default function FavoritesPage() {
  const [favorites, setFavorites] = useState<FavoriteItem[]>([])
  const [filteredFavorites, setFilteredFavorites] = useState<FavoriteItem[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [typeFilter, setTypeFilter] = useState('all')
  const [categoryFilter, setCategoryFilter] = useState('all')
  const [sortBy, setSortBy] = useState('newest')
  const [user, setUser] = useState<any>(null)
  const { toast } = useToast()
  const router = useRouter()

  useEffect(() => {
    // Load user data
    const userData = localStorage.getItem('user')
    if (userData) {
      setUser(JSON.parse(userData))
    }
    loadFavorites()
  }, [])

  useEffect(() => {
    filterFavorites()
  }, [favorites, searchTerm, typeFilter, categoryFilter, sortBy])

  const getNavigationRoutes = () => {
    if (!user) return { tenders: '/tenders-public', auctions: '/auctions' }

    switch (user.role) {
      case 'individual':
        return { tenders: '/user/tenders', auctions: '/user/auctions' }
      case 'company':
        return { tenders: '/company/tenders', auctions: '/company/auctions' }
      case 'government':
        return { tenders: '/government/tenders', auctions: '/auctions' }
      case 'admin':
      case 'super_admin':
        return { tenders: '/admin/tenders', auctions: '/admin/auctions' }
      default:
        return { tenders: '/tenders-public', auctions: '/auctions' }
    }
  }

  const loadFavorites = async () => {
    try {
      setLoading(true)
      const response = await api.get('/favorites')
      
      if (response.data.success) {
        setFavorites(response.data.data.favorites)
      } else {
        setFavorites([])
      }
    } catch (error) {
      console.error('Error loading favorites:', error)
      toast({
        title: 'خطأ في التحميل',
        description: 'حدث خطأ في تحميل المفضلة',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const filterFavorites = () => {
    let filtered = favorites

    if (searchTerm) {
      filtered = filtered.filter(fav =>
        fav.item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        fav.item.description.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (typeFilter !== 'all') {
      filtered = filtered.filter(fav => fav.type === typeFilter)
    }

    if (categoryFilter !== 'all') {
      filtered = filtered.filter(fav => fav.item.category === categoryFilter)
    }

    // Sort
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'newest':
          return new Date(b.addedAt).getTime() - new Date(a.addedAt).getTime()
        case 'oldest':
          return new Date(a.addedAt).getTime() - new Date(b.addedAt).getTime()
        case 'title':
          return a.item.title.localeCompare(b.item.title, 'ar')
        case 'ending_soon':
          const aEnd = new Date(a.item.endTime || a.item.deadline || 0)
          const bEnd = new Date(b.item.endTime || b.item.deadline || 0)
          return aEnd.getTime() - bEnd.getTime()
        default:
          return 0
      }
    })

    setFilteredFavorites(filtered)
  }

  const removeFavorite = async (favoriteId: string) => {
    try {
      const response = await api.delete(`/favorites/${favoriteId}`)
      
      if (response.data.success) {
        setFavorites(favorites.filter(fav => fav._id !== favoriteId))
        toast({
          title: 'تم الحذف',
          description: 'تم حذف العنصر من المفضلة'
        })
      }
    } catch (error) {
      toast({
        title: 'خطأ في الحذف',
        description: 'حدث خطأ في حذف العنصر من المفضلة',
        variant: 'destructive'
      })
    }
  }

  const clearAllFavorites = async () => {
    if (!confirm('هل أنت متأكد من حذف جميع العناصر من المفضلة؟')) return

    try {
      const response = await api.delete('/favorites/clear')
      
      if (response.data.success) {
        setFavorites([])
        toast({
          title: 'تم الحذف',
          description: 'تم حذف جميع العناصر من المفضلة'
        })
      }
    } catch (error) {
      toast({
        title: 'خطأ في الحذف',
        description: 'حدث خطأ في حذف العناصر',
        variant: 'destructive'
      })
    }
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 0
    }).format(price)
  }

  const formatTimeRemaining = (endTime: string) => {
    const now = new Date()
    const end = new Date(endTime)
    const diff = end.getTime() - now.getTime()
    
    if (diff <= 0) return 'انتهى'
    
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    
    if (days > 0) return `${days} يوم`
    if (hours > 0) return `${hours} ساعة`
    return 'أقل من ساعة'
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">نشط</Badge>
      case 'completed':
        return <Badge className="bg-blue-100 text-blue-800">مكتمل</Badge>
      case 'ended':
        return <Badge variant="secondary">منتهي</Badge>
      case 'cancelled':
        return <Badge variant="destructive">ملغي</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getCategoryName = (category: string) => {
    const categories = {
      'electronics': 'إلكترونيات',
      'vehicles': 'سيارات',
      'real_estate': 'عقارات',
      'construction': 'إنشاءات',
      'it_technology': 'تقنية المعلومات',
      'consulting': 'استشارات',
      'healthcare': 'رعاية صحية',
      'education': 'تعليم',
      'other': 'أخرى'
    }
    return categories[category as keyof typeof categories] || category
  }

  const FavoriteCard = ({ favorite }: { favorite: FavoriteItem }) => (
    <Card className="hover:shadow-lg transition-shadow">
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg line-clamp-2">{favorite.item.title}</CardTitle>
            <div className="flex items-center gap-2 mt-2">
              {favorite.type === 'auction' ? (
                <Gavel className="h-4 w-4 text-blue-600" />
              ) : (
                <FileText className="h-4 w-4 text-green-600" />
              )}
              <span className="text-sm text-gray-600">
                {favorite.type === 'auction' ? 'مزاد' : 'مناقصة'}
              </span>
              {getStatusBadge(favorite.item.status)}
            </div>
          </div>
          <div className="flex items-center gap-2">
            <div className="text-right">
              <div className="text-lg font-bold text-blue-600">
                {favorite.type === 'auction' 
                  ? formatPrice(favorite.item.currentBid || 0)
                  : formatPrice(favorite.item.budget || 0)
                }
              </div>
              <div className="text-sm text-gray-500">
                {favorite.type === 'auction' ? 'المزايدة الحالية' : 'الميزانية'}
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => removeFavorite(favorite._id)}
              className="text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              <HeartOff className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <p className="text-gray-600 line-clamp-2 mb-4">{favorite.item.description}</p>
        
        <div className="grid grid-cols-2 gap-4 text-sm mb-4">
          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4 text-gray-400" />
            <span>
              {favorite.type === 'auction' 
                ? formatTimeRemaining(favorite.item.endTime || '')
                : formatTimeRemaining(favorite.item.deadline || '')
              }
            </span>
          </div>
          <div className="flex items-center gap-2">
            <Users className="h-4 w-4 text-gray-400" />
            <span>
              {favorite.type === 'auction' 
                ? `${favorite.item.bidsCount || 0} مزايدة`
                : `${favorite.item.applicationsCount || 0} طلب`
              }
            </span>
          </div>
          <div className="flex items-center gap-2">
            <Eye className="h-4 w-4 text-gray-400" />
            <span>{favorite.item.viewsCount} مشاهدة</span>
          </div>
          <div className="flex items-center gap-2">
            <Tag className="h-4 w-4 text-gray-400" />
            <span>{getCategoryName(favorite.item.category)}</span>
          </div>
        </div>

        <div className="flex items-center gap-2 text-sm text-gray-500 mb-4">
          <MapPin className="h-4 w-4" />
          <span>{favorite.item.location}</span>
        </div>

        <div className="flex items-center justify-between pt-4 border-t">
          <div className="flex items-center gap-2">
            <Building className="h-4 w-4 text-gray-400" />
            <span className="text-sm text-gray-600">
              {favorite.item.organizer.profile.companyName || 
               favorite.item.organizer.profile.governmentEntity || 
               favorite.item.organizer.profile.fullName}
            </span>
          </div>
          <Button 
            onClick={() => router.push(`/${favorite.type === 'auction' ? 'auctions' : 'tenders'}/${favorite.item._id}`)}
            size="sm"
          >
            <Eye className="h-4 w-4 ml-1" />
            عرض
          </Button>
        </div>

        <div className="text-xs text-gray-500 mt-2">
          أضيف للمفضلة: {new Date(favorite.addedAt).toLocaleDateString('ar-SA')}
        </div>
      </CardContent>
    </Card>
  )

  if (loading) {
    return (
      <DashboardLayout allowedRoles={['user', 'company', 'government', 'admin']}>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">جاري تحميل المفضلة...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout allowedRoles={['user', 'company', 'government', 'admin']}>
      <div className="space-y-6">
        {/* Header */}
        <div className="bg-gradient-to-r from-red-600 to-pink-600 text-white rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">المفضلة</h1>
              <p className="text-red-100 mt-1">العناصر المحفوظة في قائمة المفضلة</p>
            </div>
            <div className="flex items-center gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold">{favorites.length}</div>
                <div className="text-sm text-red-100">عنصر محفوظ</div>
              </div>
              <div className="flex gap-2">
                <Button 
                  onClick={loadFavorites}
                  variant="outline"
                  className="bg-white/10 border-white/20 text-white hover:bg-white/20"
                >
                  <RefreshCw className="h-4 w-4 ml-2" />
                  تحديث
                </Button>
                {favorites.length > 0 && (
                  <Button 
                    onClick={clearAllFavorites}
                    variant="outline"
                    className="bg-white/10 border-white/20 text-white hover:bg-white/20"
                  >
                    <Trash2 className="h-4 w-4 ml-2" />
                    حذف الكل
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              <div className="md:col-span-2">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="البحث في المفضلة..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pr-10"
                  />
                </div>
              </div>
              
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="النوع" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الأنواع</SelectItem>
                  <SelectItem value="auction">مزادات</SelectItem>
                  <SelectItem value="tender">مناقصات</SelectItem>
                </SelectContent>
              </Select>

              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="الفئة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الفئات</SelectItem>
                  <SelectItem value="electronics">إلكترونيات</SelectItem>
                  <SelectItem value="vehicles">سيارات</SelectItem>
                  <SelectItem value="construction">إنشاءات</SelectItem>
                  <SelectItem value="it_technology">تقنية المعلومات</SelectItem>
                </SelectContent>
              </Select>

              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger>
                  <SelectValue placeholder="ترتيب" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="newest">الأحدث</SelectItem>
                  <SelectItem value="oldest">الأقدم</SelectItem>
                  <SelectItem value="title">الاسم</SelectItem>
                  <SelectItem value="ending_soon">ينتهي قريباً</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Favorites Grid */}
        {filteredFavorites.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredFavorites.map((favorite) => (
              <FavoriteCard key={favorite._id} favorite={favorite} />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <Heart className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchTerm || typeFilter !== 'all' || categoryFilter !== 'all' 
                ? 'لا توجد عناصر تطابق البحث' 
                : 'لا توجد عناصر في المفضلة'
              }
            </h3>
            <p className="text-gray-600 mb-6">
              {searchTerm || typeFilter !== 'all' || categoryFilter !== 'all'
                ? 'جرب تغيير معايير البحث أو الفلترة'
                : 'ابدأ بإضافة المزادات والمناقصات المفضلة لديك'
              }
            </p>
            {(!searchTerm && typeFilter === 'all' && categoryFilter === 'all') && (
              <div className="flex gap-4 justify-center">
                <Button
                  onClick={() => {
                    try {
                      router.push(getNavigationRoutes().tenders)
                    } catch (error) {
                      console.error('Navigation error:', error)
                      router.push('/tenders-public')
                    }
                  }}
                >
                  <FileText className="h-4 w-4 ml-2" />
                  تصفح المناقصات
                </Button>
                <Button
                  onClick={() => {
                    try {
                      router.push(getNavigationRoutes().auctions)
                    } catch (error) {
                      console.error('Navigation error:', error)
                      router.push('/auctions')
                    }
                  }}
                  variant="outline"
                >
                  <Gavel className="h-4 w-4 ml-2" />
                  تصفح المزادات
                </Button>
              </div>
            )}
          </div>
        )}
      </div>
    </DashboardLayout>
  )
}
