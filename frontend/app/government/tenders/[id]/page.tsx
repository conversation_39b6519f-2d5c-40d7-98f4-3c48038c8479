'use client'

import React, { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'

import { useToast } from '@/components/ui/use-toast'
import { 
  ArrowLeft, 
  Edit, 
  Trash2, 
  Calendar, 
  MapPin, 
  DollarSign, 
  Users, 
  FileText,
  Clock,
  CheckCircle,
  XCircle
} from 'lucide-react'
import api, { governmentAPI } from '@/lib/api'

interface Tender {
  _id: string
  title: string
  description: string
  category: string
  budget: number
  deadline: string
  requirements: string
  location: { country: string } | string
  status: string
  createdAt: string
  updatedAt: string
  proposalsCount?: number
  createdBy: {
    profile: {
      governmentEntity: string
    }
  }
}

export default function TenderDetailPage() {
  const params = useParams()
  const router = useRouter()
  const { toast } = useToast()
  const [loading, setLoading] = useState(true)
  const [tender, setTender] = useState<Tender | null>(null)
  const [deleting, setDeleting] = useState(false)

  useEffect(() => {
    loadTender()
  }, [params.id])

  const loadTender = async () => {
    try {
      setLoading(true)
      const response = await governmentAPI.tenders.getById(params.id)

      if (response.data.success) {
        setTender(response.data.data.tender)
      } else {
        toast({
          title: 'خطأ',
          description: 'لم يتم العثور على المناقصة',
          variant: 'destructive'
        })
        router.push('/government/tenders')
      }
    } catch (error) {
      console.error('Error loading tender:', error)
      toast({
        title: 'خطأ في التحميل',
        description: 'حدث خطأ في تحميل بيانات المناقصة',
        variant: 'destructive'
      })
      router.push('/government/tenders')
    } finally {
      setLoading(false)
    }
  }

  const handleEdit = () => {
    router.push(`/government/tenders/${params.id}/edit`)
  }

  const handleDelete = async () => {
    if (!confirm('هل أنت متأكد من حذف هذه المناقصة؟ لا يمكن التراجع عن هذا الإجراء.')) {
      return
    }

    try {
      setDeleting(true)
      const response = await governmentAPI.tenders.delete(params.id)

      if (response.data.success) {
        toast({
          title: 'تم الحذف',
          description: 'تم حذف المناقصة بنجاح'
        })
        router.push('/government/tenders')
      }
    } catch (error: any) {
      toast({
        title: 'خطأ في الحذف',
        description: error.response?.data?.message || 'حدث خطأ في حذف المناقصة',
        variant: 'destructive'
      })
    } finally {
      setDeleting(false)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'open':
        return <Badge className="bg-green-100 text-green-800">مفتوحة</Badge>
      case 'closed':
        return <Badge className="bg-red-100 text-red-800">مغلقة</Badge>
      case 'awarded':
        return <Badge className="bg-blue-100 text-blue-800">تم الترسية</Badge>
      case 'draft':
        return <Badge className="bg-gray-100 text-gray-800">مسودة</Badge>
      case 'cancelled':
        return <Badge className="bg-orange-100 text-orange-800">ملغية</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getCategoryName = (category: string) => {
    const categories: { [key: string]: string } = {
      construction: 'إنشاءات',
      technology: 'تكنولوجيا',
      services: 'خدمات',
      supplies: 'مستلزمات',
      consulting: 'استشارات',
      maintenance: 'صيانة',
      other: 'أخرى'
    }
    return categories[category] || category
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">جاري تحميل بيانات المناقصة...</p>
        </div>
      </div>
    )
  }

  if (!tender) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold mb-4">المناقصة غير موجودة</h2>
        <p className="text-muted-foreground mb-6">لم يتم العثور على المناقصة المطلوبة</p>
        <Button onClick={() => router.push('/government/tenders')}>
          العودة إلى المناقصات
        </Button>
      </div>
    )
  }

  return (
    <>
      <div className="space-y-6">
        <header className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              size="icon"
              onClick={() => router.push('/government/tenders')}
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <h1 className="text-3xl font-bold">تفاصيل المناقصة</h1>
              <p className="text-muted-foreground">عرض وإدارة تفاصيل المناقصة</p>
            </div>
          </div>
          <div className="flex gap-2">
            <Button onClick={handleEdit}>
              <Edit className="h-4 w-4 ml-2" />
              تعديل
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleDelete}
              disabled={deleting}
            >
              <Trash2 className="h-4 w-4 ml-2" />
              {deleting ? 'جاري الحذف...' : 'حذف'}
            </Button>
          </div>
        </header>

        {/* Main Tender Info */}
        <Card>
          <CardHeader>
            <div className="flex items-start justify-between">
              <div>
                <CardTitle className="text-2xl mb-2">{tender.title}</CardTitle>
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <span>الفئة: {getCategoryName(tender.category)}</span>
                  <span>•</span>
                  <span>تاريخ الإنشاء: {new Date(tender.createdAt).toLocaleDateString('ar')}</span>
                </div>
              </div>
              {getStatusBadge(tender.status)}
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <h3 className="font-semibold mb-2">وصف المناقصة</h3>
              <p className="text-muted-foreground leading-relaxed">{tender.description}</p>
            </div>

            <Separator />

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-100 rounded-lg">
                  <DollarSign className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">الميزانية</p>
                  <p className="font-semibold">{tender.budget.toLocaleString()} ر.س</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Calendar className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">تاريخ الانتهاء</p>
                  <p className="font-semibold">{new Date(tender.deadline).toLocaleDateString('ar')}</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <MapPin className="h-5 w-5 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">الموقع</p>
                  <p className="font-semibold">
                    {typeof tender.location === 'object' && tender.location?.country
                      ? tender.location.country
                      : tender.location || 'غير محدد'}
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <Users className="h-5 w-5 text-orange-600" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">عدد العروض</p>
                  <p className="font-semibold">{tender.proposalsCount || 0}</p>
                </div>
              </div>
            </div>

            {tender.requirements && (
              <>
                <Separator />
                <div>
                  <h3 className="font-semibold mb-2 flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    المتطلبات والشروط
                  </h3>
                  <p className="text-muted-foreground leading-relaxed whitespace-pre-wrap">
                    {tender.requirements}
                  </p>
                </div>
              </>
            )}

            <Separator />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
              <div>
                <p className="text-muted-foreground">الجهة الحكومية</p>
                <p className="font-medium">{tender.createdBy?.profile?.governmentEntity || 'غير محدد'}</p>
              </div>
              <div>
                <p className="text-muted-foreground">آخر تحديث</p>
                <p className="font-medium">{new Date(tender.updatedAt).toLocaleDateString('ar')}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Actions Card */}
        <Card>
          <CardHeader>
            <CardTitle>إجراءات سريعة</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button 
                variant="outline" 
                onClick={() => router.push(`/government/applications?tender=${tender._id}`)}
              >
                <Users className="h-4 w-4 ml-2" />
                عرض العروض المقدمة
              </Button>
              <Button 
                variant="outline"
                onClick={() => router.push(`/government/tenders/${tender._id}/edit`)}
              >
                <Edit className="h-4 w-4 ml-2" />
                تعديل المناقصة
              </Button>
              <Button 
                variant="outline"
                onClick={() => window.print()}
              >
                <FileText className="h-4 w-4 ml-2" />
                طباعة التفاصيل
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  )
}
