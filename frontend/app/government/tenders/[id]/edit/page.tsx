'use client'

import React, { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import DashboardLayout from '@/components/DashboardLayout'
import { useToast } from '@/components/ui/use-toast'
import { ArrowLeft, Save, X, AlertCircle } from 'lucide-react'
import api, { governmentAPI } from '@/lib/api'

interface TenderFormData {
  title: string
  description: string
  category: string
  budget: string
  deadline: string
  requirements: string
  location: string
  status: string
}

export default function EditTenderPage() {
  const params = useParams()
  const router = useRouter()
  const { toast } = useToast()
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [tender, setTender] = useState<any>(null)
  const [hasProposals, setHasProposals] = useState(false)
  const [formData, setFormData] = useState<TenderFormData>({
    title: '',
    description: '',
    category: '',
    budget: '',
    deadline: '',
    requirements: '',
    location: '',
    status: ''
  })

  useEffect(() => {
    loadTender()
  }, [params.id])

  const loadTender = async () => {
    try {
      setLoading(true)
      const response = await governmentAPI.tenders.getById(params.id)
      
      if (response.data.success) {
        const tenderData = response.data.data.tender
        setTender(tenderData)

        // Check if tender has proposals
        const proposalsCount = tenderData.proposals?.length || 0
        setHasProposals(proposalsCount > 0)

        // Format deadline for datetime-local input
        const deadline = new Date(tenderData.deadline)
        const formattedDeadline = deadline.toISOString().slice(0, 16)

        setFormData({
          title: tenderData.title || '',
          description: tenderData.description || '',
          category: tenderData.category || '',
          budget: tenderData.budget?.toString() || '',
          deadline: formattedDeadline,
          requirements: tenderData.requirements || '',
          location: typeof tenderData.location === 'object' && tenderData.location?.country
            ? tenderData.location.country
            : tenderData.location || '',
          status: tenderData.status || 'draft'
        })
      } else {
        toast({
          title: 'خطأ',
          description: 'لم يتم العثور على المناقصة',
          variant: 'destructive'
        })
        router.push('/government/tenders')
      }
    } catch (error) {
      console.error('Error loading tender:', error)
      toast({
        title: 'خطأ في التحميل',
        description: 'حدث خطأ في تحميل بيانات المناقصة',
        variant: 'destructive'
      })
      router.push('/government/tenders')
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: keyof TenderFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Check if tender has proposals
    if (hasProposals) {
      toast({
        title: 'لا يمكن التحديث',
        description: 'لا يمكن تحديث المناقصة بعد تلقي عروض من الشركات. هذا لضمان العدالة في المنافسة.',
        variant: 'destructive'
      })
      return
    }

    if (!formData.title || !formData.description || !formData.category || !formData.budget || !formData.deadline) {
      toast({
        title: 'بيانات ناقصة',
        description: 'يرجى ملء جميع الحقول المطلوبة',
        variant: 'destructive'
      })
      return
    }

    const budget = parseFloat(formData.budget)
    if (isNaN(budget) || budget <= 0) {
      toast({
        title: 'خطأ في الميزانية',
        description: 'يرجى إدخال ميزانية صحيحة',
        variant: 'destructive'
      })
      return
    }

    const deadline = new Date(formData.deadline)
    if (deadline <= new Date()) {
      toast({
        title: 'خطأ في التاريخ',
        description: 'يجب أن يكون تاريخ الانتهاء في المستقبل',
        variant: 'destructive'
      })
      return
    }

    try {
      setSaving(true)
      const response = await governmentAPI.tenders.update(params.id, {
        title: formData.title,
        description: formData.description,
        category: formData.category,
        budget,
        deadline: deadline.toISOString(),
        requirements: formData.requirements,
        location: formData.location,
        status: formData.status
      })
      
      if (response.data.success) {
        toast({
          title: 'تم التحديث',
          description: 'تم تحديث المناقصة بنجاح'
        })
        router.push('/government/tenders')
      }
    } catch (error: any) {
      toast({
        title: 'خطأ في التحديث',
        description: error.response?.data?.message || 'حدث خطأ في تحديث المناقصة',
        variant: 'destructive'
      })
    } finally {
      setSaving(false)
    }
  }

  const handleCancel = () => {
    router.push('/government/tenders')
  }

  if (loading) {
    return (
      <DashboardLayout allowedRoles={['government']}>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">جاري تحميل بيانات المناقصة...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout allowedRoles={['government']}>
      <div className="space-y-6">
        <header className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              size="icon"
              onClick={() => router.push('/government/tenders')}
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <h1 className="text-3xl font-bold">تعديل المناقصة</h1>
              <p className="text-muted-foreground">تحديث تفاصيل المناقصة</p>
            </div>
          </div>
        </header>

        {/* Warning Banner for Tenders with Proposals */}
        {hasProposals && (
          <Card className="border-orange-200 bg-orange-50">
            <CardContent className="pt-6">
              <div className="flex items-start gap-3">
                <AlertCircle className="h-5 w-5 text-orange-600 mt-0.5" />
                <div>
                  <h3 className="font-semibold text-orange-800 mb-1">
                    تحذير: لا يمكن تعديل هذه المناقصة
                  </h3>
                  <p className="text-orange-700 text-sm mb-3">
                    تم تلقي عروض من الشركات لهذه المناقصة. لا يمكن تعديل تفاصيل المناقصة بعد تلقي العروض لضمان العدالة في المنافسة.
                  </p>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => router.push(`/government/tenders/${params.id}`)}
                      className="border-orange-300 text-orange-700 hover:bg-orange-100"
                    >
                      عرض المناقصة
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => router.push('/government/applications')}
                      className="border-orange-300 text-orange-700 hover:bg-orange-100"
                    >
                      عرض العروض المقدمة
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        <Card>
          <CardHeader>
            <CardTitle>تفاصيل المناقصة</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="title">عنوان المناقصة *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    placeholder="أدخل عنوان المناقصة"
                    required
                    disabled={hasProposals}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="category">الفئة *</Label>
                  <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)} disabled={hasProposals}>
                    <SelectTrigger>
                      <SelectValue placeholder="اختر الفئة" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="construction">إنشاءات</SelectItem>
                      <SelectItem value="technology">تكنولوجيا</SelectItem>
                      <SelectItem value="services">خدمات</SelectItem>
                      <SelectItem value="supplies">مستلزمات</SelectItem>
                      <SelectItem value="consulting">استشارات</SelectItem>
                      <SelectItem value="maintenance">صيانة</SelectItem>
                      <SelectItem value="other">أخرى</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="budget">الميزانية (ر.س) *</Label>
                  <Input
                    id="budget"
                    type="number"
                    value={formData.budget}
                    onChange={(e) => handleInputChange('budget', e.target.value)}
                    placeholder="أدخل الميزانية"
                    min="1"
                    required
                    disabled={hasProposals}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="deadline">تاريخ الانتهاء *</Label>
                  <Input
                    id="deadline"
                    type="datetime-local"
                    value={formData.deadline}
                    onChange={(e) => handleInputChange('deadline', e.target.value)}
                    required
                    disabled={hasProposals}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="location">الموقع</Label>
                  <Input
                    id="location"
                    value={formData.location}
                    onChange={(e) => handleInputChange('location', e.target.value)}
                    placeholder="أدخل موقع المناقصة"
                    disabled={hasProposals}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="status">الحالة</Label>
                  <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)} disabled={hasProposals}>
                    <SelectTrigger>
                      <SelectValue placeholder="اختر الحالة" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="draft">مسودة</SelectItem>
                      <SelectItem value="open">مفتوحة</SelectItem>
                      <SelectItem value="closed">مغلقة</SelectItem>
                      <SelectItem value="awarded">تم الترسية</SelectItem>
                      <SelectItem value="cancelled">ملغية</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">وصف المناقصة *</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="أدخل وصف تفصيلي للمناقصة"
                  rows={4}
                  required
                  disabled={hasProposals}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="requirements">المتطلبات والشروط</Label>
                <Textarea
                  id="requirements"
                  value={formData.requirements}
                  onChange={(e) => handleInputChange('requirements', e.target.value)}
                  placeholder="أدخل المتطلبات والشروط الخاصة بالمناقصة"
                  rows={4}
                  disabled={hasProposals}
                />
              </div>

              <div className="flex gap-4 pt-4">
                <Button type="submit" disabled={saving || hasProposals}>
                  <Save className="h-4 w-4 ml-2" />
                  {saving ? 'جاري الحفظ...' : hasProposals ? 'لا يمكن التحديث' : 'حفظ التغييرات'}
                </Button>
                <Button type="button" variant="outline" onClick={handleCancel}>
                  <X className="h-4 w-4 ml-2" />
                  إلغاء
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
