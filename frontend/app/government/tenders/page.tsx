'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { useToast } from '@/components/ui/use-toast'

import { useRouter } from 'next/navigation'
import api, { governmentAPI } from '@/lib/api'
import {
  Eye,
  Edit,
  Trash2,
  PlusCircle,
  Users,
  TrendingUp,
  CheckSquare,
  Clock,
  Search,
  Filter,
  RefreshCw,
  DollarSign,
  Calendar,
  BarChart3,
  FileText
} from 'lucide-react'

interface Tender {
  _id: string
  title: string
  description: string
  budget: number
  deadline: string
  status: string
  category: string
  applicationsCount: number
  viewsCount: number
  createdAt: string
}

export default function GovernmentTendersPage() {
  const [tenders, setTenders] = useState<Tender[]>([])
  const [filteredTenders, setFilteredTenders] = useState<Tender[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [categoryFilter, setCategoryFilter] = useState('all')
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    completed: 0,
    totalBudget: 0
  })
  const { toast } = useToast()
  const router = useRouter()

  useEffect(() => {
    loadTenders()
  }, [])

  useEffect(() => {
    filterTenders()
  }, [tenders, searchTerm, statusFilter, categoryFilter])

  const loadTenders = async () => {
    try {
      setLoading(true)

      const response = await api.get('/government/tenders?limit=100')

      if (response.data.success) {
        setTenders(response.data.data.tenders)
        calculateStats(response.data.data.tenders)
      } else {
        toast({
          title: 'خطأ في التحميل',
          description: 'حدث خطأ في تحميل بيانات المناقصات',
          variant: 'destructive'
        })
      }
    } catch (error) {
      console.error('Error loading tenders:', error)
      toast({
        title: 'خطأ في التحميل',
        description: 'حدث خطأ في تحميل بيانات المناقصات',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const calculateStats = (tendersData: Tender[]) => {
    const total = tendersData.length
    const active = tendersData.filter(t => t.status === 'active').length
    const completed = tendersData.filter(t => t.status === 'completed').length
    const totalBudget = tendersData.reduce((sum, t) => sum + t.budget, 0)

    setStats({ total, active, completed, totalBudget })
  }

  const filterTenders = () => {
    let filtered = tenders

    if (searchTerm) {
      filtered = filtered.filter(tender =>
        tender.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        tender.description.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(tender => tender.status === statusFilter)
    }

    if (categoryFilter !== 'all') {
      filtered = filtered.filter(tender => tender.category === categoryFilter)
    }

    setFilteredTenders(filtered)
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 0
    }).format(price)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA')
  }

  const formatTimeRemaining = (deadline: string) => {
    const now = new Date()
    const end = new Date(deadline)
    const diff = end.getTime() - now.getTime()

    if (diff <= 0) return 'انتهى'

    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))

    if (days > 0) return `${days} يوم`
    if (hours > 0) return `${hours} ساعة`
    return 'أقل من ساعة'
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">نشط</Badge>
      case 'completed':
        return <Badge className="bg-blue-100 text-blue-800">مكتمل</Badge>
      case 'review':
        return <Badge className="bg-yellow-100 text-yellow-800">قيد المراجعة</Badge>
      case 'cancelled':
        return <Badge variant="destructive">ملغي</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const handleDeleteTender = async (tenderId: string) => {
    if (confirm('هل أنت متأكد من حذف هذه المناقصة؟')) {
      try {
        const response = await governmentAPI.tenders.delete(tenderId)

        if (response.data.success) {
          setTenders(tenders.filter(tender => tender._id !== tenderId))
          toast({
            title: 'تم الحذف',
            description: 'تم حذف المناقصة بنجاح'
          })
        }
      } catch (error) {
        toast({
          title: 'خطأ في الحذف',
          description: 'حدث خطأ في حذف المناقصة',
          variant: 'destructive'
        })
      }
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل المناقصات...</p>
        </div>
      </div>
    )
  }

  return (
    <>
      <div className="space-y-6">
        {/* Enhanced Header */}
        <header className="bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">مناقصاتي</h1>
              <p className="text-green-100 mt-1">إدارة ومتابعة جميع المناقصات الحكومية</p>
            </div>
            <div className="flex items-center gap-4">
              <Button
                onClick={loadTenders}
                variant="outline"
                className="bg-white/10 border-white/20 text-white hover:bg-white/20"
              >
                <RefreshCw className="h-4 w-4 ml-2" />
                تحديث
              </Button>
              <Button
                onClick={() => router.push('/government/create-tender')}
                className="bg-white text-green-600 hover:bg-gray-100"
              >
                <PlusCircle className="h-4 w-4 ml-2" />
                إنشاء مناقصة جديدة
              </Button>
            </div>
          </div>
        </header>

        {/* Enhanced Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-green-600">إجمالي المناقصات</p>
                  <p className="text-3xl font-bold text-green-900">{stats.total}</p>
                </div>
                <FileText className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-blue-600">المناقصات النشطة</p>
                  <p className="text-3xl font-bold text-blue-900">{stats.active}</p>
                </div>
                <TrendingUp className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-purple-600">المناقصات المكتملة</p>
                  <p className="text-3xl font-bold text-purple-900">{stats.completed}</p>
                </div>
                <CheckSquare className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-orange-600">إجمالي الميزانية</p>
                  <p className="text-3xl font-bold text-orange-900">{formatPrice(stats.totalBudget)}</p>
                </div>
                <DollarSign className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="البحث في المناقصات..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pr-10"
                  />
                </div>
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full md:w-48">
                  <SelectValue placeholder="فلترة حسب الحالة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الحالات</SelectItem>
                  <SelectItem value="active">نشط</SelectItem>
                  <SelectItem value="completed">مكتمل</SelectItem>
                  <SelectItem value="review">قيد المراجعة</SelectItem>
                  <SelectItem value="cancelled">ملغي</SelectItem>
                </SelectContent>
              </Select>
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger className="w-full md:w-48">
                  <SelectValue placeholder="فلترة حسب الفئة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الفئات</SelectItem>
                  <SelectItem value="construction">إنشاءات</SelectItem>
                  <SelectItem value="it_technology">تقنية المعلومات</SelectItem>
                  <SelectItem value="consulting">استشارات</SelectItem>
                  <SelectItem value="healthcare">رعاية صحية</SelectItem>
                  <SelectItem value="education">تعليم</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Enhanced Tenders Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-green-600" />
              قائمة المناقصات
            </CardTitle>
            <CardDescription>
              إدارة ومتابعة جميع المناقصات الحكومية ({filteredTenders.length} من {tenders.length})
            </CardDescription>
          </CardHeader>
          <CardContent>
            {filteredTenders.length > 0 ? (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[300px]">تفاصيل المناقصة</TableHead>
                      <TableHead>الميزانية</TableHead>
                      <TableHead>الحالة</TableHead>
                      <TableHead>الموعد النهائي</TableHead>
                      <TableHead>الطلبات</TableHead>
                      <TableHead>المشاهدات</TableHead>
                      <TableHead className="text-center">الإجراءات</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredTenders.map((tender) => (
                      <TableRow key={tender._id} className="hover:bg-gray-50">
                        <TableCell>
                          <div className="space-y-1">
                            <h4 className="font-medium text-gray-900">{tender.title}</h4>
                            <p className="text-sm text-gray-600 line-clamp-2">{tender.description}</p>
                            <div className="flex items-center gap-2 text-xs text-gray-500">
                              <Calendar className="h-3 w-3" />
                              تم الإنشاء: {formatDate(tender.createdAt)}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="font-medium text-gray-900">
                            {formatPrice(tender.budget)}
                          </div>
                        </TableCell>
                        <TableCell>{getStatusBadge(tender.status)}</TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="text-sm font-medium">{formatDate(tender.deadline)}</div>
                            <div className="text-xs text-gray-500">
                              <Clock className="h-3 w-3 inline ml-1" />
                              {formatTimeRemaining(tender.deadline)}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <Users className="h-4 w-4 text-blue-600" />
                            <span className="font-medium">{tender.applicationsCount || 0}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <Eye className="h-4 w-4 text-gray-600" />
                            <span className="font-medium">{tender.viewsCount || 0}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center justify-center gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => router.push(`/government/tenders/${tender._id}`)}
                            >
                              <Eye className="h-4 w-4 ml-1" />
                              عرض
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => router.push(`/government/tenders/${tender._id}/edit`)}
                            >
                              <Edit className="h-4 w-4 ml-1" />
                              تعديل
                            </Button>
                            <Button
                              variant="destructive"
                              size="sm"
                              onClick={() => handleDeleteTender(tender._id)}
                            >
                              <Trash2 className="h-4 w-4 ml-1" />
                              حذف
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className="text-center py-12">
                <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {searchTerm || statusFilter !== 'all' || categoryFilter !== 'all'
                    ? 'لا توجد مناقصات تطابق البحث'
                    : 'لا توجد مناقصات'
                  }
                </h3>
                <p className="text-gray-600 mb-6">
                  {searchTerm || statusFilter !== 'all' || categoryFilter !== 'all'
                    ? 'جرب تغيير معايير البحث أو الفلترة'
                    : 'ابدأ بإنشاء مناقصتك الأولى'
                  }
                </p>
                {(!searchTerm && statusFilter === 'all' && categoryFilter === 'all') && (
                  <Button onClick={() => router.push('/government/create-tender')}>
                    <PlusCircle className="h-4 w-4 ml-2" />
                    إنشاء مناقصة جديدة
                  </Button>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </>
  )
}

