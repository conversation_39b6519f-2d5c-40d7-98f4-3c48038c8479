'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import DashboardLayout from '@/components/DashboardLayout'
import { Calendar, FileText, DollarSign, Building } from 'lucide-react'
import api, { tenderAPI } from '@/lib/api'

export default function CreateTenderPage() {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    budget: '',
    category: '',
    startDate: '',
    submissionDeadline: '',
    executionPeriod: '',
    requirements: '',
    specifications: ''
  })

  // Category mapping from Arabic to English
  const categoryMapping: { [key: string]: string } = {
    'إنشاءات ومقاولات': 'construction',
    'توريد مواد': 'manufacturing',
    'خدمات': 'other',
    'استشارات': 'consulting',
    'صيانة': 'other',
    'تقنية معلومات': 'it_technology'
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData({ ...formData, [name]: value })
  }

  const handleSelectChange = (name: string, value: string) => {
    setFormData({ ...formData, [name]: value })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      const response = await tenderAPI.create({
        title: formData.title,
        description: formData.description,
        category: categoryMapping[formData.category] || 'other',
        budget: parseInt(formData.budget),
        startDate: formData.startDate || new Date().toISOString(),
        deadline: formData.submissionDeadline,
        duration: parseInt(formData.executionPeriod),
        requirements: formData.requirements.split('\n').filter(req => req.trim() !== ''),
        technicalSpecs: formData.specifications
      })

      console.log('Tender created:', response.data)
      alert('تم إنشاء المناقصة بنجاح!')

      // Reset form
      setFormData({
        title: '',
        description: '',
        budget: '',
        category: '',
        startDate: '',
        submissionDeadline: '',
        executionPeriod: '',
        requirements: '',
        specifications: ''
      })
    } catch (error: any) {
      console.error('Error creating tender:', error)
      const errorMessage = error.response?.data?.message || error.message || 'خطأ غير معروف'
      alert('حدث خطأ في إنشاء المناقصة: ' + errorMessage)
    }
  }

  return (
    <DashboardLayout allowedRoles={['government']}>
      <div className="space-y-6">
        <header>
          <h1 className="text-3xl font-bold">إنشاء مناقصة جديدة</h1>
          <p className="text-muted-foreground">املأ المعلومات التالية لإنشاء مناقصة حكومية جديدة</p>
        </header>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                المعلومات الأساسية
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="title">عنوان المناقصة *</Label>
                  <Input 
                    id="title" 
                    name="title" 
                    value={formData.title} 
                    onChange={handleChange} 
                    placeholder="أدخل عنوان المناقصة"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="category">فئة المناقصة *</Label>
                  <Select onValueChange={(value) => handleSelectChange('category', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="اختر فئة المناقصة" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="construction">إنشاءات ومقاولات</SelectItem>
                      <SelectItem value="supplies">توريد مواد</SelectItem>
                      <SelectItem value="services">خدمات</SelectItem>
                      <SelectItem value="consulting">استشارات</SelectItem>
                      <SelectItem value="maintenance">صيانة</SelectItem>
                      <SelectItem value="technology">تقنية معلومات</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="budget">الميزانية المتوقعة (ر.س) *</Label>
                  <Input 
                    id="budget" 
                    name="budget" 
                    type="number"
                    value={formData.budget} 
                    onChange={handleChange} 
                    placeholder="أدخل الميزانية المتوقعة"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="executionPeriod">مدة التنفيذ (بالأشهر) *</Label>
                  <Input 
                    id="executionPeriod" 
                    name="executionPeriod" 
                    type="number"
                    value={formData.executionPeriod} 
                    onChange={handleChange} 
                    placeholder="أدخل مدة التنفيذ"
                    required
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="description">وصف المناقصة *</Label>
                <textarea 
                  id="description" 
                  name="description" 
                  value={formData.description} 
                  onChange={handleChange} 
                  className="w-full h-32 p-3 border rounded-md resize-none"
                  placeholder="اكتب وصفاً مفصلاً للمناقصة والأعمال المطلوبة"
                  required
                />
              </div>
            </CardContent>
          </Card>

          {/* Timeline */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                التوقيتات المهمة
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="startDate">تاريخ بداية المناقصة *</Label>
                  <Input
                    id="startDate"
                    name="startDate"
                    type="datetime-local"
                    value={formData.startDate}
                    onChange={handleChange}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="submissionDeadline">آخر موعد لتقديم العطاءات *</Label>
                  <Input
                    id="submissionDeadline"
                    name="submissionDeadline"
                    type="datetime-local"
                    value={formData.submissionDeadline}
                    onChange={handleChange}
                    required
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Requirements and Specifications */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building className="h-5 w-5" />
                المتطلبات والمواصفات
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="requirements">متطلبات المتقدمين *</Label>
                <textarea 
                  id="requirements" 
                  name="requirements" 
                  value={formData.requirements} 
                  onChange={handleChange} 
                  className="w-full h-32 p-3 border rounded-md resize-none"
                  placeholder="اذكر المتطلبات الواجب توفرها في الشركات المتقدمة (التراخيص، الخبرة، إلخ)"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="specifications">المواصفات الفنية</Label>
                <textarea 
                  id="specifications" 
                  name="specifications" 
                  value={formData.specifications} 
                  onChange={handleChange} 
                  className="w-full h-32 p-3 border rounded-md resize-none"
                  placeholder="اذكر المواصفات الفنية التفصيلية للمشروع أو الخدمة المطلوبة"
                />
              </div>
            </CardContent>
          </Card>

          {/* Submit Button */}
          <div className="flex justify-end space-x-4 space-x-reverse">
            <Button type="button" variant="outline">
              حفظ كمسودة
            </Button>
            <Button type="submit">
              نشر المناقصة
            </Button>
          </div>
        </form>
      </div>
    </DashboardLayout>
  )
}
