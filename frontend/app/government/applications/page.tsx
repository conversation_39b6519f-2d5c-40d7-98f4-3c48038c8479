'use client'

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import { useRouter } from 'next/navigation';
import api from '@/lib/api';
import {
  Eye,
  CheckCircle,
  XCircle,
  User,
  FileText,
  Phone,
  Search,
  Filter,
  RefreshCw,
  Calendar,
  Star,
  Building,
  Mail,
  Clock,
  BarChart3,
  Users,
  TrendingUp
} from 'lucide-react';

interface Application {
  _id: string
  tender: {
    _id: string
    title: string
  }
  applicant: {
    _id: string
    profile: {
      companyName?: string
      fullName?: string
    }
    email: string
  }
  submittedAt: string
  status: string
  score?: number
  documents: string[]
}

export default function GovernmentApplicationsPage() {
  const [applications, setApplications] = useState<Application[]>([])
  const [filteredApplications, setFilteredApplications] = useState<Application[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [tenderFilter, setTenderFilter] = useState('all')
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    accepted: 0,
    rejected: 0
  })
  const { toast } = useToast()
  const router = useRouter()

  useEffect(() => {
    loadApplications()
  }, [])

  useEffect(() => {
    filterApplications()
  }, [applications, searchTerm, statusFilter, tenderFilter])

  const loadApplications = async () => {
    try {
      setLoading(true)

      const response = await api.get('/government/applications/recent?limit=100')

      if (response.data.success) {
        const applicationsData = response.data.data.applications || []
        setApplications(applicationsData)
        calculateStats(applicationsData)
      } else {
        setApplications([])
        calculateStats([])
        toast({
          title: 'خطأ في التحميل',
          description: 'حدث خطأ في تحميل بيانات الطلبات',
          variant: 'destructive'
        })
      }
    } catch (error) {
      console.error('Error loading applications:', error)
      setApplications([])
      calculateStats([])
      toast({
        title: 'خطأ في التحميل',
        description: 'حدث خطأ في تحميل بيانات الطلبات',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const calculateStats = (applicationsData: Application[]) => {
    if (!applicationsData || applicationsData.length === 0) {
      setStats({ total: 0, pending: 0, accepted: 0, rejected: 0 })
      return
    }

    const total = applicationsData.length
    const pending = applicationsData.filter(app => app.status === 'pending').length
    const accepted = applicationsData.filter(app => app.status === 'accepted').length
    const rejected = applicationsData.filter(app => app.status === 'rejected').length

    setStats({ total, pending, accepted, rejected })
  }

  const filterApplications = () => {
    if (!applications || applications.length === 0) {
      setFilteredApplications([])
      return
    }

    let filtered = applications

    if (searchTerm) {
      filtered = filtered.filter(app =>
        app.tender.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (app.applicant.profile.companyName && app.applicant.profile.companyName.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (app.applicant.profile.fullName && app.applicant.profile.fullName.toLowerCase().includes(searchTerm.toLowerCase()))
      )
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(app => app.status === statusFilter)
    }

    if (tenderFilter !== 'all') {
      filtered = filtered.filter(app => app.tender._id === tenderFilter)
    }

    setFilteredApplications(filtered)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA')
  }

  const formatRelativeTime = (dateString: string) => {
    const now = new Date()
    const date = new Date(dateString)
    const diff = now.getTime() - date.getTime()

    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))

    if (days > 0) return `منذ ${days} يوم`
    if (hours > 0) return `منذ ${hours} ساعة`
    if (minutes > 0) return `منذ ${minutes} دقيقة`
    return 'الآن'
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'accepted':
        return <Badge className="bg-green-100 text-green-800">موافق عليه</Badge>
      case 'rejected':
        return <Badge variant="destructive">مرفوض</Badge>
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800">قيد المراجعة</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getUniqueTenders = () => {
    if (!applications || applications.length === 0) return []
    const tenders = Array.from(new Set(applications.map(app => app.tender.title)))
    return tenders.map(title => {
      const app = applications.find(a => a.tender.title === title)
      return { title, id: app?.tender._id }
    })
  }

  const handleUpdateStatus = async (applicationId: string, newStatus: string) => {
    try {
      const response = await api.patch(`/government/applications/${applicationId}/status`, {
        status: newStatus
      })

      if (response.data.success) {
        setApplications(applications.map(app =>
          app._id === applicationId ? { ...app, status: newStatus } : app
        ))
        toast({
          title: 'تم التحديث',
          description: `تم تحديث حالة الطلب إلى ${newStatus === 'accepted' ? 'موافق عليه' : newStatus === 'rejected' ? 'مرفوض' : 'قيد المراجعة'}`
        })
      }
    } catch (error) {
      toast({
        title: 'خطأ في التحديث',
        description: 'حدث خطأ في تحديث حالة الطلب',
        variant: 'destructive'
      })
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل الطلبات...</p>
        </div>
      </div>
    )
  }

  return (
    <>
      <div className="space-y-6">
        {/* Enhanced Header */}
        <header className="bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">طلبات المشاركة</h1>
              <p className="text-green-100 mt-1">مراجعة وإدارة طلبات المشاركة في المناقصات</p>
            </div>
            <div className="flex items-center gap-4">
              <Button
                onClick={() => window.open('/tenders', '_blank')}
                variant="outline"
                className="bg-white/10 border-white/20 text-white hover:bg-white/20"
              >
                <Eye className="h-4 w-4 ml-2" />
                عرض المناقصات العامة
              </Button>
              <Button
                onClick={loadApplications}
                variant="outline"
                className="bg-white/10 border-white/20 text-white hover:bg-white/20"
              >
                <RefreshCw className="h-4 w-4 ml-2" />
                تحديث
              </Button>
            </div>
          </div>
        </header>

        {/* Enhanced Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-blue-600">إجمالي الطلبات</p>
                  <p className="text-3xl font-bold text-blue-900">{stats.total}</p>
                </div>
                <FileText className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-yellow-50 to-yellow-100 border-yellow-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-yellow-600">قيد المراجعة</p>
                  <p className="text-3xl font-bold text-yellow-900">{stats.pending}</p>
                </div>
                <Clock className="h-8 w-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-green-600">موافق عليها</p>
                  <p className="text-3xl font-bold text-green-900">{stats.accepted}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-red-50 to-red-100 border-red-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-red-600">مرفوضة</p>
                  <p className="text-3xl font-bold text-red-900">{stats.rejected}</p>
                </div>
                <XCircle className="h-8 w-8 text-red-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="البحث في الطلبات..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pr-10"
                  />
                </div>
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full md:w-48">
                  <SelectValue placeholder="فلترة حسب الحالة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الحالات</SelectItem>
                  <SelectItem value="pending">قيد المراجعة</SelectItem>
                  <SelectItem value="accepted">موافق عليه</SelectItem>
                  <SelectItem value="rejected">مرفوض</SelectItem>
                </SelectContent>
              </Select>
              <Select value={tenderFilter} onValueChange={setTenderFilter}>
                <SelectTrigger className="w-full md:w-64">
                  <SelectValue placeholder="فلترة حسب المناقصة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع المناقصات</SelectItem>
                  {getUniqueTenders().map(tender => (
                    <SelectItem key={tender.id || tender.title} value={tender.id || ''}>
                      {tender.title}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Enhanced Applications Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5 text-green-600" />
              قائمة الطلبات
            </CardTitle>
            <CardDescription>
              مراجعة وإدارة طلبات المشاركة في المناقصات ({(filteredApplications || []).length} من {(applications || []).length})
            </CardDescription>
          </CardHeader>
          <CardContent>
            {(filteredApplications || []).length > 0 ? (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[250px]">تفاصيل المناقصة</TableHead>
                      <TableHead className="w-[200px]">المتقدم</TableHead>
                      <TableHead>تاريخ التقديم</TableHead>
                      <TableHead>التقييم</TableHead>
                      <TableHead>الحالة</TableHead>
                      <TableHead className="text-center">الإجراءات</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {(filteredApplications || []).map((app) => (
                      <TableRow key={app._id} className="hover:bg-gray-50">
                        <TableCell>
                          <div className="space-y-1">
                            <h4 className="font-medium text-gray-900">{app.tender?.title || 'غير محدد'}</h4>
                            <div className="flex items-center gap-2 text-xs text-gray-500">
                              <FileText className="h-3 w-3" />
                              {(app.documents || []).length} مستند مرفق
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="flex items-center gap-2">
                              <Building className="h-4 w-4 text-gray-600" />
                              <span className="font-medium">
                                {app.applicant?.profile?.companyName || app.applicant?.profile?.fullName || 'غير محدد'}
                              </span>
                            </div>
                            <div className="flex items-center gap-2 text-sm text-gray-500">
                              <Mail className="h-3 w-3" />
                              {app.applicant?.email || 'غير محدد'}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="text-sm font-medium">{formatDate(app.submittedAt)}</div>
                            <div className="text-xs text-gray-500">
                              <Clock className="h-3 w-3 inline ml-1" />
                              {formatRelativeTime(app.submittedAt)}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          {app.score ? (
                            <div className="flex items-center gap-1">
                              <Star className="h-4 w-4 text-yellow-500" />
                              <span className="font-medium">{app.score}/100</span>
                            </div>
                          ) : (
                            <span className="text-gray-400">غير مقيم</span>
                          )}
                        </TableCell>
                        <TableCell>{getStatusBadge(app.status)}</TableCell>
                        <TableCell>
                          <div className="flex items-center justify-center gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => router.push(`/government/applications/${app._id}`)}
                            >
                              <Eye className="h-4 w-4 ml-1" />
                              عرض
                            </Button>
                            {app.status === 'pending' && (
                              <>
                                <Button
                                  variant="default"
                                  size="sm"
                                  className="bg-green-600 hover:bg-green-700"
                                  onClick={() => handleUpdateStatus(app._id, 'accepted')}
                                >
                                  <CheckCircle className="h-4 w-4 ml-1" />
                                  موافقة
                                </Button>
                                <Button
                                  variant="destructive"
                                  size="sm"
                                  onClick={() => handleUpdateStatus(app._id, 'rejected')}
                                >
                                  <XCircle className="h-4 w-4 ml-1" />
                                  رفض
                                </Button>
                              </>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className="text-center py-12">
                <Users className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {searchTerm || statusFilter !== 'all' || tenderFilter !== 'all'
                    ? 'لا توجد طلبات تطابق البحث'
                    : 'لا توجد طلبات مشاركة حتى الآن'
                  }
                </h3>
                <p className="text-gray-600 mb-4">
                  {searchTerm || statusFilter !== 'all' || tenderFilter !== 'all'
                    ? 'جرب تغيير معايير البحث أو الفلترة'
                    : 'ستظهر هنا طلبات المشاركة عندما تقوم الشركات بالتقديم على مناقصاتك'
                  }
                </p>
                {!searchTerm && statusFilter === 'all' && tenderFilter === 'all' && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-md mx-auto">
                    <div className="flex items-start gap-3">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-blue-600 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="text-sm text-blue-800">
                        <p className="font-medium mb-1">كيف تعمل طلبات المشاركة؟</p>
                        <ul className="text-xs space-y-1 text-blue-700">
                          <li>• الشركات تتصفح مناقصاتك المنشورة</li>
                          <li>• تقوم بتقديم طلبات المشاركة</li>
                          <li>• ستظهر هنا لمراجعتها والموافقة عليها</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </>
  )
}
