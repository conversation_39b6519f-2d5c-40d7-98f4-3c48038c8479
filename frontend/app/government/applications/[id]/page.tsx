'use client'

import React, { useState, useEffect } from 'react'
import { useP<PERSON><PERSON>, useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { useToast } from '@/components/ui/use-toast'

import api from '@/lib/api'
import { 
  ArrowLeft,
  CheckCircle,
  XCircle,
  Clock,
  User,
  Building,
  Mail,
  Phone,
  Calendar,
  FileText,
  Download,
  Star,
  MessageSquare,
  AlertCircle,
  Eye,
  Edit
} from 'lucide-react'

interface Application {
  _id: string
  tender: {
    _id: string
    title: string
    budget: number
    deadline: string
  }
  applicant: {
    _id: string
    email: string
    profile: {
      companyName?: string
      fullName?: string
      phone?: string
      address?: string
      website?: string
      description?: string
    }
  }
  submittedAt: string
  status: string
  documents: string[]
  proposedBudget?: number
  timeline?: string
  experience?: string
  notes?: string
  reviewNotes?: string
  score?: number
}

export default function ApplicationReviewPage() {
  const params = useParams()
  const router = useRouter()
  const { toast } = useToast()
  const [application, setApplication] = useState<Application | null>(null)
  const [loading, setLoading] = useState(true)
  const [updating, setUpdating] = useState(false)
  const [reviewNotes, setReviewNotes] = useState('')
  const [score, setScore] = useState('')

  useEffect(() => {
    loadApplication()
  }, [params.id])

  const loadApplication = async () => {
    try {
      setLoading(true)
      const response = await api.get(`/government/applications/${params.id}`)
      
      if (response.data.success) {
        const appData = response.data.data
        setApplication(appData)
        setReviewNotes(appData.reviewNotes || '')
        setScore(appData.score?.toString() || '')
      } else {
        toast({
          title: 'خطأ',
          description: 'لم يتم العثور على الطلب',
          variant: 'destructive'
        })
        router.push('/government/applications')
      }
    } catch (error) {
      console.error('Error loading application:', error)
      toast({
        title: 'خطأ في التحميل',
        description: 'حدث خطأ في تحميل بيانات الطلب',
        variant: 'destructive'
      })
      router.push('/government/applications')
    } finally {
      setLoading(false)
    }
  }

  const updateApplicationStatus = async (newStatus: string) => {
    try {
      setUpdating(true)
      const response = await api.patch(`/government/applications/${params.id}/status`, {
        status: newStatus,
        reviewNotes,
        score: score ? parseInt(score) : undefined
      })
      
      if (response.data.success) {
        setApplication({ ...application!, status: newStatus, reviewNotes, score: score ? parseInt(score) : undefined })
        toast({
          title: 'تم التحديث',
          description: `تم تحديث حالة الطلب إلى ${getStatusText(newStatus)}`
        })
      }
    } catch (error: any) {
      toast({
        title: 'خطأ في التحديث',
        description: error.response?.data?.message || 'حدث خطأ في تحديث الطلب',
        variant: 'destructive'
      })
    } finally {
      setUpdating(false)
    }
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 0
    }).format(price)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'accepted':
        return <Badge className="bg-green-100 text-green-800">موافق عليه</Badge>
      case 'rejected':
        return <Badge variant="destructive">مرفوض</Badge>
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800">قيد المراجعة</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'accepted':
        return 'موافق عليه'
      case 'rejected':
        return 'مرفوض'
      case 'pending':
        return 'قيد المراجعة'
      default:
        return status
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل تفاصيل الطلب...</p>
        </div>
      </div>
    )
  }

  if (!application) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="h-16 w-16 text-gray-400 mx-auto mb-4" />
        <h2 className="text-2xl font-bold text-gray-900 mb-2">الطلب غير موجود</h2>
        <p className="text-gray-600 mb-6">لم يتم العثور على الطلب المطلوب</p>
        <Button onClick={() => router.push('/government/applications')}>
          العودة للطلبات
        </Button>
      </div>
    )
  }

  return (
    <>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button 
              variant="ghost" 
              onClick={() => router.push('/government/applications')}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              العودة للطلبات
            </Button>
            <div>
              <h1 className="text-3xl font-bold">مراجعة الطلب</h1>
              <p className="text-gray-600">تفاصيل ومراجعة طلب المشاركة في المناقصة</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {getStatusBadge(application.status)}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Tender Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5 text-green-600" />
                  معلومات المناقصة
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="font-medium text-lg">{application.tender.title}</h3>
                  <div className="flex items-center gap-4 mt-2 text-sm text-gray-600">
                    <span>الميزانية: {formatPrice(application.tender.budget)}</span>
                    <span>الموعد النهائي: {formatDate(application.tender.deadline)}</span>
                  </div>
                </div>
                <Button 
                  variant="outline" 
                  onClick={() => router.push(`/tenders/${application.tender._id}`)}
                >
                  <Eye className="h-4 w-4 ml-2" />
                  عرض المناقصة
                </Button>
              </CardContent>
            </Card>

            {/* Applicant Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building className="h-5 w-5 text-blue-600" />
                  معلومات المتقدم
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium text-gray-600">اسم الشركة/المتقدم</Label>
                    <p className="font-medium">
                      {application.applicant.profile.companyName || application.applicant.profile.fullName}
                    </p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-600">البريد الإلكتروني</Label>
                    <p className="font-medium">{application.applicant.email}</p>
                  </div>
                  {application.applicant.profile.phone && (
                    <div>
                      <Label className="text-sm font-medium text-gray-600">الهاتف</Label>
                      <p className="font-medium">{application.applicant.profile.phone}</p>
                    </div>
                  )}
                  {application.applicant.profile.website && (
                    <div>
                      <Label className="text-sm font-medium text-gray-600">الموقع الإلكتروني</Label>
                      <p className="font-medium">
                        <a href={application.applicant.profile.website} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                          {application.applicant.profile.website}
                        </a>
                      </p>
                    </div>
                  )}
                </div>
                
                {application.applicant.profile.address && (
                  <div>
                    <Label className="text-sm font-medium text-gray-600">العنوان</Label>
                    <p className="font-medium">{application.applicant.profile.address}</p>
                  </div>
                )}
                
                {application.applicant.profile.description && (
                  <div>
                    <Label className="text-sm font-medium text-gray-600">وصف الشركة</Label>
                    <p className="text-gray-700">{application.applicant.profile.description}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Application Details */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5 text-purple-600" />
                  تفاصيل الطلب
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium text-gray-600">تاريخ التقديم</Label>
                    <p className="font-medium">{formatDate(application.submittedAt)}</p>
                  </div>
                  {application.proposedBudget && (
                    <div>
                      <Label className="text-sm font-medium text-gray-600">الميزانية المقترحة</Label>
                      <p className="font-medium">{formatPrice(application.proposedBudget)}</p>
                    </div>
                  )}
                  {application.timeline && (
                    <div>
                      <Label className="text-sm font-medium text-gray-600">الجدول الزمني المقترح</Label>
                      <p className="font-medium">{application.timeline}</p>
                    </div>
                  )}
                </div>
                
                {application.experience && (
                  <div>
                    <Label className="text-sm font-medium text-gray-600">الخبرة والمؤهلات</Label>
                    <p className="text-gray-700 whitespace-pre-line">{application.experience}</p>
                  </div>
                )}
                
                {application.notes && (
                  <div>
                    <Label className="text-sm font-medium text-gray-600">ملاحظات إضافية</Label>
                    <p className="text-gray-700 whitespace-pre-line">{application.notes}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Documents */}
            {application.documents && application.documents.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5 text-orange-600" />
                    المستندات المرفقة
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {application.documents.map((document, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center gap-2">
                          <FileText className="h-5 w-5 text-blue-600" />
                          <span className="font-medium">مستند {index + 1}</span>
                        </div>
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4 ml-2" />
                          تحميل
                        </Button>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar - Review Actions */}
          <div className="space-y-6">
            {/* Review Form */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Edit className="h-5 w-5" />
                  مراجعة الطلب
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="score">التقييم (من 100)</Label>
                  <input
                    id="score"
                    type="number"
                    min="0"
                    max="100"
                    value={score}
                    onChange={(e) => setScore(e.target.value)}
                    className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    placeholder="أدخل التقييم"
                  />
                </div>

                <div>
                  <Label htmlFor="reviewNotes">ملاحظات المراجعة</Label>
                  <Textarea
                    id="reviewNotes"
                    value={reviewNotes}
                    onChange={(e) => setReviewNotes(e.target.value)}
                    placeholder="أدخل ملاحظاتك حول الطلب..."
                    rows={4}
                  />
                </div>

                <Separator />

                <div className="space-y-2">
                  {/* Show approve button only if status is pending */}
                  {application?.status === 'pending' && (
                    <Button
                      onClick={() => updateApplicationStatus('accepted')}
                      disabled={updating}
                      className="w-full bg-green-600 hover:bg-green-700"
                    >
                      {updating ? (
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                      ) : (
                        <CheckCircle className="h-4 w-4 ml-2" />
                      )}
                      الموافقة على الطلب
                    </Button>
                  )}

                  {/* Show reject button only if status is pending */}
                  {application?.status === 'pending' && (
                    <Button
                      onClick={() => updateApplicationStatus('rejected')}
                      disabled={updating}
                      variant="destructive"
                      className="w-full"
                    >
                      {updating ? (
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                      ) : (
                        <XCircle className="h-4 w-4 ml-2" />
                      )}
                      رفض الطلب
                    </Button>
                  )}

                  {/* Show return to review button only if status is accepted or rejected */}
                  {(application?.status === 'accepted' || application?.status === 'rejected') && (
                    <Button
                      onClick={() => updateApplicationStatus('pending')}
                      disabled={updating}
                      variant="outline"
                      className="w-full"
                    >
                      {updating ? (
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 ml-2"></div>
                      ) : (
                        <Clock className="h-4 w-4 ml-2" />
                      )}
                      إعادة للمراجعة
                    </Button>
                  )}

                  {/* Show status message if no actions are available */}
                  {application?.status === 'accepted' && (
                    <div className="text-center p-4 bg-green-50 rounded-lg border border-green-200">
                      <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
                      <p className="text-green-800 font-medium">تم الموافقة على هذا الطلب</p>
                      <p className="text-green-600 text-sm">يمكنك إعادته للمراجعة إذا لزم الأمر</p>
                    </div>
                  )}

                  {application?.status === 'rejected' && (
                    <div className="text-center p-4 bg-red-50 rounded-lg border border-red-200">
                      <XCircle className="h-8 w-8 text-red-600 mx-auto mb-2" />
                      <p className="text-red-800 font-medium">تم رفض هذا الطلب</p>
                      <p className="text-red-600 text-sm">يمكنك إعادته للمراجعة إذا لزم الأمر</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Current Review */}
            {(application.score || application.reviewNotes) && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Star className="h-5 w-5 text-yellow-600" />
                    المراجعة الحالية
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {application.score && (
                    <div>
                      <Label className="text-sm font-medium text-gray-600">التقييم</Label>
                      <div className="flex items-center gap-2">
                        <Star className="h-4 w-4 text-yellow-500" />
                        <span className="font-medium">{application.score}/100</span>
                      </div>
                    </div>
                  )}
                  
                  {application.reviewNotes && (
                    <div>
                      <Label className="text-sm font-medium text-gray-600">ملاحظات المراجعة</Label>
                      <p className="text-gray-700 text-sm whitespace-pre-line">{application.reviewNotes}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Application Summary */}
            <Card>
              <CardHeader>
                <CardTitle>ملخص الطلب</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">الحالة:</span>
                  <span className="font-medium">{getStatusText(application.status)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">تاريخ التقديم:</span>
                  <span className="font-medium">{new Date(application.submittedAt).toLocaleDateString('ar-SA')}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">المستندات:</span>
                  <span className="font-medium">{application.documents?.length || 0} مستند</span>
                </div>
                {application.proposedBudget && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">الميزانية المقترحة:</span>
                    <span className="font-medium">{formatPrice(application.proposedBudget)}</span>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </>
  )
}
