'use client'

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
;
import api from '@/lib/api';
import {
  Building,
  Mail,
  Phone,
  MapPin,
  Globe,
  Edit,
  Save,
  X,
  Shield,
  Calendar,
  Users,
  FileText,
  Award,
  Settings
} from 'lucide-react';

interface GovernmentProfile {
  _id: string
  email: string
  profile: {
    governmentEntity: string
    department: string
    contactPerson: string
    phone: string
    address: string
    website?: string
    description?: string
    establishedYear?: number
    employeeCount?: number
    specializations: string[]
  }
  createdAt: string
  isVerified: boolean
}

export default function GovernmentProfile() {
  const [profile, setProfile] = useState<GovernmentProfile | null>(null)
  const [loading, setLoading] = useState(true)
  const [editing, setEditing] = useState(false)
  const [formData, setFormData] = useState<any>({})
  const { toast } = useToast()

  useEffect(() => {
    loadProfile()
  }, [])

  const loadProfile = async () => {
    try {
      setLoading(true)
      const response = await api.get('/users/profile')

      if (response.data.success) {
        setProfile(response.data.data.user)
        setFormData(response.data.data.user.profile || {})
      }
    } catch (error) {
      console.error('Error loading profile:', error)
      toast({
        title: 'خطأ في التحميل',
        description: 'حدث خطأ في تحميل بيانات الملف الشخصي',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const handleSave = async () => {
    try {
      const response = await api.patch('/users/profile', {
        profile: formData
      })

      if (response.data.success) {
        setProfile(response.data.data.user)
        setEditing(false)
        toast({
          title: 'تم الحفظ',
          description: 'تم تحديث الملف الشخصي بنجاح'
        })
      }
    } catch (error) {
      toast({
        title: 'خطأ في الحفظ',
        description: 'حدث خطأ في حفظ التغييرات',
        variant: 'destructive'
      })
    }
  }

  const handleCancel = () => {
    setFormData(profile?.profile || {})
    setEditing(false)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل الملف الشخصي...</p>
        </div>
      </div>
    )
  }

  return (
    <>
      <div className="space-y-6">
        {/* Enhanced Header */}
        <header className="bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="h-16 w-16 bg-white/20 rounded-full flex items-center justify-center">
                <Building className="h-8 w-8 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold">{profile?.profile.governmentEntity || 'الجهة الحكومية'}</h1>
                <p className="text-green-100 mt-1">{profile?.profile.department || 'القسم الحكومي'}</p>
                <div className="flex items-center gap-2 mt-2">
                  {profile?.isVerified ? (
                    <Badge className="bg-white/20 text-white border-white/30">
                      <Shield className="h-3 w-3 ml-1" />
                      جهة موثقة
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-300">
                      في انتظار التوثيق
                    </Badge>
                  )}
                </div>
              </div>
            </div>
            <div className="flex items-center gap-4">
              {!editing ? (
                <Button
                  onClick={() => setEditing(true)}
                  className="bg-white text-green-600 hover:bg-gray-100"
                >
                  <Edit className="h-4 w-4 ml-2" />
                  تعديل الملف الشخصي
                </Button>
              ) : (
                <div className="flex gap-2">
                  <Button
                    onClick={handleSave}
                    className="bg-white text-green-600 hover:bg-gray-100"
                  >
                    <Save className="h-4 w-4 ml-2" />
                    حفظ
                  </Button>
                  <Button
                    onClick={handleCancel}
                    variant="outline"
                    className="bg-white/10 border-white/20 text-white hover:bg-white/20"
                  >
                    <X className="h-4 w-4 ml-2" />
                    إلغاء
                  </Button>
                </div>
              )}
            </div>
          </div>
        </header>

        {/* Profile Information Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building className="h-5 w-5 text-green-600" />
                المعلومات الأساسية
              </CardTitle>
              <CardDescription>
                البيانات الأساسية للجهة الحكومية
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-600">اسم الجهة</label>
                  {editing ? (
                    <Input
                      value={formData.governmentEntity || ''}
                      onChange={(e) => setFormData({...formData, governmentEntity: e.target.value})}
                      placeholder="اسم الجهة الحكومية"
                    />
                  ) : (
                    <p className="font-medium text-gray-900">{profile?.profile.governmentEntity || 'غير محدد'}</p>
                  )}
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">القسم</label>
                  {editing ? (
                    <Input
                      value={formData.department || ''}
                      onChange={(e) => setFormData({...formData, department: e.target.value})}
                      placeholder="القسم أو الإدارة"
                    />
                  ) : (
                    <p className="font-medium text-gray-900">{profile?.profile.department || 'غير محدد'}</p>
                  )}
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">الشخص المسؤول</label>
                  {editing ? (
                    <Input
                      value={formData.contactPerson || ''}
                      onChange={(e) => setFormData({...formData, contactPerson: e.target.value})}
                      placeholder="اسم الشخص المسؤول"
                    />
                  ) : (
                    <p className="font-medium text-gray-900">{profile?.profile.contactPerson || 'غير محدد'}</p>
                  )}
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">سنة التأسيس</label>
                  {editing ? (
                    <Input
                      type="number"
                      value={formData.establishedYear || ''}
                      onChange={(e) => setFormData({...formData, establishedYear: parseInt(e.target.value)})}
                      placeholder="سنة التأسيس"
                    />
                  ) : (
                    <p className="font-medium text-gray-900">{profile?.profile.establishedYear || 'غير محدد'}</p>
                  )}
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">وصف الجهة</label>
                {editing ? (
                  <Textarea
                    value={formData.description || ''}
                    onChange={(e) => setFormData({...formData, description: e.target.value})}
                    placeholder="وصف مختصر عن الجهة ومهامها"
                    rows={3}
                  />
                ) : (
                  <p className="font-medium text-gray-900">{profile?.profile.description || 'لا يوجد وصف'}</p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Phone className="h-5 w-5 text-blue-600" />
                معلومات الاتصال
              </CardTitle>
              <CardDescription>
                بيانات التواصل مع الجهة
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-600 flex items-center gap-2">
                  <Mail className="h-4 w-4" />
                  البريد الإلكتروني
                </label>
                <p className="font-medium text-gray-900">{profile?.email}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600 flex items-center gap-2">
                  <Phone className="h-4 w-4" />
                  رقم الهاتف
                </label>
                {editing ? (
                  <Input
                    value={formData.phone || ''}
                    onChange={(e) => setFormData({...formData, phone: e.target.value})}
                    placeholder="+966 5X XXX XXXX"
                  />
                ) : (
                  <p className="font-medium text-gray-900">{profile?.profile.phone || 'غير محدد'}</p>
                )}
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600 flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  العنوان
                </label>
                {editing ? (
                  <Textarea
                    value={formData.address || ''}
                    onChange={(e) => setFormData({...formData, address: e.target.value})}
                    placeholder="العنوان الكامل للجهة"
                    rows={2}
                  />
                ) : (
                  <p className="font-medium text-gray-900">{profile?.profile.address || 'غير محدد'}</p>
                )}
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600 flex items-center gap-2">
                  <Globe className="h-4 w-4" />
                  الموقع الإلكتروني
                </label>
                {editing ? (
                  <Input
                    value={formData.website || ''}
                    onChange={(e) => setFormData({...formData, website: e.target.value})}
                    placeholder="https://example.gov.sa"
                  />
                ) : (
                  <p className="font-medium text-gray-900">
                    {profile?.profile.website ? (
                      <a href={profile.profile.website} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                        {profile.profile.website}
                      </a>
                    ) : (
                      'غير محدد'
                    )}
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Statistics and Additional Info */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5 text-purple-600" />
                تاريخ الانضمام
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold text-purple-900">
                {profile?.createdAt ? new Date(profile.createdAt).toLocaleDateString('ar-SA') : 'غير محدد'}
              </p>
              <p className="text-sm text-gray-600 mt-1">تاريخ إنشاء الحساب</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5 text-orange-600" />
                عدد الموظفين
              </CardTitle>
            </CardHeader>
            <CardContent>
              {editing ? (
                <Input
                  type="number"
                  value={formData.employeeCount || ''}
                  onChange={(e) => setFormData({...formData, employeeCount: parseInt(e.target.value)})}
                  placeholder="عدد الموظفين"
                />
              ) : (
                <>
                  <p className="text-2xl font-bold text-orange-900">
                    {profile?.profile.employeeCount || 'غير محدد'}
                  </p>
                  <p className="text-sm text-gray-600 mt-1">إجمالي الموظفين</p>
                </>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="h-5 w-5 text-green-600" />
                حالة التوثيق
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2">
                {profile?.isVerified ? (
                  <>
                    <Shield className="h-6 w-6 text-green-600" />
                    <div>
                      <p className="font-bold text-green-900">موثق</p>
                      <p className="text-sm text-gray-600">جهة حكومية موثقة</p>
                    </div>
                  </>
                ) : (
                  <>
                    <Settings className="h-6 w-6 text-yellow-600" />
                    <div>
                      <p className="font-bold text-yellow-900">قيد المراجعة</p>
                      <p className="text-sm text-gray-600">في انتظار التوثيق</p>
                    </div>
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </>
  )
}

