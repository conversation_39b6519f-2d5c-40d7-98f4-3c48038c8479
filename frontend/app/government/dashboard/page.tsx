'use client'

import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import SearchWidget from '@/components/SearchWidget';

import { useRouter } from 'next/navigation';
import api from '@/lib/api';
import {
  FileText,
  Building,
  TrendingUp,
  PlusCircle,
  DollarSign,
  Clock,
  Users,
  Eye,
  ArrowRight,
  BarChart3,
  PieChart,
  LineChart,
  Zap,
  Trophy,
  CheckCircle,
  AlertCircle,
  Briefcase
} from 'lucide-react';
import {
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart as RechartsPieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';

interface DashboardStats {
  activeTenders: number
  completedTenders: number
  totalApplications: number
  approvedApplications: number
  totalBudget: number
  pendingReviews: number
  totalProjects: number
  averageTenderValue: number
  successRate: number
}

interface RecentTender {
  _id: string
  title: string
  description: string
  budget: number
  deadline: string
  status: string
  category: string
  applicationsCount: number
  viewsCount: number
}

interface RecentApplication {
  _id: string
  applicant: {
    _id: string
    profile: {
      companyName?: string
      fullName?: string
    }
  }
  submittedAt: string
  status: string
  tender: {
    _id: string
    title: string
  }
}

interface Notification {
  _id: string
  type: string
  title: string
  message: string
  createdAt: string
  read: boolean
}

export default function GovernmentDashboard() {
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [recentTenders, setRecentTenders] = useState<RecentTender[]>([])
  const [recentApplications, setRecentApplications] = useState<RecentApplication[]>([])
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [loading, setLoading] = useState(true)
  const [user, setUser] = useState<any>(null)
  const [chartData, setChartData] = useState<any>({
    budgetOverTime: [],
    tenderPerformance: [],
    categoryDistribution: [],
    applicationTrends: []
  })
  const { toast } = useToast()
  const router = useRouter()

  // Chart colors
  const COLORS = {
    primary: '#059669',
    secondary: '#7C3AED',
    success: '#10B981',
    warning: '#F59E0B',
    danger: '#EF4444',
    info: '#06B6D4'
  }



  useEffect(() => {
    const userData = localStorage.getItem('user')
    if (userData) {
      setUser(JSON.parse(userData))
    }

    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      setLoading(true)

      // Load government stats
      const statsResponse = await api.get('/government/dashboard/stats')
      if (statsResponse.data.success) {
        setStats(statsResponse.data.data)
      }

      // Load recent tenders
      const tendersResponse = await api.get('/government/tenders?limit=5&sort=-createdAt')
      if (tendersResponse.data.success) {
        const tenders = (tendersResponse.data.data.tenders || []).filter(tender => tender && tender._id)
        setRecentTenders(tenders)
      }

      // Load recent applications
      const applicationsResponse = await api.get('/government/applications/recent?limit=5')
      if (applicationsResponse.data.success) {
        const applications = (applicationsResponse.data.data.applications || []).filter(app => app && app._id)
        setRecentApplications(applications)
      }

      // Load notifications
      const notificationsResponse = await api.get('/government/notifications?limit=5')
      if (notificationsResponse.data.success) {
        setNotifications(notificationsResponse.data.data.notifications || [])
      }

      // Load chart data
      const chartResponse = await api.get('/government/analytics/charts')
      if (chartResponse.data.success) {
        setChartData({
          budgetOverTime: chartResponse.data.data.budgetOverTime || [],
          tenderPerformance: chartResponse.data.data.tenderPerformance || [],
          categoryDistribution: chartResponse.data.data.categoryDistribution || [],
          applicationTrends: chartResponse.data.data.applicationTrends || []
        })
      }

    } catch (error) {
      console.error('Error loading dashboard data:', error)
      toast({
        title: 'خطأ في التحميل',
        description: 'حدث خطأ في تحميل بيانات لوحة التحكم',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 0
    }).format(price)
  }

  const formatTimeRemaining = (deadline: string) => {
    const now = new Date()
    const end = new Date(deadline)
    const diff = end.getTime() - now.getTime()

    if (diff <= 0) return 'انتهى'

    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))

    if (days > 0) return `${days} يوم`
    if (hours > 0) return `${hours} ساعة`
    return 'أقل من ساعة'
  }

  const formatRelativeTime = (dateString: string) => {
    const now = new Date()
    const date = new Date(dateString)
    const diff = now.getTime() - date.getTime()

    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))

    if (days > 0) return `منذ ${days} يوم`
    if (hours > 0) return `منذ ${hours} ساعة`
    if (minutes > 0) return `منذ ${minutes} دقيقة`
    return 'الآن'
  }

  const getTenderStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">نشط</Badge>
      case 'completed':
        return <Badge className="bg-blue-100 text-blue-800">مكتمل</Badge>
      case 'review':
        return <Badge className="bg-yellow-100 text-yellow-800">قيد المراجعة</Badge>
      case 'cancelled':
        return <Badge variant="destructive">ملغي</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getApplicationStatusBadge = (status: string) => {
    switch (status) {
      case 'approved':
        return <Badge className="bg-green-100 text-green-800">موافق عليه</Badge>
      case 'rejected':
        return <Badge variant="destructive">مرفوض</Badge>
      case 'review':
        return <Badge className="bg-yellow-100 text-yellow-800">قيد المراجعة</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل بيانات الجهة الحكومية...</p>
        </div>
      </div>
    )
  }

  return (
    <>
      <div className="space-y-6">
        {/* Enhanced Header */}
        <header className="bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">
                مرحباً، {user?.profile?.governmentEntity || 'الجهة الحكومية'}
              </h1>
              <p className="text-green-100 mt-2">
                إدارة المناقصات الحكومية ومتابعة المشاريع العامة
              </p>
            </div>
            <div className="hidden md:flex items-center gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold">{stats?.successRate || 0}%</div>
                <div className="text-sm text-green-100">معدل النجاح</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">{stats?.totalProjects || 0}</div>
                <div className="text-sm text-green-100">إجمالي المشاريع</div>
              </div>
            </div>
          </div>
        </header>

        {/* Search Widget */}
        <SearchWidget />

        {/* Enhanced Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Button
            className="h-16 text-lg bg-green-600 hover:bg-green-700"
            size="lg"
            onClick={() => router.push('/government/create-tender')}
          >
            <PlusCircle className="h-6 w-6 ml-2" />
            إنشاء مناقصة جديدة
          </Button>
          <Button
            variant="outline"
            className="h-16 text-lg border-blue-200 text-blue-700 hover:bg-blue-50"
            size="lg"
            onClick={() => router.push('/government/tenders')}
          >
            <FileText className="h-6 w-6 ml-2" />
            إدارة المناقصات
          </Button>
          <Button
            variant="outline"
            className="h-16 text-lg border-purple-200 text-purple-700 hover:bg-purple-50"
            size="lg"
            onClick={() => router.push('/government/applications')}
          >
            <Briefcase className="h-6 w-6 ml-2" />
            مراجعة الطلبات
          </Button>
          <Button
            variant="outline"
            className="h-16 text-lg border-orange-200 text-orange-700 hover:bg-orange-50"
            size="lg"
            onClick={() => router.push('/government/profile')}
          >
            <Building className="h-6 w-6 ml-2" />
            الملف الشخصي
          </Button>
        </div>

        {/* Enhanced Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-green-600">المناقصات النشطة</p>
                  <p className="text-3xl font-bold text-green-900">{stats?.activeTenders || 0}</p>
                  <p className="text-xs text-green-600 mt-1">
                    <Zap className="h-3 w-3 inline ml-1" />
                    نشطة الآن
                  </p>
                </div>
                <div className="h-12 w-12 bg-green-200 rounded-full flex items-center justify-center">
                  <FileText className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-blue-600">المناقصات المكتملة</p>
                  <p className="text-3xl font-bold text-blue-900">{stats?.completedTenders || 0}</p>
                  <p className="text-xs text-blue-600 mt-1">
                    <CheckCircle className="h-3 w-3 inline ml-1" />
                    مكتملة
                  </p>
                </div>
                <div className="h-12 w-12 bg-blue-200 rounded-full flex items-center justify-center">
                  <Trophy className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-purple-600">إجمالي الميزانية</p>
                  <p className="text-3xl font-bold text-purple-900">{formatPrice(stats?.totalBudget || 0)}</p>
                  <p className="text-xs text-purple-600 mt-1">
                    <TrendingUp className="h-3 w-3 inline ml-1" />
                    استثمار عام
                  </p>
                </div>
                <div className="h-12 w-12 bg-purple-200 rounded-full flex items-center justify-center">
                  <DollarSign className="h-6 w-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-orange-600">الطلبات المعلقة</p>
                  <p className="text-3xl font-bold text-orange-900">{stats?.pendingReviews || 0}</p>
                  <p className="text-xs text-orange-600 mt-1">
                    <AlertCircle className="h-3 w-3 inline ml-1" />
                    تحتاج مراجعة
                  </p>
                </div>
                <div className="h-12 w-12 bg-orange-200 rounded-full flex items-center justify-center">
                  <Clock className="h-6 w-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Analytics Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Budget Over Time Chart */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <LineChart className="h-5 w-5 text-green-600" />
                الميزانية الشهرية
              </CardTitle>
              <CardDescription>
                تطور الميزانية وعدد المناقصات خلال الأشهر الماضية
              </CardDescription>
            </CardHeader>
            <CardContent>
              {chartData.budgetOverTime && chartData.budgetOverTime.length > 0 ? (
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={chartData.budgetOverTime}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip formatter={(value: any, name: string) => [
                      name === 'budget' ? formatPrice(value) : value,
                      name === 'budget' ? 'الميزانية' : 'المناقصات'
                    ]} />
                    <Legend />
                    <Area
                      type="monotone"
                      dataKey="budget"
                      stackId="1"
                      stroke={COLORS.primary}
                      fill={COLORS.primary}
                      fillOpacity={0.6}
                      name="الميزانية"
                    />
                    <Area
                      type="monotone"
                      dataKey="tenders"
                      stackId="2"
                      stroke={COLORS.success}
                      fill={COLORS.success}
                      fillOpacity={0.8}
                      name="المناقصات"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              ) : (
                <div className="flex items-center justify-center h-[300px] text-gray-500">
                  <div className="text-center">
                    <BarChart3 className="h-12 w-12 mx-auto mb-2 text-gray-400" />
                    <p>لا توجد بيانات للعرض</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Tender Performance Chart */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <PieChart className="h-5 w-5 text-purple-600" />
                أداء المناقصات
              </CardTitle>
              <CardDescription>
                توزيع المناقصات حسب الحالة
              </CardDescription>
            </CardHeader>
            <CardContent>
              {chartData.tenderPerformance && chartData.tenderPerformance.length > 0 ? (
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsPieChart>
                    <Pie
                      data={chartData.tenderPerformance}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ status, percent }) => `${status} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="count"
                    >
                      {chartData.tenderPerformance.map((entry: any, index: number) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </RechartsPieChart>
                </ResponsiveContainer>
              ) : (
                <div className="flex items-center justify-center h-[300px] text-gray-500">
                  <div className="text-center">
                    <PieChart className="h-12 w-12 mx-auto mb-2 text-gray-400" />
                    <p>لا توجد بيانات للعرض</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Additional Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Category Distribution Chart */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-blue-600" />
                توزيع الفئات
              </CardTitle>
              <CardDescription>
                توزيع المناقصات حسب الفئات المختلفة
              </CardDescription>
            </CardHeader>
            <CardContent>
              {chartData.categoryDistribution && chartData.categoryDistribution.length > 0 ? (
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsPieChart>
                    <Pie
                      data={chartData.categoryDistribution}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={100}
                      paddingAngle={5}
                      dataKey="value"
                    >
                      {chartData.categoryDistribution.map((entry: any, index: number) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value: any) => `${value}%`} />
                    <Legend />
                  </RechartsPieChart>
                </ResponsiveContainer>
              ) : (
                <div className="flex items-center justify-center h-[300px] text-gray-500">
                  <div className="text-center">
                    <BarChart3 className="h-12 w-12 mx-auto mb-2 text-gray-400" />
                    <p>لا توجد بيانات للعرض</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Application Trends Chart */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5 text-orange-600" />
                اتجاه الطلبات
              </CardTitle>
              <CardDescription>
                الطلبات المقدمة والموافق عليها خلال الأسبوع
              </CardDescription>
            </CardHeader>
            <CardContent>
              {chartData.applicationTrends && chartData.applicationTrends.length > 0 ? (
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={chartData.applicationTrends}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="day" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="applications" fill={COLORS.primary} name="الطلبات" />
                    <Bar dataKey="approved" fill={COLORS.success} name="الموافق عليها" />
                  </BarChart>
                </ResponsiveContainer>
              ) : (
                <div className="flex items-center justify-center h-[300px] text-gray-500">
                  <div className="text-center">
                    <Users className="h-12 w-12 mx-auto mb-2 text-gray-400" />
                    <p>لا توجد بيانات للعرض</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Tenders */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <FileText className="h-5 w-5 text-green-600" />
                  المناقصات الحديثة
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => router.push('/government/tenders')}
                >
                  عرض الكل
                  <ArrowRight className="h-4 w-4 mr-1" />
                </Button>
              </CardTitle>
              <CardDescription>
                آخر المناقصات التي تم إنشاؤها
              </CardDescription>
            </CardHeader>
            <CardContent>
              {recentTenders.length > 0 ? (
                <div className="space-y-3">
                  {recentTenders.slice(0, 3).filter(tender => tender && tender._id).map((tender) => (
                    <div
                      key={tender._id}
                      className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
                      onClick={() => router.push(`/tenders/${tender._id}`)}
                    >
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900">{tender.title}</h4>
                        <div className="flex items-center gap-4 mt-1">
                          <p className="text-sm text-gray-600">
                            <DollarSign className="h-4 w-4 inline ml-1" />
                            {formatPrice(tender.budget)}
                          </p>
                          <p className="text-sm text-gray-600">
                            <Users className="h-4 w-4 inline ml-1" />
                            {tender.applicationsCount} طلب
                          </p>
                          <p className="text-sm text-gray-600">
                            <Eye className="h-4 w-4 inline ml-1" />
                            {tender.viewsCount} مشاهدة
                          </p>
                        </div>
                      </div>
                      <div className="flex flex-col items-end gap-2">
                        {getTenderStatusBadge(tender.status)}
                        <p className="text-xs text-gray-500">
                          <Clock className="h-3 w-3 inline ml-1" />
                          {formatTimeRemaining(tender.deadline)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد مناقصات حديثة</h3>
                  <p className="text-gray-600 mb-4">ابدأ بإنشاء مناقصتك الأولى</p>
                  <Button onClick={() => router.push('/government/create-tender')}>
                    إنشاء مناقصة جديدة
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Recent Applications */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Briefcase className="h-5 w-5 text-blue-600" />
                  الطلبات الحديثة
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => router.push('/government/applications')}
                >
                  عرض الكل
                  <ArrowRight className="h-4 w-4 mr-1" />
                </Button>
              </CardTitle>
              <CardDescription>
                آخر الطلبات المقدمة على المناقصات
              </CardDescription>
            </CardHeader>
            <CardContent>
              {recentApplications.length > 0 ? (
                <div className="space-y-3">
                  {recentApplications.slice(0, 3).filter(application => application && application._id).map((application) => (
                    <div
                      key={application._id}
                      className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                    >
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900">{application.tender.title}</h4>
                        <div className="flex items-center gap-4 mt-1">
                          <p className="text-sm text-gray-600">
                            <Users className="h-4 w-4 inline ml-1" />
                            {application.applicant?.profile?.companyName || application.applicant?.profile?.fullName || 'غير محدد'}
                          </p>
                        </div>
                        <p className="text-xs text-gray-500 mt-1">
                          {formatRelativeTime(application.submittedAt)}
                        </p>
                      </div>
                      <div className="flex flex-col items-end gap-2">
                        {getApplicationStatusBadge(application.status)}
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => router.push(`/government/applications/${application._id}`)}
                        >
                          <Eye className="h-4 w-4 ml-1" />
                          مراجعة
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Briefcase className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد طلبات حديثة</h3>
                  <p className="text-gray-600">ستظهر هنا الطلبات المقدمة على مناقصاتك</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </>
  )
}

