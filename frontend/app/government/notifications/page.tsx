'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { useToast } from '@/components/ui/use-toast'

import { useRouter } from 'next/navigation'
import api from '@/lib/api'
import { 
  Bell, 
  BellRing,
  Check,
  CheckCheck,
  Trash2,
  RefreshCw,
  Calendar,
  Clock,
  AlertCircle,
  Info,
  CheckCircle,
  XCircle,
  FileText,
  Users,
  Building,
  Send,
  Eye
} from 'lucide-react'

interface Notification {
  _id: string
  type: string
  title: string
  message: string
  data?: any
  read: boolean
  createdAt: string
  priority: 'low' | 'medium' | 'high'
  category: string
}

export default function GovernmentNotificationsPage() {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('all')
  const { toast } = useToast()
  const router = useRouter()

  useEffect(() => {
    loadNotifications()
  }, [])

  const loadNotifications = async () => {
    try {
      setLoading(true)
      const response = await api.get('/government/notifications')
      
      if (response.data.success) {
        setNotifications(response.data.data.notifications)
      } else {
        // Fallback to sample data
        setNotifications([
          {
            _id: '1',
            type: 'new_application',
            title: 'طلب جديد على مناقصة',
            message: 'تم تقديم طلب جديد على مناقصة بناء المستشفى من شركة البناء المتقدمة',
            read: false,
            createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
            priority: 'high',
            category: 'tender',
            data: { tenderId: 'tender123', applicantName: 'شركة البناء المتقدمة' }
          },
          {
            _id: '2',
            type: 'tender_deadline_approaching',
            title: 'اقتراب موعد انتهاء مناقصة',
            message: 'مناقصة تطوير نظام إدارة المدارس تنتهي خلال 24 ساعة',
            read: false,
            createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            priority: 'medium',
            category: 'tender',
            data: { tenderId: 'tender456' }
          },
          {
            _id: '3',
            type: 'application_review_required',
            title: 'مراجعة طلب مطلوبة',
            message: 'يتطلب طلب شركة التقنية الحديثة مراجعة عاجلة لمناقصة الأنظمة الذكية',
            read: true,
            createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
            priority: 'high',
            category: 'application',
            data: { applicationId: 'app789', tenderId: 'tender789' }
          },
          {
            _id: '4',
            type: 'tender_published',
            title: 'تم نشر مناقصة جديدة',
            message: 'تم نشر مناقصة تطوير البنية التحتية للنقل بنجاح',
            read: true,
            createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
            priority: 'low',
            category: 'tender',
            data: { tenderId: 'tender101' }
          },
          {
            _id: '5',
            type: 'system_update',
            title: 'تحديث النظام',
            message: 'تم تحديث نظام إدارة المناقصات بميزات جديدة لتحسين الأداء',
            read: true,
            createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
            priority: 'low',
            category: 'system',
            data: {}
          }
        ])
      }
    } catch (error) {
      console.error('Error loading notifications:', error)
      toast({
        title: 'خطأ في التحميل',
        description: 'حدث خطأ في تحميل الإشعارات',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const markAsRead = async (notificationId: string) => {
    try {
      await api.patch(`/government/notifications/${notificationId}/read`)
      setNotifications(notifications.map(notif => 
        notif._id === notificationId ? { ...notif, read: true } : notif
      ))
    } catch (error) {
      toast({
        title: 'خطأ',
        description: 'حدث خطأ في تحديث الإشعار',
        variant: 'destructive'
      })
    }
  }

  const markAllAsRead = async () => {
    try {
      await api.patch('/government/notifications/mark-all-read')
      setNotifications(notifications.map(notif => ({ ...notif, read: true })))
      toast({
        title: 'تم التحديث',
        description: 'تم تحديد جميع الإشعارات كمقروءة'
      })
    } catch (error) {
      toast({
        title: 'خطأ',
        description: 'حدث خطأ في تحديث الإشعارات',
        variant: 'destructive'
      })
    }
  }

  const deleteNotification = async (notificationId: string) => {
    try {
      await api.delete(`/government/notifications/${notificationId}`)
      setNotifications(notifications.filter(notif => notif._id !== notificationId))
      toast({
        title: 'تم الحذف',
        description: 'تم حذف الإشعار بنجاح'
      })
    } catch (error) {
      toast({
        title: 'خطأ',
        description: 'حدث خطأ في حذف الإشعار',
        variant: 'destructive'
      })
    }
  }

  const handleNotificationClick = (notification: Notification) => {
    if (!notification.read) {
      markAsRead(notification._id)
    }

    // Navigate based on notification type
    if (notification.data) {
      switch (notification.type) {
        case 'new_application':
        case 'application_review_required':
          if (notification.data.applicationId) {
            router.push(`/government/applications/${notification.data.applicationId}`)
          } else {
            router.push('/government/applications')
          }
          break
        case 'tender_deadline_approaching':
        case 'tender_published':
          router.push(`/tenders/${notification.data.tenderId}`)
          break
        default:
          break
      }
    }
  }

  const formatRelativeTime = (dateString: string) => {
    const now = new Date()
    const date = new Date(dateString)
    const diff = now.getTime() - date.getTime()
    
    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    
    if (days > 0) return `منذ ${days} يوم`
    if (hours > 0) return `منذ ${hours} ساعة`
    if (minutes > 0) return `منذ ${minutes} دقيقة`
    return 'الآن'
  }

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'new_application':
        return <Send className="h-5 w-5 text-blue-600" />
      case 'tender_deadline_approaching':
        return <Clock className="h-5 w-5 text-orange-600" />
      case 'application_review_required':
        return <Eye className="h-5 w-5 text-red-600" />
      case 'tender_published':
        return <FileText className="h-5 w-5 text-green-600" />
      case 'system_update':
        return <Building className="h-5 w-5 text-purple-600" />
      default:
        return <Bell className="h-5 w-5 text-gray-600" />
    }
  }

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'high':
        return <Badge variant="destructive">عالية</Badge>
      case 'medium':
        return <Badge className="bg-yellow-100 text-yellow-800">متوسطة</Badge>
      case 'low':
        return <Badge variant="secondary">منخفضة</Badge>
      default:
        return null
    }
  }

  const filteredNotifications = notifications.filter(notif => {
    if (activeTab === 'all') return true
    if (activeTab === 'unread') return !notif.read
    if (activeTab === 'read') return notif.read
    return notif.category === activeTab
  })

  const unreadCount = notifications.filter(notif => !notif.read).length

  return (
    <>
      <div className="space-y-6">
        {/* Header */}
        <div className="bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">إشعارات الجهة الحكومية</h1>
              <p className="text-green-100 mt-1">
                متابعة جميع التحديثات المتعلقة بمناقصاتك وطلبات المشاركة
              </p>
            </div>
            <div className="flex items-center gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold">{unreadCount}</div>
                <div className="text-sm text-green-100">غير مقروءة</div>
              </div>
              <div className="flex gap-2">
                <Button 
                  onClick={loadNotifications}
                  variant="outline"
                  className="bg-white/10 border-white/20 text-white hover:bg-white/20"
                >
                  <RefreshCw className="h-4 w-4 ml-2" />
                  تحديث
                </Button>
                {unreadCount > 0 && (
                  <Button 
                    onClick={markAllAsRead}
                    className="bg-white text-green-600 hover:bg-gray-100"
                  >
                    <CheckCheck className="h-4 w-4 ml-2" />
                    تحديد الكل كمقروء
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Tabs and Notifications */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="all">الكل ({notifications.length})</TabsTrigger>
            <TabsTrigger value="unread">غير مقروءة ({unreadCount})</TabsTrigger>
            <TabsTrigger value="tender">مناقصات</TabsTrigger>
            <TabsTrigger value="application">طلبات</TabsTrigger>
            <TabsTrigger value="system">النظام</TabsTrigger>
          </TabsList>

          <TabsContent value={activeTab} className="mt-6">
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <div className="text-center">
                  <RefreshCw className="h-8 w-8 animate-spin text-green-600 mx-auto mb-4" />
                  <p className="text-gray-600">جاري تحميل الإشعارات...</p>
                </div>
              </div>
            ) : filteredNotifications.length > 0 ? (
              <div className="space-y-4">
                {filteredNotifications.map((notification) => (
                  <Card 
                    key={notification._id} 
                    className={`transition-all hover:shadow-md cursor-pointer ${
                      !notification.read ? 'border-l-4 border-l-green-500 bg-green-50/50' : ''
                    }`}
                    onClick={() => handleNotificationClick(notification)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex items-start gap-3 flex-1">
                          <div className="mt-1">
                            {getNotificationIcon(notification.type)}
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <h3 className={`font-medium ${!notification.read ? 'font-semibold' : ''}`}>
                                {notification.title}
                              </h3>
                              {!notification.read && (
                                <div className="h-2 w-2 bg-green-600 rounded-full"></div>
                              )}
                              {getPriorityBadge(notification.priority)}
                            </div>
                            <p className="text-gray-600 text-sm mb-2">
                              {notification.message}
                            </p>
                            <div className="flex items-center gap-4 text-xs text-gray-500">
                              <div className="flex items-center gap-1">
                                <Calendar className="h-3 w-3" />
                                {formatRelativeTime(notification.createdAt)}
                              </div>
                              {notification.data?.applicantName && (
                                <div className="flex items-center gap-1">
                                  <Users className="h-3 w-3" />
                                  {notification.data.applicantName}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {!notification.read && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation()
                                markAsRead(notification._id)
                              }}
                            >
                              <Check className="h-4 w-4" />
                            </Button>
                          )}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              deleteNotification(notification._id)
                            }}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <Bell className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد إشعارات</h3>
                <p className="text-gray-600">
                  {activeTab === 'unread' 
                    ? 'جميع الإشعارات مقروءة' 
                    : 'ستظهر هنا الإشعارات المتعلقة بأنشطة جهتكم الحكومية'
                  }
                </p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </>
  )
}
