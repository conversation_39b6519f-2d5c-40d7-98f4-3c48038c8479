'use client'

import React, { useState, useEffect } from 'react'
import { useP<PERSON><PERSON>, useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { useToast } from '@/components/ui/use-toast'
import DashboardLayout from '@/components/DashboardLayout'
import api, { favoritesAPI } from '@/lib/api'
import { 
  FileText, 
  Clock, 
  DollarSign, 
  Users, 
  Eye, 
  Calendar,
  MapPin,
  Tag,
  Building,
  Edit,
  AlertCircle,
  CheckCircle,
  Send,
  Download,
  Share2,
  Heart,
  ArrowLeft
} from 'lucide-react'

interface Tender {
  _id: string
  title: string
  description: string
  budget: number
  deadline: string
  status: string
  category: string
  location: string | { country: string }
  requirements: string[]
  documents: string[]
  organizer: {
    _id: string
    profile: {
      governmentEntity?: string
      fullName?: string
      phone?: string
      email?: string
    }
  }
  applications: Array<{
    _id: string
    applicant: {
      _id: string
      profile: {
        companyName?: string
        fullName?: string
      }
    }
    submittedAt: string
    status: string
  }>
  viewsCount: number
  createdAt: string
}

export default function TenderDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const { toast } = useToast()
  const [tender, setTender] = useState<Tender | null>(null)
  const [loading, setLoading] = useState(true)
  const [applying, setApplying] = useState(false)
  const [user, setUser] = useState<any>(null)
  const [hasApplied, setHasApplied] = useState(false)
  const [isFavorite, setIsFavorite] = useState(false)
  const [favoriteLoading, setFavoriteLoading] = useState(false)

  useEffect(() => {
    const userData = localStorage.getItem('user')
    if (userData) {
      setUser(JSON.parse(userData))
    }
    loadTender()
    checkFavoriteStatus()
  }, [params.id])

  const loadTender = async () => {
    try {
      setLoading(true)
      const response = await api.get(`/tenders/${params.id}`)
      
      if (response.data.success) {
        setTender(response.data.data.tender)
        // Check if user has already applied
        if (user && response.data.data.tender.applications) {
          const userApplication = response.data.data.tender.applications.find(
            (app: any) => app.applicant && app.applicant._id === user._id
          )
          setHasApplied(!!userApplication)
        }
      } else {
        toast({
          title: 'خطأ',
          description: 'لم يتم العثور على المناقصة',
          variant: 'destructive'
        })
        router.push('/tenders')
      }
    } catch (error) {
      console.error('Error loading tender:', error)
      toast({
        title: 'خطأ في التحميل',
        description: 'حدث خطأ في تحميل بيانات المناقصة',
        variant: 'destructive'
      })
      router.push('/tenders')
    } finally {
      setLoading(false)
    }
  }

  const checkFavoriteStatus = async () => {
    try {
      if (!user) return

      const response = await api.get(`/favorites/check/tender/${params.id}`)
      setIsFavorite(response.data.isFavorite)
    } catch (error) {
      console.error('Error checking favorite status:', error)
    }
  }

  const toggleFavorite = async () => {
    try {
      if (!user) {
        toast({
          title: 'تسجيل الدخول مطلوب',
          description: 'يجب تسجيل الدخول لإضافة العناصر للمفضلة',
          variant: 'destructive'
        })
        return
      }

      setFavoriteLoading(true)

      if (isFavorite) {
        // Remove from favorites
        await api.delete('/favorites/remove', {
          data: {
            itemType: 'tender',
            itemId: params.id as string
          }
        })
        setIsFavorite(false)
        toast({
          title: '❤️ تم إزالة من المفضلة',
          description: `تم إزالة "${tender?.title}" من المفضلة`
        })
      } else {
        // Add to favorites
        await api.post('/favorites/add', {
          itemType: 'tender',
          itemId: params.id as string,
          notifyOnUpdate: true,
          notifyOnStatusChange: true
        })
        setIsFavorite(true)
        toast({
          title: '💖 تم إضافة إلى المفضلة',
          description: `تم إضافة "${tender?.title}" إلى المفضلة`
        })
      }
    } catch (error: any) {
      console.error('Error toggling favorite:', error)
      toast({
        title: 'خطأ في المفضلة',
        description: 'حدث خطأ في تحديث المفضلة',
        variant: 'destructive'
      })
    } finally {
      setFavoriteLoading(false)
    }
  }

  const applyToTender = async () => {
    if (!user) {
      toast({
        title: 'تسجيل الدخول مطلوب',
        description: 'يجب تسجيل الدخول للتقدم للمناقصة',
        variant: 'destructive'
      })
      return
    }

    try {
      setApplying(true)
      const response = await api.post(`/tenders/${params.id}/apply`)
      
      if (response.data.success) {
        toast({
          title: 'تم التقديم',
          description: 'تم تقديم طلبك للمناقصة بنجاح'
        })
        setHasApplied(true)
        loadTender() // Reload to get updated data
      }
    } catch (error: any) {
      toast({
        title: 'خطأ في التقديم',
        description: error.response?.data?.message || 'حدث خطأ في تقديم الطلب',
        variant: 'destructive'
      })
    } finally {
      setApplying(false)
    }
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 0
    }).format(price)
  }

  const formatTimeRemaining = (deadline: string) => {
    const now = new Date()
    const end = new Date(deadline)
    const diff = end.getTime() - now.getTime()
    
    if (diff <= 0) return 'انتهى'
    
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    
    if (days > 0) return `${days} يوم ${hours} ساعة`
    if (hours > 0) return `${hours} ساعة`
    return 'أقل من ساعة'
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">نشط</Badge>
      case 'completed':
        return <Badge className="bg-blue-100 text-blue-800">مكتمل</Badge>
      case 'review':
        return <Badge className="bg-yellow-100 text-yellow-800">قيد المراجعة</Badge>
      case 'cancelled':
        return <Badge variant="destructive">ملغي</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getCategoryName = (category: string) => {
    const categories = {
      'construction': 'إنشاءات',
      'it_technology': 'تقنية المعلومات',
      'consulting': 'استشارات',
      'healthcare': 'رعاية صحية',
      'education': 'تعليم',
      'transportation': 'نقل ومواصلات',
      'other': 'أخرى'
    }
    return categories[category as keyof typeof categories] || category
  }

  const canEdit = user && tender && tender.organizer && user._id === tender.organizer._id
  const canApply = user && tender && tender.organizer && user._id !== tender.organizer._id && tender.status === 'active' && !hasApplied

  if (loading) {
    return (
      <DashboardLayout allowedRoles={['user', 'company', 'government', 'admin']}>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">جاري تحميل تفاصيل المناقصة...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (!tender) {
    return (
      <DashboardLayout allowedRoles={['user', 'company', 'government', 'admin']}>
        <div className="text-center py-12">
          <AlertCircle className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">المناقصة غير موجودة</h2>
          <p className="text-gray-600 mb-6">لم يتم العثور على المناقصة المطلوبة</p>
          <Button onClick={() => router.push('/tenders')}>
            العودة للمناقصات
          </Button>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout allowedRoles={['user', 'company', 'government', 'admin']}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <Button 
            variant="ghost" 
            onClick={() => router.push('/tenders')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            العودة للمناقصات
          </Button>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              onClick={toggleFavorite}
              disabled={favoriteLoading}
              className={isFavorite ? 'text-red-600 border-red-200 bg-red-50' : ''}
            >
              <Heart className={`h-4 w-4 ml-2 ${isFavorite ? 'fill-red-600' : ''}`} />
              {favoriteLoading ? 'جاري التحديث...' : isFavorite ? 'إزالة من المفضلة' : 'إضافة للمفضلة'}
            </Button>
            <Button variant="outline">
              <Share2 className="h-4 w-4 ml-2" />
              مشاركة
            </Button>
            {canEdit && (
              <Button variant="outline" onClick={() => router.push(`/tenders/${tender._id}/edit`)}>
                <Edit className="h-4 w-4 ml-2" />
                تعديل
              </Button>
            )}
          </div>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Main Details */}
          <div className="lg:col-span-2 space-y-6">
            {/* Title and Status */}
            <Card>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-2xl">{tender.title}</CardTitle>
                    <div className="flex items-center gap-4 mt-3">
                      {getStatusBadge(tender.status)}
                      <div className="flex items-center gap-1 text-gray-600">
                        <Eye className="h-4 w-4" />
                        <span>{tender.viewsCount} مشاهدة</span>
                      </div>
                      <div className="flex items-center gap-1 text-gray-600">
                        <Calendar className="h-4 w-4" />
                        <span>{new Date(tender.createdAt).toLocaleDateString('ar-SA')}</span>
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-3xl font-bold text-green-600">
                      {formatPrice(tender.budget)}
                    </div>
                    <div className="text-sm text-gray-500">الميزانية المخصصة</div>
                  </div>
                </div>
              </CardHeader>
            </Card>

            {/* Description */}
            <Card>
              <CardHeader>
                <CardTitle>وصف المناقصة</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 leading-relaxed whitespace-pre-line">
                  {tender.description}
                </p>
              </CardContent>
            </Card>

            {/* Requirements */}
            {tender.requirements && tender.requirements.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>المتطلبات والشروط</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {tender.requirements.map((requirement, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700">{requirement}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}

            {/* Documents */}
            {tender.documents && tender.documents.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>المستندات المرفقة</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {tender.documents.map((document, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center gap-2">
                          <FileText className="h-5 w-5 text-blue-600" />
                          <span className="font-medium">مستند {index + 1}</span>
                        </div>
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4 ml-2" />
                          تحميل
                        </Button>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Right Column - Sidebar */}
          <div className="space-y-6">
            {/* Apply Button */}
            {canApply && (
              <Card>
                <CardContent className="p-6">
                  <Button 
                    onClick={applyToTender}
                    disabled={applying}
                    className="w-full h-12 text-lg bg-green-600 hover:bg-green-700"
                  >
                    {applying ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                        جاري التقديم...
                      </>
                    ) : (
                      <>
                        <Send className="h-5 w-5 ml-2" />
                        تقدم للمناقصة
                      </>
                    )}
                  </Button>
                </CardContent>
              </Card>
            )}

            {hasApplied && (
              <Card className="border-green-200 bg-green-50">
                <CardContent className="p-6 text-center">
                  <CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-3" />
                  <h3 className="font-medium text-green-900 mb-2">تم التقديم</h3>
                  <p className="text-sm text-green-700">لقد تقدمت لهذه المناقصة بالفعل</p>
                </CardContent>
              </Card>
            )}

            {/* Tender Info */}
            <Card>
              <CardHeader>
                <CardTitle>معلومات المناقصة</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-3">
                  <Clock className="h-5 w-5 text-gray-400" />
                  <div>
                    <div className="font-medium">الموعد النهائي</div>
                    <div className="text-sm text-gray-600">
                      {new Date(tender.deadline).toLocaleDateString('ar-SA')}
                    </div>
                    <div className="text-sm text-orange-600 font-medium">
                      {formatTimeRemaining(tender.deadline)}
                    </div>
                  </div>
                </div>

                <Separator />

                <div className="flex items-center gap-3">
                  <Tag className="h-5 w-5 text-gray-400" />
                  <div>
                    <div className="font-medium">الفئة</div>
                    <div className="text-sm text-gray-600">{getCategoryName(tender.category)}</div>
                  </div>
                </div>

                <Separator />

                <div className="flex items-center gap-3">
                  <MapPin className="h-5 w-5 text-gray-400" />
                  <div>
                    <div className="font-medium">الموقع</div>
                    <div className="text-sm text-gray-600">
                      {typeof tender.location === 'object' && tender.location?.country
                        ? tender.location.country
                        : tender.location || 'غير محدد'}
                    </div>
                  </div>
                </div>

                <Separator />

                <div className="flex items-center gap-3">
                  <Users className="h-5 w-5 text-gray-400" />
                  <div>
                    <div className="font-medium">عدد المتقدمين</div>
                    <div className="text-sm text-gray-600">{tender.applications?.length || 0} متقدم</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Organizer Info */}
            <Card>
              <CardHeader>
                <CardTitle>الجهة المنظمة</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-3">
                  <Building className="h-5 w-5 text-gray-400" />
                  <div>
                    <div className="font-medium">
                      {tender.organizer?.profile?.governmentEntity || 'غير محدد'}
                    </div>
                    <div className="text-sm text-gray-600">جهة حكومية</div>
                  </div>
                </div>

                {tender.organizer?.profile?.phone && (
                  <>
                    <Separator />
                    <div className="text-sm">
                      <div className="font-medium text-gray-600">الهاتف</div>
                      <div>{tender.organizer.profile.phone}</div>
                    </div>
                  </>
                )}

                {tender.organizer?.profile?.email && (
                  <>
                    <Separator />
                    <div className="text-sm">
                      <div className="font-medium text-gray-600">البريد الإلكتروني</div>
                      <div>{tender.organizer.profile.email}</div>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
