'use client'

import React, { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useRouter } from 'next/navigation'
import { Search, Filter, Gavel, FileText } from 'lucide-react'

interface SearchWidgetProps {
  className?: string
  showFilters?: boolean
  placeholder?: string
}

export default function SearchWidget({ 
  className = '', 
  showFilters = true, 
  placeholder = 'ابحث في المزادات والمناقصات...' 
}: SearchWidgetProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [searchType, setSearchType] = useState('all')
  const [category, setCategory] = useState('all')
  const router = useRouter()

  const handleSearch = () => {
    if (!searchQuery.trim() && searchType === 'all' && category === 'all') {
      // If no search criteria, go to general search page
      router.push('/search')
      return
    }

    // Build search URL with parameters
    const params = new URLSearchParams()
    if (searchQuery.trim()) params.set('q', searchQuery.trim())
    if (searchType !== 'all') params.set('type', searchType)
    if (category !== 'all') params.set('category', category)

    router.push(`/search?${params.toString()}`)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch()
    }
  }

  const quickSearchButtons = [
    {
      label: 'المزادات النشطة',
      icon: <Gavel className="h-4 w-4" />,
      onClick: () => router.push('/search?type=auction&status=active')
    },
    {
      label: 'المناقصات المفتوحة',
      icon: <FileText className="h-4 w-4" />,
      onClick: () => router.push('/search?type=tender&status=active')
    }
  ]

  return (
    <Card className={`bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200 ${className}`}>
      <CardContent className="p-6">
        <div className="space-y-4">
          {/* Search Header */}
          <div className="flex items-center gap-2 mb-4">
            <Search className="h-5 w-5 text-blue-600" />
            <h3 className="text-lg font-semibold text-gray-900">البحث السريع</h3>
          </div>

          {/* Main Search Input */}
          <div className="flex gap-2">
            <div className="flex-1">
              <Input
                placeholder={placeholder}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={handleKeyPress}
                className="text-lg h-12"
              />
            </div>
            <Button 
              onClick={handleSearch}
              className="h-12 px-6 bg-blue-600 hover:bg-blue-700"
            >
              <Search className="h-5 w-5" />
            </Button>
          </div>

          {/* Filters */}
          {showFilters && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-700 mb-1 block">النوع</label>
                <Select value={searchType} onValueChange={setSearchType}>
                  <SelectTrigger>
                    <SelectValue placeholder="اختر النوع" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">الكل</SelectItem>
                    <SelectItem value="auction">مزادات</SelectItem>
                    <SelectItem value="tender">مناقصات</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700 mb-1 block">الفئة</label>
                <Select value={category} onValueChange={setCategory}>
                  <SelectTrigger>
                    <SelectValue placeholder="اختر الفئة" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع الفئات</SelectItem>
                    <SelectItem value="construction">إنشاءات</SelectItem>
                    <SelectItem value="it_technology">تقنية معلومات</SelectItem>
                    <SelectItem value="consulting">استشارات</SelectItem>
                    <SelectItem value="healthcare">رعاية صحية</SelectItem>
                    <SelectItem value="education">تعليم</SelectItem>
                    <SelectItem value="transportation">نقل</SelectItem>
                    <SelectItem value="manufacturing">تصنيع</SelectItem>
                    <SelectItem value="energy">طاقة</SelectItem>
                    <SelectItem value="other">أخرى</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}

          {/* Quick Search Buttons */}
          <div className="flex flex-wrap gap-2 pt-2">
            {quickSearchButtons.map((button, index) => (
              <Button
                key={index}
                variant="outline"
                size="sm"
                onClick={button.onClick}
                className="text-blue-700 border-blue-300 hover:bg-blue-100"
              >
                {button.icon}
                <span className="mr-2">{button.label}</span>
              </Button>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
