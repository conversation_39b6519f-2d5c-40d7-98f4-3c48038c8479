'use client'

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import { useRouter, usePathname } from 'next/navigation'
import { Button } from '@/components/ui/button'
import {
  LayoutDashboard,
  Users,
  Gavel,
  FileText,
  Building,
  User,
  Shield,
  Crown,
  Settings,
  LogOut,
  ChevronLeft,
  ChevronRight,
  Home,
  PlusCircle,
  Bell,
  TrendingUp,
  Activity,
  Trophy,
  Heart,
  Search
} from 'lucide-react'

interface SidebarProps {
  userRole?: string
}

interface NavItem {
  href: string
  label: string
  icon: React.ReactNode
  roles: string[]
}

const navItems: NavItem[] = [
  // Admin and Super Admin navigation
  {
    href: '/admin/dashboard',
    label: 'لوحة التحكم',
    icon: <LayoutDashboard className="h-5 w-5" />,
    roles: ['admin', 'super_admin']
  },
  {
    href: '/search',
    label: 'البحث والاستكشاف',
    icon: <Search className="h-5 w-5" />,
    roles: ['admin', 'super_admin']
  },
  {
    href: '/admin/pending-accounts',
    label: 'الحسابات المعلقة',
    icon: <Users className="h-5 w-5" />,
    roles: ['admin', 'super_admin']
  },
  {
    href: '/admin/users',
    label: 'إدارة المستخدمين',
    icon: <Shield className="h-5 w-5" />,
    roles: ['admin', 'super_admin']
  },
  {
    href: '/admin/auctions',
    label: 'إدارة المزادات',
    icon: <Gavel className="h-5 w-5" />,
    roles: ['admin', 'super_admin']
  },
  {
    href: '/admin/tenders',
    label: 'إدارة المناقصات',
    icon: <FileText className="h-5 w-5" />,
    roles: ['admin', 'super_admin']
  },
  {
    href: '/admin/create-tender',
    label: 'إنشاء مناقصة',
    icon: <PlusCircle className="h-5 w-5" />,
    roles: ['admin', 'super_admin']
  },
  {
    href: '/favorites',
    label: 'المفضلة',
    icon: <Heart className="h-5 w-5" />,
    roles: ['admin', 'super_admin']
  },
  {
    href: '/admin/settings',
    label: 'الإعدادات',
    icon: <Settings className="h-5 w-5" />,
    roles: ['super_admin']
  },

  // Company navigation
  {
    href: '/company/dashboard',
    label: 'لوحة التحكم',
    icon: <LayoutDashboard className="h-5 w-5" />,
    roles: ['company']
  },
  {
    href: '/search',
    label: 'البحث والاستكشاف',
    icon: <Search className="h-5 w-5" />,
    roles: ['company']
  },
  {
    href: '/company/auctions',
    label: 'مزاداتي',
    icon: <Gavel className="h-5 w-5" />,
    roles: ['company']
  },
  {
    href: '/company/tenders',
    label: 'مناقصاتي',
    icon: <FileText className="h-5 w-5" />,
    roles: ['company']
  },
  {
    href: '/company/create-auction',
    label: 'إنشاء مزاد',
    icon: <PlusCircle className="h-5 w-5" />,
    roles: ['company']
  },
  {
    href: '/company/bids',
    label: 'عطاءاتي',
    icon: <TrendingUp className="h-5 w-5" />,
    roles: ['company']
  },
  {
    href: '/company/notifications',
    label: 'الإشعارات',
    icon: <Bell className="h-5 w-5" />,
    roles: ['company']
  },
  {
    href: '/favorites',
    label: 'المفضلة',
    icon: <Heart className="h-5 w-5" />,
    roles: ['company']
  },
  {
    href: '/company/profile',
    label: 'الملف الشخصي',
    icon: <Building className="h-5 w-5" />,
    roles: ['company']
  },

  // Individual user navigation
  {
    href: '/user/dashboard',
    label: 'لوحة التحكم',
    icon: <LayoutDashboard className="h-5 w-5" />,
    roles: ['individual']
  },
  {
    href: '/search',
    label: 'البحث والاستكشاف',
    icon: <Search className="h-5 w-5" />,
    roles: ['individual']
  },
  {
    href: '/user/leaderboard',
    label: 'لوحة الصدارة',
    icon: <Trophy className="h-5 w-5" />,
    roles: ['individual']
  },
  {
    href: '/user/auctions',
    label: 'المزادات المتاحة',
    icon: <Gavel className="h-5 w-5" />,
    roles: ['individual']
  },
  {
    href: '/user/my-bids',
    label: 'مزايداتي',
    icon: <Activity className="h-5 w-5" />,
    roles: ['individual']
  },
  {
    href: '/user/notifications',
    label: 'الإشعارات',
    icon: <Bell className="h-5 w-5" />,
    roles: ['individual']
  },
  {
    href: '/favorites',
    label: 'المفضلة',
    icon: <Heart className="h-5 w-5" />,
    roles: ['individual']
  },
  {
    href: '/user/profile',
    label: 'الملف الشخصي',
    icon: <User className="h-5 w-5" />,
    roles: ['individual']
  },

  // Government navigation
  {
    href: '/government/dashboard',
    label: 'لوحة التحكم',
    icon: <LayoutDashboard className="h-5 w-5" />,
    roles: ['government']
  },
  {
    href: '/search',
    label: 'البحث والاستكشاف',
    icon: <Search className="h-5 w-5" />,
    roles: ['government']
  },
  {
    href: '/government/tenders',
    label: 'مناقصاتي',
    icon: <FileText className="h-5 w-5" />,
    roles: ['government']
  },
  {
    href: '/government/applications',
    label: 'طلبات المشاركة',
    icon: <Users className="h-5 w-5" />,
    roles: ['government']
  },
  {
    href: '/government/notifications',
    label: 'الإشعارات',
    icon: <Bell className="h-5 w-5" />,
    roles: ['government']
  },
  {
    href: '/favorites',
    label: 'المفضلة',
    icon: <Heart className="h-5 w-5" />,
    roles: ['government']
  },
  {
    href: '/government/profile',
    label: 'الملف الشخصي',
    icon: <Shield className="h-5 w-5" />,
    roles: ['government']
  }
]

export default function Sidebar({ userRole }: SidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [user, setUser] = useState<any>(null)
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    const userData = localStorage.getItem('user')
    if (userData) {
      setUser(JSON.parse(userData))
    }
  }, [])

  const handleLogout = () => {
    localStorage.removeItem('token')
    localStorage.removeItem('user')
    router.push('/auth/login')
  }

  const roleIcon = () => {
    switch (user?.role) {
      case 'super_admin':
        return <Crown className="h-6 w-6 text-yellow-500" />
      case 'admin':
        return <Shield className="h-6 w-6 text-blue-500" />
      case 'company':
        return <Building className="h-6 w-6 text-green-500" />
      case 'government':
        return <Shield className="h-6 w-6 text-red-500" />
      case 'individual':
        return <User className="h-6 w-6 text-purple-500" />
      default:
        return <User className="h-6 w-6" />
    }
  }

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'super_admin':
        return 'مدير عام'
      case 'admin':
        return 'مدير'
      case 'company':
        return 'شركة'
      case 'government':
        return 'جهة حكومية'
      case 'individual':
        return 'فرد'
      default:
        return role
    }
  }

  const filteredNavItems = navItems.filter(item => 
    item.roles.includes(user?.role || '')
  )

  return (
    <div className={`${isCollapsed ? 'w-16' : 'w-56'} transition-all duration-300 backdrop-blur-xl bg-white/90 border-r border-white/20 shadow-xl flex flex-col h-screen relative z-10`}>
      {/* Enhanced Header */}
      <div className="p-4 border-b border-gray-200/50">
        <div className="flex items-center justify-between">
          {!isCollapsed && (
            <Link href="/" className="flex items-center gap-3 group">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 rounded-xl flex items-center justify-center group-hover:scale-110 group-hover:rotate-3 transition-all duration-300 shadow-lg">
                <Crown className="w-4 h-4 text-white" />
              </div>
              <div className="text-right">
                <h1 className="text-base font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">المنصة</h1>
                <p className="text-xs text-gray-600 font-medium">المزادات والمناقصات</p>
              </div>
            </Link>
          )}
          {isCollapsed && (
            <div className="w-8 h-8 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 rounded-xl flex items-center justify-center shadow-lg mx-auto">
              <Crown className="w-4 h-4 text-white" />
            </div>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="p-1.5 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors duration-200"
          >
            {isCollapsed ? <ChevronLeft className="h-3 w-3" /> : <ChevronRight className="h-3 w-3" />}
          </Button>
        </div>
      </div>

      {/* Enhanced User Info */}
      {user && (
        <div className="px-4 py-4 border-b border-gray-200/50">
          <div className={`flex items-center ${isCollapsed ? 'justify-center' : 'gap-3'}`}>
            <div className="w-8 h-8 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 rounded-xl flex items-center justify-center shadow-lg">
              {roleIcon()}
            </div>
            {!isCollapsed && (
              <div className="flex-1 min-w-0">
                <p className="text-sm font-bold text-gray-900 truncate">
                  {user.profile?.fullName || user.profile?.companyName || user.profile?.governmentEntity || 'المدير'}
                </p>
                <p className="text-xs text-gray-600 font-medium truncate">
                  {getRoleLabel(user.role)}
                </p>
                <div className="flex items-center gap-1.5 mt-1">
                  <div className="w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-xs text-gray-500">متصل</span>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Enhanced Navigation */}
      <nav className="flex-1 px-4 py-4">
        <div className="space-y-2">
          {filteredNavItems.map((item) => {
            const isActive = pathname === item.href
            return (
              <Link
                key={item.href}
                href={item.href}
                className={`group flex items-center ${isCollapsed ? 'justify-center px-2 py-3' : 'gap-3 px-4 py-3'} rounded-xl transition-all duration-300 relative overflow-hidden ${
                  isActive
                    ? 'bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-indigo-500/10 border border-blue-300/30 shadow-md backdrop-blur-sm'
                    : 'hover:bg-white/60 hover:shadow-md hover:scale-[1.02] border border-transparent'
                }`}
              >
                {/* Active state background effect */}
                {isActive && (
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5"></div>
                )}

                <div className={`${isCollapsed ? 'w-6 h-6' : 'w-8 h-8'} rounded-lg flex items-center justify-center transition-all duration-300 relative z-10 ${
                  isActive
                    ? 'bg-gradient-to-br from-blue-500 to-purple-600 shadow-lg scale-105'
                    : 'bg-gray-100 group-hover:bg-gradient-to-br group-hover:from-gray-200 group-hover:to-gray-300'
                }`}>
                  <div className={`transition-all duration-300 ${
                    isActive ? 'text-white scale-105' : 'text-gray-600 group-hover:text-gray-700'
                  }`}>
                    {item.icon}
                  </div>
                </div>

                {!isCollapsed && (
                  <div className="relative z-10">
                    <span className={`text-sm font-semibold transition-all duration-300 ${
                      isActive
                        ? 'text-gray-900'
                        : 'text-gray-700 group-hover:text-gray-900'
                    }`}>
                      {item.label}
                    </span>
                    {isActive && (
                      <div className="w-1.5 h-1.5 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mt-0.5"></div>
                    )}
                  </div>
                )}

                {isActive && !isCollapsed && (
                  <ChevronRight className="w-4 h-4 text-blue-600 mr-auto relative z-10" />
                )}
              </Link>
            )
          })}
        </div>
      </nav>

      {/* Enhanced Footer */}
      <div className="px-4 pb-4">
        <div className="border-t border-gray-200/50 pt-4">
          <Button
            onClick={handleLogout}
            variant="ghost"
            className={`w-full group flex items-center ${isCollapsed ? 'justify-center px-2 py-3' : 'gap-3 px-4 py-3'} rounded-xl hover:bg-red-50/80 hover:shadow-md transition-all duration-300 border border-transparent hover:border-red-200/50`}
          >
            <div className={`${isCollapsed ? 'w-6 h-6' : 'w-8 h-8'} rounded-lg bg-red-100 group-hover:bg-gradient-to-br group-hover:from-red-500 group-hover:to-pink-500 flex items-center justify-center transition-all duration-300`}>
              <LogOut className={`${isCollapsed ? 'w-3 h-3' : 'w-4 h-4'} text-red-600 group-hover:text-white transition-all duration-300`} />
            </div>
            {!isCollapsed && (
              <span className="text-sm font-semibold text-red-700 group-hover:text-red-800 transition-all duration-300">
                تسجيل الخروج
              </span>
            )}
          </Button>
        </div>
      </div>
    </div>
  )
}
