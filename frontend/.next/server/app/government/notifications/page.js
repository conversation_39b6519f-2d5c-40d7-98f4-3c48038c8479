/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/government/notifications/page";
exports.ids = ["app/government/notifications/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fgovernment%2Fnotifications%2Fpage&page=%2Fgovernment%2Fnotifications%2Fpage&appPaths=%2Fgovernment%2Fnotifications%2Fpage&pagePath=private-next-app-dir%2Fgovernment%2Fnotifications%2Fpage.tsx&appDir=%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fgovernment%2Fnotifications%2Fpage&page=%2Fgovernment%2Fnotifications%2Fpage&appPaths=%2Fgovernment%2Fnotifications%2Fpage&pagePath=private-next-app-dir%2Fgovernment%2Fnotifications%2Fpage.tsx&appDir=%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'government',\n        {\n        children: [\n        'notifications',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/government/notifications/page.tsx */ \"(rsc)/./app/government/notifications/page.tsx\")), \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/government/layout.tsx */ \"(rsc)/./app/government/layout.tsx\")), \"/Users/<USER>/Desktop/brid1/frontend/app/government/layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/Users/<USER>/Desktop/brid1/frontend/app/layout.tsx\"],\n'error': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/error.tsx */ \"(rsc)/./app/error.tsx\")), \"/Users/<USER>/Desktop/brid1/frontend/app/error.tsx\"],\n'loading': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/loading.tsx */ \"(rsc)/./app/loading.tsx\")), \"/Users/<USER>/Desktop/brid1/frontend/app/loading.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/not-found.tsx */ \"(rsc)/./app/not-found.tsx\")), \"/Users/<USER>/Desktop/brid1/frontend/app/not-found.tsx\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/government/notifications/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/government/notifications/page\",\n        pathname: \"/government/notifications\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fgovernment%2Fnotifications%2Fpage&page=%2Fgovernment%2Fnotifications%2Fpage&appPaths=%2Fgovernment%2Fnotifications%2Fpage&pagePath=private-next-app-dir%2Fgovernment%2Fnotifications%2Fpage.tsx&appDir=%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fapp%2Ferror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fapp%2Ferror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/error.tsx */ \"(ssr)/./app/error.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGZmFoYWQlMkZEZXNrdG9wJTJGYnJpZDElMkZmcm9udGVuZCUyRmFwcCUyRmVycm9yLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsMElBQXNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXVjdGlvbi10ZW5kZXItZnJvbnRlbmQvPzBjNzUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvZmFoYWQvRGVza3RvcC9icmlkMS9mcm9udGVuZC9hcHAvZXJyb3IudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fapp%2Ferror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fapp%2Fgovernment%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fapp%2Fgovernment%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/government/layout.tsx */ \"(ssr)/./app/government/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGZmFoYWQlMkZEZXNrdG9wJTJGYnJpZDElMkZmcm9udGVuZCUyRmFwcCUyRmdvdmVybm1lbnQlMkZsYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrS0FBa0ciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hdWN0aW9uLXRlbmRlci1mcm9udGVuZC8/NzVjMyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9mYWhhZC9EZXNrdG9wL2JyaWQxL2Zyb250ZW5kL2FwcC9nb3Zlcm5tZW50L2xheW91dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fapp%2Fgovernment%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fapp%2Fgovernment%2Fnotifications%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fapp%2Fgovernment%2Fnotifications%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/government/notifications/page.tsx */ \"(ssr)/./app/government/notifications/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGZmFoYWQlMkZEZXNrdG9wJTJGYnJpZDElMkZmcm9udGVuZCUyRmFwcCUyRmdvdmVybm1lbnQlMkZub3RpZmljYXRpb25zJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBMQUE4RyIsInNvdXJjZXMiOlsid2VicGFjazovL2F1Y3Rpb24tdGVuZGVyLWZyb250ZW5kLz8wNjAzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2ZhaGFkL0Rlc2t0b3AvYnJpZDEvZnJvbnRlbmQvYXBwL2dvdmVybm1lbnQvbm90aWZpY2F0aW9ucy9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fapp%2Fgovernment%2Fnotifications%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fapp%2Fnot-found.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fapp%2Fnot-found.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/not-found.tsx */ \"(ssr)/./app/not-found.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGZmFoYWQlMkZEZXNrdG9wJTJGYnJpZDElMkZmcm9udGVuZCUyRmFwcCUyRm5vdC1mb3VuZC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtKQUEwRiIsInNvdXJjZXMiOlsid2VicGFjazovL2F1Y3Rpb24tdGVuZGVyLWZyb250ZW5kLz81ZDRkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2ZhaGFkL0Rlc2t0b3AvYnJpZDEvZnJvbnRlbmQvYXBwL25vdC1mb3VuZC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fapp%2Fnot-found.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fcomponents%2Fui%2Ftoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fcontexts%2FAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fcomponents%2Fui%2Ftoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fcontexts%2FAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/toaster.tsx */ \"(ssr)/./components/ui/toaster.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./contexts/AuthContext.tsx */ \"(ssr)/./contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGZmFoYWQlMkZEZXNrdG9wJTJGYnJpZDElMkZmcm9udGVuZCUyRmNvbXBvbmVudHMlMkZ1aSUyRnRvYXN0ZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyVG9hc3RlciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmZhaGFkJTJGRGVza3RvcCUyRmJyaWQxJTJGZnJvbnRlbmQlMkZjb250ZXh0cyUyRkF1dGhDb250ZXh0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkF1dGhQcm92aWRlciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmZhaGFkJTJGRGVza3RvcCUyRmJyaWQxJTJGZnJvbnRlbmQlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZm9udCUyRmdvb2dsZSUyRnRhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyYXBwJTJGbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkludGVyJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTdEJTVEJTJDJTVDJTIydmFyaWFibGVOYW1lJTVDJTIyJTNBJTVDJTIyaW50ZXIlNUMlMjIlN0QlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZmYWhhZCUyRkRlc2t0b3AlMkZicmlkMSUyRmZyb250ZW5kJTJGYXBwJTJGZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtLQUErSDtBQUMvSDtBQUNBLGdLQUFtSSIsInNvdXJjZXMiOlsid2VicGFjazovL2F1Y3Rpb24tdGVuZGVyLWZyb250ZW5kLz9kOGYyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiVG9hc3RlclwiXSAqLyBcIi9Vc2Vycy9mYWhhZC9EZXNrdG9wL2JyaWQxL2Zyb250ZW5kL2NvbXBvbmVudHMvdWkvdG9hc3Rlci50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkF1dGhQcm92aWRlclwiXSAqLyBcIi9Vc2Vycy9mYWhhZC9EZXNrdG9wL2JyaWQxL2Zyb250ZW5kL2NvbnRleHRzL0F1dGhDb250ZXh0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fcomponents%2Fui%2Ftoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fcontexts%2FAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGZmFoYWQlMkZEZXNrdG9wJTJGYnJpZDElMkZmcm9udGVuZCUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmFwcC1yb3V0ZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZmYWhhZCUyRkRlc2t0b3AlMkZicmlkMSUyRmZyb250ZW5kJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGY2xpZW50LXBhZ2UuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZmYWhhZCUyRkRlc2t0b3AlMkZicmlkMSUyRmZyb250ZW5kJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGZXJyb3ItYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZmYWhhZCUyRkRlc2t0b3AlMkZicmlkMSUyRmZyb250ZW5kJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGbGF5b3V0LXJvdXRlci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmZhaGFkJTJGRGVza3RvcCUyRmJyaWQxJTJGZnJvbnRlbmQlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZub3QtZm91bmQtYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZmYWhhZCUyRkRlc2t0b3AlMkZicmlkMSUyRmZyb250ZW5kJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQStIO0FBQy9IO0FBQ0Esb09BQWdJO0FBQ2hJO0FBQ0EsME9BQW1JO0FBQ25JO0FBQ0Esd09BQWtJO0FBQ2xJO0FBQ0Esa1BBQXVJO0FBQ3ZJO0FBQ0Esc1FBQWlKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXVjdGlvbi10ZW5kZXItZnJvbnRlbmQvPzFmNTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvZmFoYWQvRGVza3RvcC9icmlkMS9mcm9udGVuZC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2FwcC1yb3V0ZXIuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9mYWhhZC9EZXNrdG9wL2JyaWQxL2Zyb250ZW5kL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvY2xpZW50LXBhZ2UuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9mYWhhZC9EZXNrdG9wL2JyaWQxL2Zyb250ZW5kL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9mYWhhZC9EZXNrdG9wL2JyaWQxL2Zyb250ZW5kL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbGF5b3V0LXJvdXRlci5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2ZhaGFkL0Rlc2t0b3AvYnJpZDEvZnJvbnRlbmQvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9mYWhhZC9EZXNrdG9wL2JyaWQxL2Zyb250ZW5kL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/error.tsx":
/*!***********************!*\
  !*** ./app/error.tsx ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Error)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Error({ error, reset }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Log the error to an error reporting service\n        console.error(\"Application error:\", error);\n    }, [\n        error\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            className: \"w-full max-w-md\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-6 w-6 text-red-600\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/error.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/error.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"text-xl font-bold text-gray-900\",\n                            children: \"حدث خطأ غير متوقع\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/error.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/error.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 text-center\",\n                            children: \"نعتذر، حدث خطأ أثناء تحميل الصفحة. يرجى المحاولة مرة أخرى.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/error.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 11\n                        }, this),\n                         true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-100 p-3 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-600 font-mono\",\n                                children: error.message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/error.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/error.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: reset,\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-4 w-4 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/error.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"إعادة المحاولة\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/error.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>window.location.href = \"/\",\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/error.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"العودة للصفحة الرئيسية\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/error.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/error.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/error.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/error.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/error.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/error.tsx\n");

/***/ }),

/***/ "(ssr)/./app/government/layout.tsx":
/*!***********************************!*\
  !*** ./app/government/layout.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GovernmentLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _components_Sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Sidebar */ \"(ssr)/./components/Sidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction GovernmentLayout({ children }) {\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -top-40 -right-40 w-96 h-96 bg-gradient-to-br from-blue-400 to-cyan-300 rounded-full mix-blend-multiply filter blur-2xl opacity-20 animate-blob\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/layout.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -bottom-40 -left-40 w-96 h-96 bg-gradient-to-br from-purple-400 to-pink-300 rounded-full mix-blend-multiply filter blur-2xl opacity-20 animate-blob animation-delay-2000\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/layout.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-40 left-1/2 w-96 h-96 bg-gradient-to-br from-indigo-400 to-blue-300 rounded-full mix-blend-multiply filter blur-2xl opacity-20 animate-blob animation-delay-4000\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/layout.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/layout.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-screen relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        userRole: user?.role\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/layout.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex flex-col overflow-hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"flex-1 overflow-y-auto p-6 relative\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-7xl mx-auto\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/layout.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/layout.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/layout.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/layout.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/layout.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/government/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./app/government/notifications/page.tsx":
/*!***********************************************!*\
  !*** ./app/government/notifications/page.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GovernmentNotificationsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/tabs */ \"(ssr)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(ssr)/./components/ui/use-toast.tsx\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(ssr)/./components/DashboardLayout.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,Check,CheckCheck,Clock,Eye,FileText,RefreshCw,Send,Trash2,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,Check,CheckCheck,Clock,Eye,FileText,RefreshCw,Send,Trash2,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,Check,CheckCheck,Clock,Eye,FileText,RefreshCw,Send,Trash2,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,Check,CheckCheck,Clock,Eye,FileText,RefreshCw,Send,Trash2,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,Check,CheckCheck,Clock,Eye,FileText,RefreshCw,Send,Trash2,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,Check,CheckCheck,Clock,Eye,FileText,RefreshCw,Send,Trash2,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,Check,CheckCheck,Clock,Eye,FileText,RefreshCw,Send,Trash2,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,Check,CheckCheck,Clock,Eye,FileText,RefreshCw,Send,Trash2,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-check.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,Check,CheckCheck,Clock,Eye,FileText,RefreshCw,Send,Trash2,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,Check,CheckCheck,Clock,Eye,FileText,RefreshCw,Send,Trash2,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,Check,CheckCheck,Clock,Eye,FileText,RefreshCw,Send,Trash2,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,Check,CheckCheck,Clock,Eye,FileText,RefreshCw,Send,Trash2,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\nfunction GovernmentNotificationsPage() {\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadNotifications();\n    }, []);\n    const loadNotifications = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_9__[\"default\"].get(\"/government/notifications\");\n            if (response.data.success) {\n                setNotifications(response.data.data.notifications);\n            } else {\n                // Fallback to sample data\n                setNotifications([\n                    {\n                        _id: \"1\",\n                        type: \"new_application\",\n                        title: \"طلب جديد على مناقصة\",\n                        message: \"تم تقديم طلب جديد على مناقصة بناء المستشفى من شركة البناء المتقدمة\",\n                        read: false,\n                        createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),\n                        priority: \"high\",\n                        category: \"tender\",\n                        data: {\n                            tenderId: \"tender123\",\n                            applicantName: \"شركة البناء المتقدمة\"\n                        }\n                    },\n                    {\n                        _id: \"2\",\n                        type: \"tender_deadline_approaching\",\n                        title: \"اقتراب موعد انتهاء مناقصة\",\n                        message: \"مناقصة تطوير نظام إدارة المدارس تنتهي خلال 24 ساعة\",\n                        read: false,\n                        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n                        priority: \"medium\",\n                        category: \"tender\",\n                        data: {\n                            tenderId: \"tender456\"\n                        }\n                    },\n                    {\n                        _id: \"3\",\n                        type: \"application_review_required\",\n                        title: \"مراجعة طلب مطلوبة\",\n                        message: \"يتطلب طلب شركة التقنية الحديثة مراجعة عاجلة لمناقصة الأنظمة الذكية\",\n                        read: true,\n                        createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),\n                        priority: \"high\",\n                        category: \"application\",\n                        data: {\n                            applicationId: \"app789\",\n                            tenderId: \"tender789\"\n                        }\n                    },\n                    {\n                        _id: \"4\",\n                        type: \"tender_published\",\n                        title: \"تم نشر مناقصة جديدة\",\n                        message: \"تم نشر مناقصة تطوير البنية التحتية للنقل بنجاح\",\n                        read: true,\n                        createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),\n                        priority: \"low\",\n                        category: \"tender\",\n                        data: {\n                            tenderId: \"tender101\"\n                        }\n                    },\n                    {\n                        _id: \"5\",\n                        type: \"system_update\",\n                        title: \"تحديث النظام\",\n                        message: \"تم تحديث نظام إدارة المناقصات بميزات جديدة لتحسين الأداء\",\n                        read: true,\n                        createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),\n                        priority: \"low\",\n                        category: \"system\",\n                        data: {}\n                    }\n                ]);\n            }\n        } catch (error) {\n            console.error(\"Error loading notifications:\", error);\n            toast({\n                title: \"خطأ في التحميل\",\n                description: \"حدث خطأ في تحميل الإشعارات\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const markAsRead = async (notificationId)=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_9__[\"default\"].patch(`/government/notifications/${notificationId}/read`);\n            setNotifications(notifications.map((notif)=>notif._id === notificationId ? {\n                    ...notif,\n                    read: true\n                } : notif));\n        } catch (error) {\n            toast({\n                title: \"خطأ\",\n                description: \"حدث خطأ في تحديث الإشعار\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const markAllAsRead = async ()=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_9__[\"default\"].patch(\"/government/notifications/mark-all-read\");\n            setNotifications(notifications.map((notif)=>({\n                    ...notif,\n                    read: true\n                })));\n            toast({\n                title: \"تم التحديث\",\n                description: \"تم تحديد جميع الإشعارات كمقروءة\"\n            });\n        } catch (error) {\n            toast({\n                title: \"خطأ\",\n                description: \"حدث خطأ في تحديث الإشعارات\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const deleteNotification = async (notificationId)=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_9__[\"default\"].delete(`/government/notifications/${notificationId}`);\n            setNotifications(notifications.filter((notif)=>notif._id !== notificationId));\n            toast({\n                title: \"تم الحذف\",\n                description: \"تم حذف الإشعار بنجاح\"\n            });\n        } catch (error) {\n            toast({\n                title: \"خطأ\",\n                description: \"حدث خطأ في حذف الإشعار\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleNotificationClick = (notification)=>{\n        if (!notification.read) {\n            markAsRead(notification._id);\n        }\n        // Navigate based on notification type\n        if (notification.data) {\n            switch(notification.type){\n                case \"new_application\":\n                case \"application_review_required\":\n                    if (notification.data.applicationId) {\n                        router.push(`/government/applications/${notification.data.applicationId}`);\n                    } else {\n                        router.push(\"/government/applications\");\n                    }\n                    break;\n                case \"tender_deadline_approaching\":\n                case \"tender_published\":\n                    router.push(`/tenders/${notification.data.tenderId}`);\n                    break;\n                default:\n                    break;\n            }\n        }\n    };\n    const formatRelativeTime = (dateString)=>{\n        const now = new Date();\n        const date = new Date(dateString);\n        const diff = now.getTime() - date.getTime();\n        const minutes = Math.floor(diff / (1000 * 60));\n        const hours = Math.floor(diff / (1000 * 60 * 60));\n        const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n        if (days > 0) return `منذ ${days} يوم`;\n        if (hours > 0) return `منذ ${hours} ساعة`;\n        if (minutes > 0) return `منذ ${minutes} دقيقة`;\n        return \"الآن\";\n    };\n    const getNotificationIcon = (type)=>{\n        switch(type){\n            case \"new_application\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-5 w-5 text-blue-600\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 16\n                }, this);\n            case \"tender_deadline_approaching\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-5 w-5 text-orange-600\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 16\n                }, this);\n            case \"application_review_required\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"h-5 w-5 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 16\n                }, this);\n            case \"tender_published\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-5 w-5 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 16\n                }, this);\n            case \"system_update\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    className: \"h-5 w-5 text-purple-600\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                    lineNumber: 235,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    className: \"h-5 w-5 text-gray-600\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                    lineNumber: 237,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getPriorityBadge = (priority)=>{\n        switch(priority){\n            case \"high\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    variant: \"destructive\",\n                    children: \"عالية\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 16\n                }, this);\n            case \"medium\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: \"bg-yellow-100 text-yellow-800\",\n                    children: \"متوسطة\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 16\n                }, this);\n            case \"low\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    variant: \"secondary\",\n                    children: \"منخفضة\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                    lineNumber: 248,\n                    columnNumber: 16\n                }, this);\n            default:\n                return null;\n        }\n    };\n    const filteredNotifications = notifications.filter((notif)=>{\n        if (activeTab === \"all\") return true;\n        if (activeTab === \"unread\") return !notif.read;\n        if (activeTab === \"read\") return notif.read;\n        return notif.category === activeTab;\n    });\n    const unreadCount = notifications.filter((notif)=>!notif.read).length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n        allowedRoles: [\n            \"government\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold\",\n                                        children: \"إشعارات الجهة الحكومية\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-green-100 mt-1\",\n                                        children: \"متابعة جميع التحديثات المتعلقة بمناقصاتك وطلبات المشاركة\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: unreadCount\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-green-100\",\n                                                children: \"غير مقروءة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: loadNotifications,\n                                                variant: \"outline\",\n                                                className: \"bg-white/10 border-white/20 text-white hover:bg-white/20\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-4 w-4 ml-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"تحديث\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 17\n                                            }, this),\n                                            unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: markAllAsRead,\n                                                className: \"bg-white text-green-600 hover:bg-gray-100\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-4 w-4 ml-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"تحديد الكل كمقروء\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                    lineNumber: 267,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.Tabs, {\n                    value: activeTab,\n                    onValueChange: setActiveTab,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsList, {\n                            className: \"grid w-full grid-cols-5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                    value: \"all\",\n                                    children: [\n                                        \"الكل (\",\n                                        notifications.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                    value: \"unread\",\n                                    children: [\n                                        \"غير مقروءة (\",\n                                        unreadCount,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                    value: \"tender\",\n                                    children: \"مناقصات\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                    value: \"application\",\n                                    children: \"طلبات\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                    value: \"system\",\n                                    children: \"النظام\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                            value: activeTab,\n                            className: \"mt-6\",\n                            children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center py-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-8 w-8 animate-spin text-green-600 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"جاري تحميل الإشعارات...\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 15\n                            }, this) : filteredNotifications.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: filteredNotifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        className: `transition-all hover:shadow-md cursor-pointer ${!notification.read ? \"border-l-4 border-l-green-500 bg-green-50/50\" : \"\"}`,\n                                        onClick: ()=>handleNotificationClick(notification),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            className: \"p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start gap-3 flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-1\",\n                                                                children: getNotificationIcon(notification.type)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                                lineNumber: 334,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2 mb-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: `font-medium ${!notification.read ? \"font-semibold\" : \"\"}`,\n                                                                                children: notification.title\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                                                lineNumber: 339,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            !notification.read && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"h-2 w-2 bg-green-600 rounded-full\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                                                lineNumber: 343,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            getPriorityBadge(notification.priority)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                                        lineNumber: 338,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-600 text-sm mb-2\",\n                                                                        children: notification.message\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                                        lineNumber: 347,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-4 text-xs text-gray-500\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center gap-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                        className: \"h-3 w-3\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                                                        lineNumber: 352,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    formatRelativeTime(notification.createdAt)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                                                lineNumber: 351,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            notification.data?.applicantName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center gap-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                        className: \"h-3 w-3\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                                                        lineNumber: 357,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    notification.data.applicantName\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                                                lineNumber: 356,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                                        lineNumber: 350,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                                lineNumber: 337,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            !notification.read && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"ghost\",\n                                                                size: \"sm\",\n                                                                onClick: (e)=>{\n                                                                    e.stopPropagation();\n                                                                    markAsRead(notification._id);\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                                    lineNumber: 374,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                                lineNumber: 366,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"ghost\",\n                                                                size: \"sm\",\n                                                                onClick: (e)=>{\n                                                                    e.stopPropagation();\n                                                                    deleteNotification(notification._id);\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                                    lineNumber: 385,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                                lineNumber: 377,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, notification._id, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"لا توجد إشعارات\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: activeTab === \"unread\" ? \"جميع الإشعارات مقروءة\" : \"ستظهر هنا الإشعارات المتعلقة بأنشطة جهتكم الحكومية\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                    lineNumber: 304,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n            lineNumber: 265,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n        lineNumber: 264,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/government/notifications/page.tsx\n");

/***/ }),

/***/ "(ssr)/./app/not-found.tsx":
/*!***************************!*\
  !*** ./app/not-found.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_FileQuestion_Home_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,FileQuestion,Home!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-question.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_FileQuestion_Home_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,FileQuestion,Home!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_FileQuestion_Home_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,FileQuestion,Home!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            className: \"w-full max-w-md\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_FileQuestion_Home_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-6 w-6 text-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/not-found.tsx\",\n                                lineNumber: 14,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/not-found.tsx\",\n                            lineNumber: 13,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"text-xl font-bold text-gray-900\",\n                            children: \"الصفحة غير موجودة\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/not-found.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/not-found.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 text-center\",\n                            children: \"عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/not-found.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    href: \"/\",\n                                    className: \"w-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        className: \"w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_FileQuestion_Home_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-4 w-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/not-found.tsx\",\n                                                lineNumber: 28,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"العودة للصفحة الرئيسية\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/not-found.tsx\",\n                                        lineNumber: 27,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/not-found.tsx\",\n                                    lineNumber: 26,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>window.history.back(),\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_FileQuestion_Home_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/not-found.tsx\",\n                                            lineNumber: 38,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"العودة للصفحة السابقة\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/not-found.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/not-found.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/not-found.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/not-found.tsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/not-found.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/not-found.tsx\n");

/***/ }),

/***/ "(ssr)/./components/DashboardLayout.tsx":
/*!****************************************!*\
  !*** ./components/DashboardLayout.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_Sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Sidebar */ \"(ssr)/./components/Sidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction DashboardLayout({ children, allowedRoles }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const token = localStorage.getItem(\"token\");\n        const userData = localStorage.getItem(\"user\");\n        if (!token || !userData) {\n            router.push(\"/auth/login\");\n            return;\n        }\n        const parsedUser = JSON.parse(userData);\n        // Check if user role is allowed\n        if (!allowedRoles.includes(parsedUser.role)) {\n            // Redirect to appropriate dashboard based on role\n            switch(parsedUser.role){\n                case \"admin\":\n                case \"super_admin\":\n                    router.push(\"/admin/dashboard\");\n                    break;\n                case \"company\":\n                    router.push(\"/company/dashboard\");\n                    break;\n                case \"individual\":\n                    router.push(\"/user/dashboard\");\n                    break;\n                case \"government\":\n                    router.push(\"/government/dashboard\");\n                    break;\n                default:\n                    router.push(\"/auth/login\");\n            }\n            return;\n        }\n        // Check if account is approved (except for admins)\n        if (parsedUser.role !== \"admin\" && parsedUser.role !== \"super_admin\" && parsedUser.status !== \"approved\") {\n            router.push(\"/account-status\");\n            return;\n        }\n        setUser(parsedUser);\n        setLoading(false);\n    }, [\n        router,\n        allowedRoles\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/DashboardLayout.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-muted-foreground\",\n                        children: \"جاري التحميل...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/DashboardLayout.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/DashboardLayout.tsx\",\n                lineNumber: 64,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/DashboardLayout.tsx\",\n            lineNumber: 63,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                userRole: user.role\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/DashboardLayout.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 overflow-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/DashboardLayout.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/DashboardLayout.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/DashboardLayout.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/DashboardLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Sidebar.tsx":
/*!********************************!*\
  !*** ./components/Sidebar.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/gavel.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst navItems = [\n    // Admin and Super Admin navigation\n    {\n        href: \"/admin/dashboard\",\n        label: \"لوحة التحكم\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 44,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"admin\",\n            \"super_admin\"\n        ]\n    },\n    {\n        href: \"/admin/pending-accounts\",\n        label: \"الحسابات المعلقة\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 50,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"admin\",\n            \"super_admin\"\n        ]\n    },\n    {\n        href: \"/admin/users\",\n        label: \"إدارة المستخدمين\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 56,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"admin\",\n            \"super_admin\"\n        ]\n    },\n    {\n        href: \"/admin/auctions\",\n        label: \"إدارة المزادات\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 62,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"admin\",\n            \"super_admin\"\n        ]\n    },\n    {\n        href: \"/admin/tenders\",\n        label: \"إدارة المناقصات\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 68,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"admin\",\n            \"super_admin\"\n        ]\n    },\n    {\n        href: \"/admin/create-tender\",\n        label: \"إنشاء مناقصة\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 74,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"admin\",\n            \"super_admin\"\n        ]\n    },\n    {\n        href: \"/admin/settings\",\n        label: \"الإعدادات\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 80,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"super_admin\"\n        ]\n    },\n    // Company navigation\n    {\n        href: \"/company/dashboard\",\n        label: \"لوحة التحكم\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 88,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"company\"\n        ]\n    },\n    {\n        href: \"/company/auctions\",\n        label: \"مزاداتي\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 94,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"company\"\n        ]\n    },\n    {\n        href: \"/company/tenders\",\n        label: \"مناقصاتي\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 100,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"company\"\n        ]\n    },\n    {\n        href: \"/company/create-auction\",\n        label: \"إنشاء مزاد\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 106,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"company\"\n        ]\n    },\n    {\n        href: \"/company/bids\",\n        label: \"عطاءاتي\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 112,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"company\"\n        ]\n    },\n    {\n        href: \"/company/notifications\",\n        label: \"الإشعارات\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 118,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"company\"\n        ]\n    },\n    {\n        href: \"/company/profile\",\n        label: \"الملف الشخصي\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 124,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"company\"\n        ]\n    },\n    // Individual user navigation\n    {\n        href: \"/user/dashboard\",\n        label: \"لوحة التحكم\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 132,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"individual\"\n        ]\n    },\n    {\n        href: \"/user/leaderboard\",\n        label: \"لوحة الصدارة\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 138,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"individual\"\n        ]\n    },\n    {\n        href: \"/user/auctions\",\n        label: \"المزادات المتاحة\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 144,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"individual\"\n        ]\n    },\n    {\n        href: \"/user/my-bids\",\n        label: \"مزايداتي\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 150,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"individual\"\n        ]\n    },\n    {\n        href: \"/user/notifications\",\n        label: \"الإشعارات\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 156,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"individual\"\n        ]\n    },\n    {\n        href: \"/user/profile\",\n        label: \"الملف الشخصي\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 162,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"individual\"\n        ]\n    },\n    // Government navigation\n    {\n        href: \"/government/dashboard\",\n        label: \"لوحة التحكم\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 170,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"government\"\n        ]\n    },\n    {\n        href: \"/government/tenders\",\n        label: \"مناقصاتي\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 176,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"government\"\n        ]\n    },\n    {\n        href: \"/government/create-tender\",\n        label: \"إنشاء مناقصة\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 182,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"government\"\n        ]\n    },\n    {\n        href: \"/government/applications\",\n        label: \"طلبات المشاركة\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 188,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"government\"\n        ]\n    },\n    {\n        href: \"/government/notifications\",\n        label: \"الإشعارات\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 194,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"government\"\n        ]\n    },\n    {\n        href: \"/government/profile\",\n        label: \"الملف الشخصي\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 200,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"government\"\n        ]\n    }\n];\nfunction Sidebar({ userRole }) {\n    const [isCollapsed, setIsCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const userData = localStorage.getItem(\"user\");\n        if (userData) {\n            setUser(JSON.parse(userData));\n        }\n    }, []);\n    const handleLogout = ()=>{\n        localStorage.removeItem(\"token\");\n        localStorage.removeItem(\"user\");\n        router.push(\"/auth/login\");\n    };\n    const roleIcon = ()=>{\n        switch(user?.role){\n            case \"super_admin\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                    className: \"h-6 w-6 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 16\n                }, this);\n            case \"admin\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-6 w-6 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 16\n                }, this);\n            case \"company\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    className: \"h-6 w-6 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 16\n                }, this);\n            case \"government\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-6 w-6 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 16\n                }, this);\n            case \"individual\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    className: \"h-6 w-6 text-purple-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                    lineNumber: 235,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    className: \"h-6 w-6\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                    lineNumber: 237,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getRoleLabel = (role)=>{\n        switch(role){\n            case \"super_admin\":\n                return \"مدير عام\";\n            case \"admin\":\n                return \"مدير\";\n            case \"company\":\n                return \"شركة\";\n            case \"government\":\n                return \"جهة حكومية\";\n            case \"individual\":\n                return \"فرد\";\n            default:\n                return role;\n        }\n    };\n    const filteredNavItems = navItems.filter((item)=>item.roles.includes(user?.role || \"\"));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${isCollapsed ? \"w-16\" : \"w-56\"} transition-all duration-300 backdrop-blur-xl bg-white/90 border-r border-white/20 shadow-xl flex flex-col h-screen relative z-10`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-gray-200/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: \"/\",\n                            className: \"flex items-center gap-3 group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 rounded-xl flex items-center justify-center group-hover:scale-110 group-hover:rotate-3 transition-all duration-300 shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"w-4 h-4 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-base font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent\",\n                                            children: \"المنصة\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-600 font-medium\",\n                                            children: \"المزادات والمناقصات\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 13\n                        }, this),\n                        isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 rounded-xl flex items-center justify-center shadow-lg mx-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"w-4 h-4 text-white\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            onClick: ()=>setIsCollapsed(!isCollapsed),\n                            className: \"p-1.5 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors duration-200\",\n                            children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                className: \"h-3 w-3\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 28\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                className: \"h-3 w-3\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 66\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                    lineNumber: 266,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                lineNumber: 265,\n                columnNumber: 7\n            }, this),\n            user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-4 border-b border-gray-200/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `flex items-center ${isCollapsed ? \"justify-center\" : \"gap-3\"}`,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 rounded-xl flex items-center justify-center shadow-lg\",\n                            children: roleIcon()\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 13\n                        }, this),\n                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-bold text-gray-900 truncate\",\n                                    children: user.profile?.fullName || user.profile?.companyName || user.profile?.governmentEntity || \"المدير\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-600 font-medium truncate\",\n                                    children: getRoleLabel(user.role)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-1.5 mt-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: \"متصل\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                            lineNumber: 302,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                    lineNumber: 297,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                lineNumber: 296,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex-1 px-4 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: filteredNavItems.map((item)=>{\n                        const isActive = pathname === item.href;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: item.href,\n                            className: `group flex items-center ${isCollapsed ? \"justify-center px-2 py-3\" : \"gap-3 px-4 py-3\"} rounded-xl transition-all duration-300 relative overflow-hidden ${isActive ? \"bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-indigo-500/10 border border-blue-300/30 shadow-md backdrop-blur-sm\" : \"hover:bg-white/60 hover:shadow-md hover:scale-[1.02] border border-transparent\"}`,\n                            children: [\n                                isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `${isCollapsed ? \"w-6 h-6\" : \"w-8 h-8\"} rounded-lg flex items-center justify-center transition-all duration-300 relative z-10 ${isActive ? \"bg-gradient-to-br from-blue-500 to-purple-600 shadow-lg scale-105\" : \"bg-gray-100 group-hover:bg-gradient-to-br group-hover:from-gray-200 group-hover:to-gray-300\"}`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `transition-all duration-300 ${isActive ? \"text-white scale-105\" : \"text-gray-600 group-hover:text-gray-700\"}`,\n                                        children: item.icon\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 17\n                                }, this),\n                                !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: `text-sm font-semibold transition-all duration-300 ${isActive ? \"text-gray-900\" : \"text-gray-700 group-hover:text-gray-900\"}`,\n                                            children: item.label\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 21\n                                        }, this),\n                                        isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-1.5 h-1.5 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mt-0.5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 19\n                                }, this),\n                                isActive && !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    className: \"w-4 h-4 text-blue-600 mr-auto relative z-10\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, item.href, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                    lineNumber: 321,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                lineNumber: 320,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 pb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-gray-200/50 pt-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        onClick: handleLogout,\n                        variant: \"ghost\",\n                        className: `w-full group flex items-center ${isCollapsed ? \"justify-center px-2 py-3\" : \"gap-3 px-4 py-3\"} rounded-xl hover:bg-red-50/80 hover:shadow-md transition-all duration-300 border border-transparent hover:border-red-200/50`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `${isCollapsed ? \"w-6 h-6\" : \"w-8 h-8\"} rounded-lg bg-red-100 group-hover:bg-gradient-to-br group-hover:from-red-500 group-hover:to-pink-500 flex items-center justify-center transition-all duration-300`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                    className: `${isCollapsed ? \"w-3 h-3\" : \"w-4 h-4\"} text-red-600 group-hover:text-white transition-all duration-300`\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                    lineNumber: 384,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                lineNumber: 383,\n                                columnNumber: 13\n                            }, this),\n                            !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-semibold text-red-700 group-hover:text-red-800 transition-all duration-300\",\n                                children: \"تسجيل الخروج\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                        lineNumber: 378,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                    lineNumber: 377,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                lineNumber: 376,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n        lineNumber: 263,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/badge.tsx":
/*!*********************************!*\
  !*** ./components/ui/badge.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n            outline: \"text-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/ui/badge.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/ui/button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/ui/card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/ui/card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/ui/card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/ui/card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/ui/card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/ui/card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/tabs.tsx":
/*!********************************!*\
  !*** ./components/ui/tabs.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Tabs: () => (/* binding */ Tabs),\n/* harmony export */   TabsContent: () => (/* binding */ TabsContent),\n/* harmony export */   TabsList: () => (/* binding */ TabsList),\n/* harmony export */   TabsTrigger: () => (/* binding */ TabsTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-tabs */ \"(ssr)/./node_modules/@radix-ui/react-tabs/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Tabs,TabsList,TabsTrigger,TabsContent auto */ \n\n\n\nconst Tabs = _radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst TabsList = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.List, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/ui/tabs.tsx\",\n        lineNumber: 14,\n        columnNumber: 3\n    }, undefined));\nTabsList.displayName = _radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.List.displayName;\nconst TabsTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.Trigger, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/ui/tabs.tsx\",\n        lineNumber: 29,\n        columnNumber: 3\n    }, undefined));\nTabsTrigger.displayName = _radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.Trigger.displayName;\nconst TabsContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.Content, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/ui/tabs.tsx\",\n        lineNumber: 44,\n        columnNumber: 3\n    }, undefined));\nTabsContent.displayName = _radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/tabs.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/toast.tsx":
/*!*********************************!*\
  !*** ./components/ui/toast.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toast: () => (/* binding */ Toast),\n/* harmony export */   ToastAction: () => (/* binding */ ToastAction),\n/* harmony export */   ToastClose: () => (/* binding */ ToastClose),\n/* harmony export */   ToastDescription: () => (/* binding */ ToastDescription),\n/* harmony export */   ToastTitle: () => (/* binding */ ToastTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst toastVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all duration-300 animate-in slide-in-from-top-2\", {\n    variants: {\n        variant: {\n            default: \"border bg-background text-foreground\",\n            destructive: \"border-red-500 bg-red-500 text-white\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Toast = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(toastVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/ui/toast.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, undefined);\n});\nToast.displayName = \"Toast\";\nconst ToastAction = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/ui/toast.tsx\",\n        lineNumber: 42,\n        columnNumber: 3\n    }, undefined));\nToastAction.displayName = \"ToastAction\";\nconst ToastClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"absolute right-2 top-2 rounded-md p-1 text-foreground/70 opacity-100 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-[.destructive]:text-white/70 group-[.destructive]:hover:text-white\", className),\n        \"toast-close\": \"\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/ui/toast.tsx\",\n            lineNumber: 66,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/ui/toast.tsx\",\n        lineNumber: 57,\n        columnNumber: 3\n    }, undefined));\nToastClose.displayName = \"ToastClose\";\nconst ToastTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/ui/toast.tsx\",\n        lineNumber: 75,\n        columnNumber: 3\n    }, undefined));\nToastTitle.displayName = \"ToastTitle\";\nconst ToastDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm opacity-90\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/ui/toast.tsx\",\n        lineNumber: 87,\n        columnNumber: 3\n    }, undefined));\nToastDescription.displayName = \"ToastDescription\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/toast.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/toaster.tsx":
/*!***********************************!*\
  !*** ./components/ui/toaster.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/toast */ \"(ssr)/./components/ui/toast.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(ssr)/./components/ui/use-toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ Toaster auto */ \n\n\nfunction Toaster() {\n    const { toasts } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    console.log(\"Toaster rendering with toasts:\", toasts);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-0 right-0 z-[100] flex max-h-screen w-full max-w-[420px] flex-col p-4 space-y-2\",\n        children: toasts.map(function({ id, title, description, action, ...props }) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.Toast, {\n                ...props,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid gap-1\",\n                        children: [\n                            title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastTitle, {\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/ui/toaster.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 25\n                            }, this),\n                            description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastDescription, {\n                                children: description\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/ui/toaster.tsx\",\n                                lineNumber: 24,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/ui/toaster.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 13\n                    }, this),\n                    action,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastClose, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/ui/toaster.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, id, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/ui/toaster.tsx\",\n                lineNumber: 20,\n                columnNumber: 11\n            }, this);\n        })\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/ui/toaster.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/toaster.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/use-toast.tsx":
/*!*************************************!*\
  !*** ./components/ui/use-toast.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst initialState = {\n    toasts: []\n};\n// Global toast state\nlet globalToastState = initialState;\nlet listeners = [];\nfunction updateGlobalState(newState) {\n    globalToastState = newState;\n    listeners.forEach((listener)=>listener(newState));\n}\nfunction useToast() {\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(globalToastState);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        listeners.push(setState);\n        return ()=>{\n            listeners = listeners.filter((listener)=>listener !== setState);\n        };\n    }, []);\n    const toast = ({ ...props })=>{\n        console.log(\"Toast called with:\", props);\n        const id = Math.random().toString(36).substr(2, 9);\n        const newToast = {\n            ...props,\n            id\n        };\n        const newState = {\n            ...globalToastState,\n            toasts: [\n                ...globalToastState.toasts,\n                newToast\n            ]\n        };\n        console.log(\"Updating toast state with:\", newState);\n        updateGlobalState(newState);\n        // Auto remove after 8 seconds\n        setTimeout(()=>{\n            const updatedState = {\n                ...globalToastState,\n                toasts: globalToastState.toasts.filter((t)=>t.id !== id)\n            };\n            updateGlobalState(updatedState);\n        }, 8000);\n        return {\n            id,\n            dismiss: ()=>{\n                const updatedState = {\n                    ...globalToastState,\n                    toasts: globalToastState.toasts.filter((t)=>t.id !== id)\n                };\n                updateGlobalState(updatedState);\n            }\n        };\n    };\n    return {\n        toast,\n        toasts: state.toasts\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/use-toast.tsx\n");

/***/ }),

/***/ "(ssr)/./contexts/AuthContext.tsx":
/*!**********************************!*\
  !*** ./contexts/AuthContext.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Initialize auth state from localStorage on app start\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initAuth = ()=>{\n            try {\n                const storedToken = localStorage.getItem(\"token\");\n                const storedUser = localStorage.getItem(\"user\");\n                if (storedToken && storedUser) {\n                    setToken(storedToken);\n                    setUser(JSON.parse(storedUser));\n                }\n            } catch (error) {\n                console.error(\"Error initializing auth:\", error);\n                // Clear corrupted data\n                localStorage.removeItem(\"token\");\n                localStorage.removeItem(\"refreshToken\");\n                localStorage.removeItem(\"user\");\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        initAuth();\n    }, []);\n    const login = (newToken, refreshToken, userData)=>{\n        setToken(newToken);\n        setUser(userData);\n        // Store in localStorage\n        localStorage.setItem(\"token\", newToken);\n        localStorage.setItem(\"refreshToken\", refreshToken);\n        localStorage.setItem(\"user\", JSON.stringify(userData));\n    };\n    const logout = ()=>{\n        setToken(null);\n        setUser(null);\n        // Clear localStorage\n        localStorage.removeItem(\"token\");\n        localStorage.removeItem(\"refreshToken\");\n        localStorage.removeItem(\"user\");\n        // Redirect to login\n        router.push(\"/auth/login\");\n    };\n    const value = {\n        user,\n        token,\n        isLoading,\n        login,\n        logout,\n        isAuthenticated: !!token && !!user\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/contexts/AuthContext.tsx\",\n        lineNumber: 89,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   activityAPI: () => (/* binding */ activityAPI),\n/* harmony export */   adminAPI: () => (/* binding */ adminAPI),\n/* harmony export */   auctionAPI: () => (/* binding */ auctionAPI),\n/* harmony export */   authAPI: () => (/* binding */ authAPI),\n/* harmony export */   companyAPI: () => (/* binding */ companyAPI),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   favoritesAPI: () => (/* binding */ favoritesAPI),\n/* harmony export */   governmentAPI: () => (/* binding */ governmentAPI),\n/* harmony export */   leaderboardAPI: () => (/* binding */ leaderboardAPI),\n/* harmony export */   messagesAPI: () => (/* binding */ messagesAPI),\n/* harmony export */   tenderAPI: () => (/* binding */ tenderAPI),\n/* harmony export */   userAPI: () => (/* binding */ userAPI)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n\nconst API_BASE_URL = \"http://localhost:5000/api\" || 0;\nconst api = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    headers: {\n        \"Content-Type\": \"application/json\"\n    },\n    withCredentials: true\n});\n// Request interceptor to add auth token\napi.interceptors.request.use((config)=>{\n    const token = localStorage.getItem(\"token\");\n    if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Response interceptor to handle errors and token refresh\napi.interceptors.response.use((response)=>response, async (error)=>{\n    const originalRequest = error.config;\n    if (error.response?.status === 401 && !originalRequest._retry) {\n        originalRequest._retry = true;\n        try {\n            const refreshToken = localStorage.getItem(\"refreshToken\");\n            if (refreshToken) {\n                // Try to refresh the token\n                const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`${API_BASE_URL}/auth/refresh`, {\n                    refreshToken\n                });\n                const { accessToken, refreshToken: newRefreshToken } = response.data.data;\n                // Update stored tokens\n                localStorage.setItem(\"token\", accessToken);\n                localStorage.setItem(\"refreshToken\", newRefreshToken);\n                // Update the authorization header and retry the original request\n                originalRequest.headers.Authorization = `Bearer ${accessToken}`;\n                return api(originalRequest);\n            }\n        } catch (refreshError) {\n            // Refresh failed, redirect to login\n            localStorage.removeItem(\"token\");\n            localStorage.removeItem(\"refreshToken\");\n            localStorage.removeItem(\"user\");\n            window.location.href = \"/auth/login\";\n            return Promise.reject(refreshError);\n        }\n    }\n    // If not a 401 or refresh failed, just redirect to login for 401s\n    if (error.response?.status === 401) {\n        localStorage.removeItem(\"token\");\n        localStorage.removeItem(\"refreshToken\");\n        localStorage.removeItem(\"user\");\n        window.location.href = \"/auth/login\";\n    }\n    return Promise.reject(error);\n});\nconst authAPI = {\n    register: (data)=>api.post(\"/auth/register\", data),\n    login: (data)=>api.post(\"/auth/login\", data),\n    logout: ()=>api.post(\"/auth/logout\"),\n    verifyEmail: (token)=>api.post(\"/auth/verify-email\", {\n            token\n        }),\n    resendVerification: (email)=>api.post(\"/auth/resend-verification\", {\n            email\n        }),\n    forgotPassword: (email)=>api.post(\"/auth/forgot-password\", {\n            email\n        }),\n    resetPassword: (data)=>api.post(\"/auth/reset-password\", data)\n};\nconst userAPI = {\n    getProfile: ()=>api.get(\"/users/profile\"),\n    updateProfile: (data)=>api.put(\"/users/profile\", data),\n    uploadDocuments: (data)=>api.post(\"/users/documents\", data, {\n            headers: {\n                \"Content-Type\": \"multipart/form-data\"\n            }\n        })\n};\nconst auctionAPI = {\n    getAll: ()=>api.get(\"/auctions\"),\n    getById: (id)=>api.get(`/auctions/${id}`),\n    create: (data)=>api.post(\"/auctions\", data),\n    update: (id, data)=>api.put(`/auctions/${id}`, data),\n    delete: (id)=>api.delete(`/auctions/${id}`),\n    placeBid: (id, amount)=>api.post(`/auctions/${id}/bid`, {\n            amount\n        })\n};\nconst tenderAPI = {\n    getAll: ()=>api.get(\"/tenders\"),\n    getById: (id)=>api.get(`/tenders/${id}`),\n    create: (data)=>api.post(\"/tenders\", data),\n    update: (id, data)=>api.put(`/tenders/${id}`, data),\n    delete: (id)=>api.delete(`/tenders/${id}`),\n    submitProposal: (id, data)=>api.post(`/tenders/${id}/proposal`, data)\n};\n// Favorites/Watchlist API\nconst favoritesAPI = {\n    // Get user's favorites\n    getFavorites: (params)=>api.get(\"/favorites\", {\n            params\n        }),\n    // Add item to favorites\n    addFavorite: (data)=>api.post(\"/favorites\", data),\n    // Remove item from favorites\n    removeFavorite: (itemType, itemId)=>api.delete(`/favorites/${itemType}/${itemId}`),\n    // Update favorite settings\n    updateFavorite: (itemType, itemId, data)=>api.put(`/favorites/${itemType}/${itemId}`, data),\n    // Check if item is favorited\n    checkFavorite: (itemType, itemId)=>api.get(`/favorites/check/${itemType}/${itemId}`)\n};\n// Activity Logs API\nconst activityAPI = {\n    // Get user's activity logs\n    getUserActivities: (params)=>api.get(\"/activity/user\", {\n            params\n        }),\n    // Get admin activity logs (admin only)\n    getAdminActivities: (params)=>api.get(\"/activity/admin\", {\n            params\n        }),\n    // Get activity statistics\n    getActivityStats: (params)=>api.get(\"/activity/stats\", {\n            params\n        })\n};\n// Messaging API\nconst messagesAPI = {\n    // Conversation management\n    conversations: {\n        // Get user's conversations\n        getAll: (params)=>api.get(\"/messages/conversations\", {\n                params\n            }),\n        // Get conversation by ID\n        getById: (conversationId)=>api.get(`/messages/conversations/${conversationId}`),\n        // Create new conversation\n        create: (data)=>api.post(\"/messages/conversations\", data),\n        // Update conversation\n        update: (conversationId, data)=>api.put(`/messages/conversations/${conversationId}`, data),\n        // Add participants\n        addParticipants: (conversationId, userIds)=>api.post(`/messages/conversations/${conversationId}/participants`, {\n                userIds\n            }),\n        // Remove participants\n        removeParticipants: (conversationId, userIds)=>api.delete(`/messages/conversations/${conversationId}/participants`, {\n                data: {\n                    userIds\n                }\n            }),\n        // Archive/unarchive conversation\n        archive: (conversationId)=>api.post(`/messages/conversations/${conversationId}/archive`),\n        unarchive: (conversationId)=>api.post(`/messages/conversations/${conversationId}/unarchive`)\n    },\n    // Message management\n    messages: {\n        // Get messages in conversation\n        getByConversation: (conversationId, params)=>api.get(`/messages/conversations/${conversationId}/messages`, {\n                params\n            }),\n        // Send message\n        send: (conversationId, data)=>api.post(`/messages/conversations/${conversationId}/messages`, data),\n        // Edit message\n        edit: (conversationId, messageId, data)=>api.put(`/messages/conversations/${conversationId}/messages/${messageId}`, data),\n        // Delete message\n        delete: (conversationId, messageId)=>api.delete(`/messages/conversations/${conversationId}/messages/${messageId}`),\n        // Mark messages as read\n        markAsRead: (conversationId, messageIds)=>api.post(`/messages/conversations/${conversationId}/read`, {\n                messageIds\n            }),\n        // Add reaction to message\n        react: (conversationId, messageId, emoji)=>api.post(`/messages/conversations/${conversationId}/messages/${messageId}/react`, {\n                emoji\n            }),\n        // Remove reaction from message\n        unreact: (conversationId, messageId, emoji)=>api.delete(`/messages/conversations/${conversationId}/messages/${messageId}/react`, {\n                data: {\n                    emoji\n                }\n            })\n    },\n    // Search messages\n    search: (params)=>api.get(\"/messages/search\", {\n            params\n        })\n};\n// Admin API\nconst adminAPI = {\n    // Dashboard statistics\n    getDashboardStats: ()=>api.get(\"/admin/dashboard\"),\n    // Pending accounts\n    getPendingAccounts: ()=>api.get(\"/admin/pending-accounts\"),\n    approvePendingAccount: (accountId)=>api.post(`/admin/pending-accounts/${accountId}/approve`),\n    rejectPendingAccount: (accountId, reason)=>api.post(`/admin/pending-accounts/${accountId}/reject`, {\n            reason\n        }),\n    // User management\n    users: {\n        getAll: (params)=>api.get(\"/admin/users\", {\n                params\n            }),\n        getById: (userId)=>api.get(`/admin/users/${userId}`),\n        update: (userId, data)=>api.put(`/admin/users/${userId}`, data),\n        delete: (userId)=>api.delete(`/admin/users/${userId}`),\n        activate: (userId)=>api.post(`/admin/users/${userId}/activate`),\n        deactivate: (userId)=>api.post(`/admin/users/${userId}/deactivate`)\n    },\n    // Auction management\n    auctions: {\n        getAll: (params)=>api.get(\"/admin/auctions\", {\n                params\n            }),\n        getById: (auctionId)=>api.get(`/admin/auctions/${auctionId}`),\n        approve: (auctionId)=>api.post(`/admin/auctions/${auctionId}/approve`),\n        reject: (auctionId, reason)=>api.post(`/admin/auctions/${auctionId}/reject`, {\n                reason\n            }),\n        suspend: (auctionId, reason)=>api.post(`/admin/auctions/${auctionId}/suspend`, {\n                reason\n            }),\n        delete: (auctionId)=>api.delete(`/admin/auctions/${auctionId}`)\n    },\n    // Tender management\n    tenders: {\n        getAll: (params)=>api.get(\"/admin/tenders\", {\n                params\n            }),\n        getById: (tenderId)=>api.get(`/admin/tenders/${tenderId}`),\n        approve: (tenderId)=>api.post(`/admin/tenders/${tenderId}/approve`),\n        reject: (tenderId, reason)=>api.post(`/admin/tenders/${tenderId}/reject`, {\n                reason\n            }),\n        suspend: (tenderId, reason)=>api.post(`/admin/tenders/${tenderId}/suspend`, {\n                reason\n            }),\n        delete: (tenderId)=>api.delete(`/admin/tenders/${tenderId}`)\n    },\n    // Individual tender methods (for backward compatibility)\n    getTender: (tenderId)=>api.get(`/admin/tenders/${tenderId}`),\n    getTenderSubmissions: (tenderId, params)=>api.get(`/admin/tenders/${tenderId}/submissions`, {\n            params\n        }),\n    updateTenderStatus: (tenderId, data)=>api.put(`/admin/tenders/${tenderId}/status`, data),\n    updateTenderSubmissionStatus: (tenderId, submissionId, data)=>api.put(`/admin/tenders/${tenderId}/submissions/${submissionId}/status`, data),\n    // Reports and analytics\n    reports: {\n        getFinancialReport: (params)=>api.get(\"/admin/reports/financial\", {\n                params\n            }),\n        getUserReport: (params)=>api.get(\"/admin/reports/users\", {\n                params\n            }),\n        getActivityReport: (params)=>api.get(\"/admin/reports/activity\", {\n                params\n            }),\n        getAuctionReport: (params)=>api.get(\"/admin/reports/auctions\", {\n                params\n            }),\n        getTenderReport: (params)=>api.get(\"/admin/reports/tenders\", {\n                params\n            })\n    },\n    // System settings\n    settings: {\n        getAll: ()=>api.get(\"/admin/settings\"),\n        update: (data)=>api.put(\"/admin/settings\", data),\n        backup: ()=>api.post(\"/admin/settings/backup\"),\n        restore: (backupId)=>api.post(`/admin/settings/restore/${backupId}`)\n    }\n};\n// Government API\nconst governmentAPI = {\n    // Tender management\n    tenders: {\n        getAll: (params)=>api.get(\"/government/tenders\", {\n                params\n            }),\n        getById: (tenderId)=>api.get(`/tenders/${tenderId}`),\n        create: (data)=>api.post(\"/government/tenders\", data),\n        update: (tenderId, data)=>api.put(`/tenders/${tenderId}`, data),\n        delete: (tenderId)=>api.delete(`/tenders/${tenderId}`),\n        publish: (tenderId)=>api.post(`/government/tenders/${tenderId}/publish`),\n        close: (tenderId)=>api.post(`/government/tenders/${tenderId}/close`),\n        cancel: (tenderId, reason)=>api.post(`/government/tenders/${tenderId}/cancel`, {\n                reason\n            })\n    },\n    // Proposal management\n    proposals: {\n        getByTender: (tenderId, params)=>api.get(`/government/tenders/${tenderId}/proposals`, {\n                params\n            }),\n        getById: (tenderId, proposalId)=>api.get(`/government/tenders/${tenderId}/proposals/${proposalId}`),\n        evaluate: (tenderId, proposalId, data)=>api.post(`/government/tenders/${tenderId}/proposals/${proposalId}/evaluate`, data),\n        shortlist: (tenderId, proposalId)=>api.post(`/government/tenders/${tenderId}/proposals/${proposalId}/shortlist`),\n        award: (tenderId, proposalId, data)=>api.post(`/government/tenders/${tenderId}/proposals/${proposalId}/award`, data),\n        reject: (tenderId, proposalId, reason)=>api.post(`/government/tenders/${tenderId}/proposals/${proposalId}/reject`, {\n                reason\n            })\n    },\n    // Contract management\n    contracts: {\n        getAll: (params)=>api.get(\"/government/contracts\", {\n                params\n            }),\n        getById: (contractId)=>api.get(`/government/contracts/${contractId}`),\n        create: (data)=>api.post(\"/government/contracts\", data),\n        update: (contractId, data)=>api.put(`/government/contracts/${contractId}`, data),\n        approve: (contractId)=>api.post(`/government/contracts/${contractId}/approve`),\n        terminate: (contractId, reason)=>api.post(`/government/contracts/${contractId}/terminate`, {\n                reason\n            })\n    },\n    // Reports and analytics\n    reports: {\n        getDashboard: ()=>api.get(\"/government/reports/dashboard\"),\n        getTenderReport: (params)=>api.get(\"/government/reports/tenders\", {\n                params\n            }),\n        getContractReport: (params)=>api.get(\"/government/reports/contracts\", {\n                params\n            }),\n        getVendorReport: (params)=>api.get(\"/government/reports/vendors\", {\n                params\n            })\n    }\n};\n// Leaderboard API\nconst leaderboardAPI = {\n    getLeaderboard: (params)=>api.get(\"/leaderboard\", {\n            params\n        }),\n    getUserRank: (userId)=>api.get(`/leaderboard/rank${userId ? `/${userId}` : \"\"}`),\n    getTopBidders: (params)=>api.get(\"/leaderboard/bidders\", {\n            params\n        }),\n    getTopSellers: (params)=>api.get(\"/leaderboard/sellers\", {\n            params\n        })\n};\n// Company API\nconst companyAPI = {\n    getProfile: ()=>api.get(\"/company/profile\"),\n    updateProfile: (data)=>api.put(\"/company/profile\", data),\n    getEmployees: (params)=>api.get(\"/company/employees\", {\n            params\n        }),\n    addEmployee: (data)=>api.post(\"/company/employees\", data),\n    updateEmployee: (employeeId, data)=>api.put(`/company/employees/${employeeId}`, data),\n    removeEmployee: (employeeId)=>api.delete(`/company/employees/${employeeId}`),\n    getAuctions: (params)=>api.get(\"/company/auctions\", {\n            params\n        }),\n    getTenders: (params)=>api.get(\"/company/tenders\", {\n            params\n        }),\n    getContracts: (params)=>api.get(\"/company/contracts\", {\n            params\n        })\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (api);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXVjdGlvbi10ZW5kZXItZnJvbnRlbmQvLi9saWIvdXRpbHMudHM/Zjc0NSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB0eXBlIENsYXNzVmFsdWUsIGNsc3ggfSBmcm9tIFwiY2xzeFwiXG5pbXBvcnQgeyB0d01lcmdlIH0gZnJvbSBcInRhaWx3aW5kLW1lcmdlXCJcblxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSlcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"4150bee34177\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hdWN0aW9uLXRlbmRlci1mcm9udGVuZC8uL2FwcC9nbG9iYWxzLmNzcz9kZThhIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNDE1MGJlZTM0MTc3XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/error.tsx":
/*!***********************!*\
  !*** ./app/error.tsx ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/brid1/frontend/app/error.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/government/layout.tsx":
/*!***********************************!*\
  !*** ./app/government/layout.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/brid1/frontend/app/government/layout.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/government/notifications/page.tsx":
/*!***********************************************!*\
  !*** ./app/government/notifications/page.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_ui_toaster__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/toaster */ \"(rsc)/./components/ui/toaster.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/AuthContext */ \"(rsc)/./contexts/AuthContext.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"منصة المزادات والمناقصات | Auction & Tender Platform\",\n    description: \"منصة شاملة للمزادات والمناقصات للشركات والأفراد والجهات الحكومية\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toaster__WEBPACK_IMPORTED_MODULE_2__.Toaster, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/layout.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/layout.tsx\",\n                lineNumber: 22,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/layout.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/layout.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBTU1BO0FBSmlCO0FBQzJCO0FBQ0s7QUFJaEQsTUFBTUcsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFBS0MsV0FBV1gsMkpBQWU7c0JBQzlCLDRFQUFDRSwrREFBWUE7O29CQUNWSztrQ0FDRCw4REFBQ04sMkRBQU9BOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLbEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hdWN0aW9uLXRlbmRlci1mcm9udGVuZC8uL2FwcC9sYXlvdXQudHN4Pzk5ODgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gXCJuZXh0XCI7XG5pbXBvcnQgeyBJbnRlciB9IGZyb20gXCJuZXh0L2ZvbnQvZ29vZ2xlXCI7XG5pbXBvcnQgXCIuL2dsb2JhbHMuY3NzXCI7XG5pbXBvcnQgeyBUb2FzdGVyIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS90b2FzdGVyXCI7XG5pbXBvcnQgeyBBdXRoUHJvdmlkZXIgfSBmcm9tIFwiLi4vY29udGV4dHMvQXV0aENvbnRleHRcIjtcblxuY29uc3QgaW50ZXIgPSBJbnRlcih7IHN1YnNldHM6IFsnbGF0aW4nXSB9KVxuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogJ9mF2YbYtdipINin2YTZhdiy2KfYr9in2Kog2YjYp9mE2YXZhtin2YLYtdin2KogfCBBdWN0aW9uICYgVGVuZGVyIFBsYXRmb3JtJyxcbiAgZGVzY3JpcHRpb246ICfZhdmG2LXYqSDYtNin2YXZhNipINmE2YTZhdiy2KfYr9in2Kog2YjYp9mE2YXZhtin2YLYtdin2Kog2YTZhNi02LHZg9in2Kog2YjYp9mE2KPZgdix2KfYryDZiNin2YTYrNmH2KfYqiDYp9mE2K3Zg9mI2YXZitipJyxcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXtpbnRlci5jbGFzc05hbWV9PlxuICAgICAgICA8QXV0aFByb3ZpZGVyPlxuICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgICA8VG9hc3RlciAvPlxuICAgICAgICA8L0F1dGhQcm92aWRlcj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiaW50ZXIiLCJUb2FzdGVyIiwiQXV0aFByb3ZpZGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/loading.tsx":
/*!*************************!*\
  !*** ./app/loading.tsx ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction Loading() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/loading.tsx\",\n                    lineNumber: 5,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mt-4 text-gray-600\",\n                    children: \"جاري التحميل...\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/loading.tsx\",\n                    lineNumber: 6,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/loading.tsx\",\n            lineNumber: 4,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/loading.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbG9hZGluZy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFlLFNBQVNBO0lBQ3RCLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVO2tCQUNiLDRFQUFDRDtZQUFJQyxXQUFVOzs4QkFDYiw4REFBQ0Q7b0JBQUlDLFdBQVU7Ozs7Ozs4QkFDZiw4REFBQ0M7b0JBQUVELFdBQVU7OEJBQXFCOzs7Ozs7Ozs7Ozs7Ozs7OztBQUkxQyIsInNvdXJjZXMiOlsid2VicGFjazovL2F1Y3Rpb24tdGVuZGVyLWZyb250ZW5kLy4vYXBwL2xvYWRpbmcudHN4P2M1MmEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTG9hZGluZygpIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmF5LTUwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTEyIHctMTIgYm9yZGVyLWItMiBib3JkZXItYmx1ZS02MDAgbXgtYXV0b1wiPjwvZGl2PlxuICAgICAgICA8cCBjbGFzc05hbWU9XCJtdC00IHRleHQtZ3JheS02MDBcIj7YrNin2LHZiiDYp9mE2KrYrdmF2YrZhC4uLjwvcD5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsiTG9hZGluZyIsImRpdiIsImNsYXNzTmFtZSIsInAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/loading.tsx\n");

/***/ }),

/***/ "(rsc)/./app/not-found.tsx":
/*!***************************!*\
  !*** ./app/not-found.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/brid1/frontend/app/not-found.tsx#default`));


/***/ }),

/***/ "(rsc)/./components/ui/toaster.tsx":
/*!***********************************!*\
  !*** ./components/ui/toaster.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Toaster: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/brid1/frontend/components/ui/toaster.tsx#Toaster`);


/***/ }),

/***/ "(rsc)/./contexts/AuthContext.tsx":
/*!**********************************!*\
  !*** ./contexts/AuthContext.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/brid1/frontend/contexts/AuthContext.tsx#AuthProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/brid1/frontend/contexts/AuthContext.tsx#useAuth`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@radix-ui","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/class-variance-authority","vendor-chunks/clsx","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/form-data","vendor-chunks/get-intrinsic","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fgovernment%2Fnotifications%2Fpage&page=%2Fgovernment%2Fnotifications%2Fpage&appPaths=%2Fgovernment%2Fnotifications%2Fpage&pagePath=private-next-app-dir%2Fgovernment%2Fnotifications%2Fpage.tsx&appDir=%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();