"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/government/create-tender/page",{

/***/ "(app-pages-browser)/./app/government/create-tender/page.tsx":
/*!***********************************************!*\
  !*** ./app/government/create-tender/page.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CreateTenderPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,FileText!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,FileText!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,FileText!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction CreateTenderPage() {\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        description: \"\",\n        budget: \"\",\n        category: \"\",\n        startDate: \"\",\n        submissionDeadline: \"\",\n        executionPeriod: \"\",\n        requirements: \"\",\n        specifications: \"\"\n    });\n    // Category mapping from Arabic to English\n    const categoryMapping = {\n        \"إنشاءات ومقاولات\": \"construction\",\n        \"توريد مواد\": \"manufacturing\",\n        \"خدمات\": \"other\",\n        \"استشارات\": \"consulting\",\n        \"صيانة\": \"other\",\n        \"تقنية معلومات\": \"it_technology\"\n    };\n    const handleChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData({\n            ...formData,\n            [name]: value\n        });\n    };\n    const handleSelectChange = (name, value)=>{\n        setFormData({\n            ...formData,\n            [name]: value\n        });\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // Frontend validation\n        if (!formData.title || formData.title.trim().length < 3) {\n            alert(\"عنوان المناقصة يجب أن يكون 3 أحرف على الأقل\");\n            return;\n        }\n        if (!formData.description || formData.description.trim().length < 10) {\n            alert(\"وصف المناقصة يجب أن يكون 10 أحرف على الأقل\");\n            return;\n        }\n        if (!formData.category) {\n            alert(\"يرجى اختيار فئة المناقصة\");\n            return;\n        }\n        if (!formData.budget || parseInt(formData.budget) <= 0) {\n            alert(\"يرجى إدخال ميزانية صحيحة\");\n            return;\n        }\n        if (!formData.submissionDeadline) {\n            alert(\"يرجى تحديد آخر موعد لتقديم العطاءات\");\n            return;\n        }\n        if (!formData.requirements || formData.requirements.trim().length === 0) {\n            alert(\"يرجى إدخال متطلبات المتقدمين\");\n            return;\n        }\n        // Prepare requirements array\n        const requirementsArray = formData.requirements.split(\"\\n\").filter((req)=>req.trim() !== \"\");\n        if (requirementsArray.length === 0) {\n            alert(\"يجب إدخال متطلب واحد على الأقل\");\n            return;\n        }\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.tenderAPI.create({\n                title: formData.title.trim(),\n                description: formData.description.trim(),\n                category: categoryMapping[formData.category] || \"other\",\n                budget: parseInt(formData.budget),\n                startDate: formData.startDate || new Date().toISOString(),\n                deadline: formData.submissionDeadline,\n                location: {\n                    country: \"Saudi Arabia\"\n                },\n                requirements: requirementsArray\n            });\n            console.log(\"Tender created:\", response.data);\n            alert(\"تم إنشاء المناقصة بنجاح!\");\n            // Reset form\n            setFormData({\n                title: \"\",\n                description: \"\",\n                budget: \"\",\n                category: \"\",\n                startDate: \"\",\n                submissionDeadline: \"\",\n                executionPeriod: \"\",\n                requirements: \"\",\n                specifications: \"\"\n            });\n        } catch (error) {\n            var _error_response_data, _error_response, _error_response_data1, _error_response1;\n            console.error(\"Error creating tender:\", error);\n            let errorMessage = \"خطأ غير معروف\";\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.errors) && Array.isArray(error.response.data.errors)) {\n                // Handle validation errors\n                const validationErrors = error.response.data.errors.map((err)=>err.msg).join(\"\\n\");\n                errorMessage = \"أخطاء في البيانات:\\n\".concat(validationErrors);\n            } else if ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data1 = _error_response1.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.message) {\n                errorMessage = error.response.data.message;\n            } else if (error.message) {\n                errorMessage = error.message;\n            }\n            alert(\"حدث خطأ في إنشاء المناقصة:\\n\" + errorMessage);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold\",\n                            children: \"إنشاء مناقصة جديدة\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"املأ المعلومات التالية لإنشاء مناقصة حكومية جديدة\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"المعلومات الأساسية\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                            htmlFor: \"title\",\n                                                            children: \"عنوان المناقصة *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                            lineNumber: 152,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                            id: \"title\",\n                                                            name: \"title\",\n                                                            value: formData.title,\n                                                            onChange: handleChange,\n                                                            placeholder: \"أدخل عنوان المناقصة\",\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                            lineNumber: 153,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                            htmlFor: \"category\",\n                                                            children: \"فئة المناقصة *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                            lineNumber: 163,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                            onValueChange: (value)=>handleSelectChange(\"category\", value),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                                        placeholder: \"اختر فئة المناقصة\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                                        lineNumber: 166,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                                    lineNumber: 165,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                            value: \"construction\",\n                                                                            children: \"إنشاءات ومقاولات\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                                            lineNumber: 169,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                            value: \"supplies\",\n                                                                            children: \"توريد مواد\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                                            lineNumber: 170,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                            value: \"services\",\n                                                                            children: \"خدمات\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                                            lineNumber: 171,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                            value: \"consulting\",\n                                                                            children: \"استشارات\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                                            lineNumber: 172,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                            value: \"maintenance\",\n                                                                            children: \"صيانة\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                                            lineNumber: 173,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                            value: \"technology\",\n                                                                            children: \"تقنية معلومات\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                                            lineNumber: 174,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                                    lineNumber: 168,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                            htmlFor: \"budget\",\n                                                            children: \"الميزانية المتوقعة (ر.س) *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                            lineNumber: 179,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                            id: \"budget\",\n                                                            name: \"budget\",\n                                                            type: \"number\",\n                                                            value: formData.budget,\n                                                            onChange: handleChange,\n                                                            placeholder: \"أدخل الميزانية المتوقعة\",\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                            lineNumber: 180,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                            htmlFor: \"executionPeriod\",\n                                                            children: \"مدة التنفيذ (بالأشهر) *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                            lineNumber: 191,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                            id: \"executionPeriod\",\n                                                            name: \"executionPeriod\",\n                                                            type: \"number\",\n                                                            value: formData.executionPeriod,\n                                                            onChange: handleChange,\n                                                            placeholder: \"أدخل مدة التنفيذ\",\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                            lineNumber: 192,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"description\",\n                                                    children: \"وصف المناقصة *\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    id: \"description\",\n                                                    name: \"description\",\n                                                    value: formData.description,\n                                                    onChange: handleChange,\n                                                    className: \"w-full h-32 p-3 border rounded-md resize-none\",\n                                                    placeholder: \"اكتب وصفاً مفصلاً للمناقصة والأعمال المطلوبة\",\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"التوقيتات المهمة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"startDate\",\n                                                        children: \"تاريخ بداية المناقصة *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"startDate\",\n                                                        name: \"startDate\",\n                                                        type: \"datetime-local\",\n                                                        value: formData.startDate,\n                                                        onChange: handleChange,\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"submissionDeadline\",\n                                                        children: \"آخر موعد لتقديم العطاءات *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"submissionDeadline\",\n                                                        name: \"submissionDeadline\",\n                                                        type: \"datetime-local\",\n                                                        value: formData.submissionDeadline,\n                                                        onChange: handleChange,\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"المتطلبات والمواصفات\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"requirements\",\n                                                    children: \"متطلبات المتقدمين *\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    id: \"requirements\",\n                                                    name: \"requirements\",\n                                                    value: formData.requirements,\n                                                    onChange: handleChange,\n                                                    className: \"w-full h-32 p-3 border rounded-md resize-none\",\n                                                    placeholder: \"اذكر المتطلبات الواجب توفرها في الشركات المتقدمة (التراخيص، الخبرة، إلخ)\",\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"specifications\",\n                                                    children: \"المواصفات الفنية\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    id: \"specifications\",\n                                                    name: \"specifications\",\n                                                    value: formData.specifications,\n                                                    onChange: handleChange,\n                                                    className: \"w-full h-32 p-3 border rounded-md resize-none\",\n                                                    placeholder: \"اذكر المواصفات الفنية التفصيلية للمشروع أو الخدمة المطلوبة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-4 space-x-reverse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    type: \"button\",\n                                    variant: \"outline\",\n                                    children: \"حفظ كمسودة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    type: \"submit\",\n                                    children: \"نشر المناقصة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n            lineNumber: 134,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n_s(CreateTenderPage, \"rBqNklUhb+M6fsMMaGPvX/kjVws=\");\n_c = CreateTenderPage;\nvar _c;\n$RefreshReg$(_c, \"CreateTenderPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/government/create-tender/page.tsx\n"));

/***/ })

});