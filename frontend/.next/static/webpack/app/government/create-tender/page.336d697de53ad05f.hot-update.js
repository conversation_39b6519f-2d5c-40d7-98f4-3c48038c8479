"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/government/create-tender/page",{

/***/ "(app-pages-browser)/./components/Sidebar.tsx":
/*!********************************!*\
  !*** ./components/Sidebar.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Sidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gavel.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst navItems = [\n    // Admin and Super Admin navigation\n    {\n        href: \"/admin/dashboard\",\n        label: \"لوحة التحكم\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 44,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"admin\",\n            \"super_admin\"\n        ]\n    },\n    {\n        href: \"/admin/pending-accounts\",\n        label: \"الحسابات المعلقة\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 50,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"admin\",\n            \"super_admin\"\n        ]\n    },\n    {\n        href: \"/admin/users\",\n        label: \"إدارة المستخدمين\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 56,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"admin\",\n            \"super_admin\"\n        ]\n    },\n    {\n        href: \"/admin/auctions\",\n        label: \"إدارة المزادات\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 62,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"admin\",\n            \"super_admin\"\n        ]\n    },\n    {\n        href: \"/admin/tenders\",\n        label: \"إدارة المناقصات\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 68,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"admin\",\n            \"super_admin\"\n        ]\n    },\n    {\n        href: \"/admin/create-tender\",\n        label: \"إنشاء مناقصة\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 74,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"admin\",\n            \"super_admin\"\n        ]\n    },\n    {\n        href: \"/admin/settings\",\n        label: \"الإعدادات\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 80,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"super_admin\"\n        ]\n    },\n    // Company navigation\n    {\n        href: \"/company/dashboard\",\n        label: \"لوحة التحكم\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 88,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"company\"\n        ]\n    },\n    {\n        href: \"/company/auctions\",\n        label: \"مزاداتي\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 94,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"company\"\n        ]\n    },\n    {\n        href: \"/company/tenders\",\n        label: \"مناقصاتي\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 100,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"company\"\n        ]\n    },\n    {\n        href: \"/company/create-auction\",\n        label: \"إنشاء مزاد\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 106,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"company\"\n        ]\n    },\n    {\n        href: \"/company/bids\",\n        label: \"عطاءاتي\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 112,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"company\"\n        ]\n    },\n    {\n        href: \"/company/notifications\",\n        label: \"الإشعارات\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 118,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"company\"\n        ]\n    },\n    {\n        href: \"/company/profile\",\n        label: \"الملف الشخصي\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 124,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"company\"\n        ]\n    },\n    // Individual user navigation\n    {\n        href: \"/user/dashboard\",\n        label: \"لوحة التحكم\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 132,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"individual\"\n        ]\n    },\n    {\n        href: \"/user/leaderboard\",\n        label: \"لوحة الصدارة\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 138,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"individual\"\n        ]\n    },\n    {\n        href: \"/user/auctions\",\n        label: \"المزادات المتاحة\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 144,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"individual\"\n        ]\n    },\n    {\n        href: \"/user/my-bids\",\n        label: \"مزايداتي\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 150,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"individual\"\n        ]\n    },\n    {\n        href: \"/user/notifications\",\n        label: \"الإشعارات\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 156,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"individual\"\n        ]\n    },\n    {\n        href: \"/user/profile\",\n        label: \"الملف الشخصي\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 162,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"individual\"\n        ]\n    },\n    // Government navigation\n    {\n        href: \"/government/dashboard\",\n        label: \"لوحة التحكم\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 170,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"government\"\n        ]\n    },\n    {\n        href: \"/government/tenders\",\n        label: \"مناقصاتي\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 176,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"government\"\n        ]\n    },\n    {\n        href: \"/government/create-tender\",\n        label: \"إنشاء مناقصة\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 182,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"government\"\n        ]\n    },\n    {\n        href: \"/government/applications\",\n        label: \"طلبات المشاركة\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 188,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"government\"\n        ]\n    },\n    {\n        href: \"/government/notifications\",\n        label: \"الإشعارات\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 194,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"government\"\n        ]\n    },\n    {\n        href: \"/government/profile\",\n        label: \"الملف الشخصي\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 200,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"government\"\n        ]\n    }\n];\nfunction Sidebar(param) {\n    let { userRole } = param;\n    var _user_profile, _user_profile1, _user_profile2;\n    _s();\n    const [isCollapsed, setIsCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const userData = localStorage.getItem(\"user\");\n        if (userData) {\n            setUser(JSON.parse(userData));\n        }\n    }, []);\n    const handleLogout = ()=>{\n        localStorage.removeItem(\"token\");\n        localStorage.removeItem(\"user\");\n        router.push(\"/auth/login\");\n    };\n    const roleIcon = ()=>{\n        switch(user === null || user === void 0 ? void 0 : user.role){\n            case \"super_admin\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                    className: \"h-6 w-6 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 16\n                }, this);\n            case \"admin\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-6 w-6 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 16\n                }, this);\n            case \"company\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    className: \"h-6 w-6 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 16\n                }, this);\n            case \"government\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-6 w-6 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 16\n                }, this);\n            case \"individual\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    className: \"h-6 w-6 text-purple-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                    lineNumber: 235,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    className: \"h-6 w-6\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                    lineNumber: 237,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getRoleLabel = (role)=>{\n        switch(role){\n            case \"super_admin\":\n                return \"مدير عام\";\n            case \"admin\":\n                return \"مدير\";\n            case \"company\":\n                return \"شركة\";\n            case \"government\":\n                return \"جهة حكومية\";\n            case \"individual\":\n                return \"فرد\";\n            default:\n                return role;\n        }\n    };\n    const filteredNavItems = navItems.filter((item)=>item.roles.includes((user === null || user === void 0 ? void 0 : user.role) || \"\"));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(isCollapsed ? \"w-16\" : \"w-56\", \" transition-all duration-300 backdrop-blur-xl bg-white/90 border-r border-white/20 shadow-xl flex flex-col h-screen relative z-10\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-gray-200/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: \"/\",\n                            className: \"flex items-center gap-3 group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 rounded-xl flex items-center justify-center group-hover:scale-110 group-hover:rotate-3 transition-all duration-300 shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"w-4 h-4 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-base font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent\",\n                                            children: \"المنصة\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-600 font-medium\",\n                                            children: \"المزادات والمناقصات\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 13\n                        }, this),\n                        isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 rounded-xl flex items-center justify-center shadow-lg mx-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"w-4 h-4 text-white\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            onClick: ()=>setIsCollapsed(!isCollapsed),\n                            className: \"p-1.5 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors duration-200\",\n                            children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                className: \"h-3 w-3\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 28\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                className: \"h-3 w-3\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 66\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                    lineNumber: 266,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                lineNumber: 265,\n                columnNumber: 7\n            }, this),\n            user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-4 border-b border-gray-200/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center \".concat(isCollapsed ? \"justify-center\" : \"gap-3\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 rounded-xl flex items-center justify-center shadow-lg\",\n                            children: roleIcon()\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 13\n                        }, this),\n                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-bold text-gray-900 truncate\",\n                                    children: ((_user_profile = user.profile) === null || _user_profile === void 0 ? void 0 : _user_profile.fullName) || ((_user_profile1 = user.profile) === null || _user_profile1 === void 0 ? void 0 : _user_profile1.companyName) || ((_user_profile2 = user.profile) === null || _user_profile2 === void 0 ? void 0 : _user_profile2.governmentEntity) || \"المدير\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-600 font-medium truncate\",\n                                    children: getRoleLabel(user.role)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-1.5 mt-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: \"متصل\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                            lineNumber: 302,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                    lineNumber: 297,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                lineNumber: 296,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex-1 px-4 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: filteredNavItems.map((item)=>{\n                        const isActive = pathname === item.href;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: item.href,\n                            className: \"group flex items-center \".concat(isCollapsed ? \"justify-center px-2 py-3\" : \"gap-3 px-4 py-3\", \" rounded-xl transition-all duration-300 relative overflow-hidden \").concat(isActive ? \"bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-indigo-500/10 border border-blue-300/30 shadow-md backdrop-blur-sm\" : \"hover:bg-white/60 hover:shadow-md hover:scale-[1.02] border border-transparent\"),\n                            children: [\n                                isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"\".concat(isCollapsed ? \"w-6 h-6\" : \"w-8 h-8\", \" rounded-lg flex items-center justify-center transition-all duration-300 relative z-10 \").concat(isActive ? \"bg-gradient-to-br from-blue-500 to-purple-600 shadow-lg scale-105\" : \"bg-gray-100 group-hover:bg-gradient-to-br group-hover:from-gray-200 group-hover:to-gray-300\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"transition-all duration-300 \".concat(isActive ? \"text-white scale-105\" : \"text-gray-600 group-hover:text-gray-700\"),\n                                        children: item.icon\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 17\n                                }, this),\n                                !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-semibold transition-all duration-300 \".concat(isActive ? \"text-gray-900\" : \"text-gray-700 group-hover:text-gray-900\"),\n                                            children: item.label\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 21\n                                        }, this),\n                                        isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-1.5 h-1.5 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mt-0.5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 19\n                                }, this),\n                                isActive && !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    className: \"w-4 h-4 text-blue-600 mr-auto relative z-10\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, item.href, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                    lineNumber: 321,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                lineNumber: 320,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 pb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-gray-200/50 pt-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        onClick: handleLogout,\n                        variant: \"ghost\",\n                        className: \"w-full group flex items-center \".concat(isCollapsed ? \"justify-center px-2 py-3\" : \"gap-3 px-4 py-3\", \" rounded-xl hover:bg-red-50/80 hover:shadow-md transition-all duration-300 border border-transparent hover:border-red-200/50\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat(isCollapsed ? \"w-6 h-6\" : \"w-8 h-8\", \" rounded-lg bg-red-100 group-hover:bg-gradient-to-br group-hover:from-red-500 group-hover:to-pink-500 flex items-center justify-center transition-all duration-300\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                    className: \"\".concat(isCollapsed ? \"w-3 h-3\" : \"w-4 h-4\", \" text-red-600 group-hover:text-white transition-all duration-300\")\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                    lineNumber: 384,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                lineNumber: 383,\n                                columnNumber: 13\n                            }, this),\n                            !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-semibold text-red-700 group-hover:text-red-800 transition-all duration-300\",\n                                children: \"تسجيل الخروج\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                        lineNumber: 378,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                    lineNumber: 377,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                lineNumber: 376,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n        lineNumber: 263,\n        columnNumber: 5\n    }, this);\n}\n_s(Sidebar, \"8D+h9wKtrXb9BO+L1pAtHWiT9Do=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/Sidebar.tsx\n"));

/***/ })

});