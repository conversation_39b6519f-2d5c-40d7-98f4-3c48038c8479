"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/government/create-tender/page",{

/***/ "(app-pages-browser)/./app/government/create-tender/page.tsx":
/*!***********************************************!*\
  !*** ./app/government/create-tender/page.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CreateTenderPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,FileText!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,FileText!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,FileText!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction CreateTenderPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        description: \"\",\n        budget: \"\",\n        category: \"\",\n        startDate: \"\",\n        submissionDeadline: \"\",\n        executionPeriod: \"\",\n        requirements: \"\",\n        specifications: \"\"\n    });\n    // Category mapping from Arabic to English\n    const categoryMapping = {\n        \"إنشاءات ومقاولات\": \"construction\",\n        \"توريد مواد\": \"manufacturing\",\n        \"خدمات\": \"other\",\n        \"استشارات\": \"consulting\",\n        \"صيانة\": \"other\",\n        \"تقنية معلومات\": \"it_technology\"\n    };\n    const handleChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData({\n            ...formData,\n            [name]: value\n        });\n    };\n    const handleSelectChange = (name, value)=>{\n        setFormData({\n            ...formData,\n            [name]: value\n        });\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // Frontend validation\n        if (!formData.title || formData.title.trim().length < 3) {\n            alert(\"عنوان المناقصة يجب أن يكون 3 أحرف على الأقل\");\n            return;\n        }\n        if (!formData.description || formData.description.trim().length < 10) {\n            alert(\"وصف المناقصة يجب أن يكون 10 أحرف على الأقل\");\n            return;\n        }\n        if (!formData.category) {\n            alert(\"يرجى اختيار فئة المناقصة\");\n            return;\n        }\n        if (!formData.budget || parseInt(formData.budget) <= 0) {\n            alert(\"يرجى إدخال ميزانية صحيحة\");\n            return;\n        }\n        if (!formData.submissionDeadline) {\n            alert(\"يرجى تحديد آخر موعد لتقديم العطاءات\");\n            return;\n        }\n        if (!formData.requirements || formData.requirements.trim().length === 0) {\n            alert(\"يرجى إدخال متطلبات المتقدمين\");\n            return;\n        }\n        // Prepare requirements array\n        const requirementsArray = formData.requirements.split(\"\\n\").filter((req)=>req.trim() !== \"\");\n        if (requirementsArray.length === 0) {\n            alert(\"يجب إدخال متطلب واحد على الأقل\");\n            return;\n        }\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.tenderAPI.create({\n                title: formData.title.trim(),\n                description: formData.description.trim(),\n                category: categoryMapping[formData.category] || \"other\",\n                budget: parseInt(formData.budget),\n                startDate: formData.startDate || new Date().toISOString(),\n                deadline: formData.submissionDeadline,\n                location: {\n                    country: \"Saudi Arabia\"\n                },\n                requirements: requirementsArray\n            });\n            console.log(\"Tender created:\", response.data);\n            alert(\"تم إنشاء المناقصة بنجاح!\");\n            // Reset form\n            setFormData({\n                title: \"\",\n                description: \"\",\n                budget: \"\",\n                category: \"\",\n                startDate: \"\",\n                submissionDeadline: \"\",\n                executionPeriod: \"\",\n                requirements: \"\",\n                specifications: \"\"\n            });\n        } catch (error) {\n            var _error_response_data, _error_response, _error_response_data1, _error_response1;\n            console.error(\"Error creating tender:\", error);\n            let errorMessage = \"خطأ غير معروف\";\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.errors) && Array.isArray(error.response.data.errors)) {\n                // Handle validation errors\n                const validationErrors = error.response.data.errors.map((err)=>err.msg).join(\"\\n\");\n                errorMessage = \"أخطاء في البيانات:\\n\".concat(validationErrors);\n            } else if ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data1 = _error_response1.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.message) {\n                errorMessage = error.response.data.message;\n            } else if (error.message) {\n                errorMessage = error.message;\n            }\n            alert(\"حدث خطأ في إنشاء المناقصة:\\n\" + errorMessage);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold\",\n                            children: \"إنشاء مناقصة جديدة\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"املأ المعلومات التالية لإنشاء مناقصة حكومية جديدة\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"المعلومات الأساسية\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                            htmlFor: \"title\",\n                                                            children: \"عنوان المناقصة *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                            lineNumber: 156,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            id: \"title\",\n                                                            name: \"title\",\n                                                            value: formData.title,\n                                                            onChange: handleChange,\n                                                            placeholder: \"أدخل عنوان المناقصة\",\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                            lineNumber: 157,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                            htmlFor: \"category\",\n                                                            children: \"فئة المناقصة *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                            onValueChange: (value)=>handleSelectChange(\"category\", value),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                        placeholder: \"اختر فئة المناقصة\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                                        lineNumber: 170,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                                    lineNumber: 169,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: \"construction\",\n                                                                            children: \"إنشاءات ومقاولات\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                                            lineNumber: 173,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: \"supplies\",\n                                                                            children: \"توريد مواد\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                                            lineNumber: 174,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: \"services\",\n                                                                            children: \"خدمات\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                                            lineNumber: 175,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: \"consulting\",\n                                                                            children: \"استشارات\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                                            lineNumber: 176,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: \"maintenance\",\n                                                                            children: \"صيانة\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                                            lineNumber: 177,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: \"technology\",\n                                                                            children: \"تقنية معلومات\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                                            lineNumber: 178,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                                    lineNumber: 172,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                            lineNumber: 168,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                            htmlFor: \"budget\",\n                                                            children: \"الميزانية المتوقعة (ر.س) *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                            lineNumber: 183,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            id: \"budget\",\n                                                            name: \"budget\",\n                                                            type: \"number\",\n                                                            value: formData.budget,\n                                                            onChange: handleChange,\n                                                            placeholder: \"أدخل الميزانية المتوقعة\",\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                            lineNumber: 184,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                            htmlFor: \"executionPeriod\",\n                                                            children: \"مدة التنفيذ (بالأشهر) *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                            lineNumber: 195,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            id: \"executionPeriod\",\n                                                            name: \"executionPeriod\",\n                                                            type: \"number\",\n                                                            value: formData.executionPeriod,\n                                                            onChange: handleChange,\n                                                            placeholder: \"أدخل مدة التنفيذ\",\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                            lineNumber: 196,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"description\",\n                                                    children: \"وصف المناقصة *\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    id: \"description\",\n                                                    name: \"description\",\n                                                    value: formData.description,\n                                                    onChange: handleChange,\n                                                    className: \"w-full h-32 p-3 border rounded-md resize-none\",\n                                                    placeholder: \"اكتب وصفاً مفصلاً للمناقصة والأعمال المطلوبة\",\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"التوقيتات المهمة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: \"startDate\",\n                                                        children: \"تاريخ بداية المناقصة *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        id: \"startDate\",\n                                                        name: \"startDate\",\n                                                        type: \"datetime-local\",\n                                                        value: formData.startDate,\n                                                        onChange: handleChange,\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: \"submissionDeadline\",\n                                                        children: \"آخر موعد لتقديم العطاءات *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                        lineNumber: 245,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        id: \"submissionDeadline\",\n                                                        name: \"submissionDeadline\",\n                                                        type: \"datetime-local\",\n                                                        value: formData.submissionDeadline,\n                                                        onChange: handleChange,\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"المتطلبات والمواصفات\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"requirements\",\n                                                    children: \"متطلبات المتقدمين *\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    id: \"requirements\",\n                                                    name: \"requirements\",\n                                                    value: formData.requirements,\n                                                    onChange: handleChange,\n                                                    className: \"w-full h-32 p-3 border rounded-md resize-none\",\n                                                    placeholder: \"اذكر المتطلبات الواجب توفرها في الشركات المتقدمة (التراخيص، الخبرة، إلخ)\",\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"specifications\",\n                                                    children: \"المواصفات الفنية\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    id: \"specifications\",\n                                                    name: \"specifications\",\n                                                    value: formData.specifications,\n                                                    onChange: handleChange,\n                                                    className: \"w-full h-32 p-3 border rounded-md resize-none\",\n                                                    placeholder: \"اذكر المواصفات الفنية التفصيلية للمشروع أو الخدمة المطلوبة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-4 space-x-reverse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    type: \"button\",\n                                    variant: \"outline\",\n                                    children: \"حفظ كمسودة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    type: \"submit\",\n                                    children: \"نشر المناقصة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n            lineNumber: 138,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n_s(CreateTenderPage, \"x1YeKibBVZwiqCWlk4P6SlQKBWM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast\n    ];\n});\n_c = CreateTenderPage;\nvar _c;\n$RefreshReg$(_c, \"CreateTenderPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/government/create-tender/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/ui/use-toast.tsx":
/*!*************************************!*\
  !*** ./components/ui/use-toast.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useToast: function() { return /* binding */ useToast; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\nvar _s = $RefreshSig$();\n\nconst initialState = {\n    toasts: []\n};\n// Global toast state\nlet globalToastState = initialState;\nlet listeners = [];\nfunction updateGlobalState(newState) {\n    globalToastState = newState;\n    listeners.forEach((listener)=>listener(newState));\n}\nfunction useToast() {\n    _s();\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(globalToastState);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        listeners.push(setState);\n        return ()=>{\n            listeners = listeners.filter((listener)=>listener !== setState);\n        };\n    }, []);\n    const toast = (param)=>{\n        let { ...props } = param;\n        console.log(\"Toast called with:\", props);\n        const id = Math.random().toString(36).substr(2, 9);\n        const newToast = {\n            ...props,\n            id\n        };\n        const newState = {\n            ...globalToastState,\n            toasts: [\n                ...globalToastState.toasts,\n                newToast\n            ]\n        };\n        console.log(\"Updating toast state with:\", newState);\n        updateGlobalState(newState);\n        // Auto remove after 8 seconds\n        setTimeout(()=>{\n            const updatedState = {\n                ...globalToastState,\n                toasts: globalToastState.toasts.filter((t)=>t.id !== id)\n            };\n            updateGlobalState(updatedState);\n        }, 8000);\n        return {\n            id,\n            dismiss: ()=>{\n                const updatedState = {\n                    ...globalToastState,\n                    toasts: globalToastState.toasts.filter((t)=>t.id !== id)\n                };\n                updateGlobalState(updatedState);\n            }\n        };\n    };\n    return {\n        toast,\n        toasts: state.toasts\n    };\n}\n_s(useToast, \"toGSHV5dBkr+WXotezLKPjsOdJ8=\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ui/use-toast.tsx\n"));

/***/ })

});