"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/government/create-tender/page",{

/***/ "(app-pages-browser)/./app/government/create-tender/page.tsx":
/*!***********************************************!*\
  !*** ./app/government/create-tender/page.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CreateTenderPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./components/DashboardLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,FileText!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,FileText!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,FileText!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction CreateTenderPage() {\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        description: \"\",\n        budget: \"\",\n        category: \"\",\n        startDate: \"\",\n        submissionDeadline: \"\",\n        executionPeriod: \"\",\n        requirements: \"\",\n        specifications: \"\"\n    });\n    // Category mapping from Arabic to English\n    const categoryMapping = {\n        \"إنشاءات ومقاولات\": \"construction\",\n        \"توريد مواد\": \"manufacturing\",\n        \"خدمات\": \"other\",\n        \"استشارات\": \"consulting\",\n        \"صيانة\": \"other\",\n        \"تقنية معلومات\": \"it_technology\"\n    };\n    const handleChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData({\n            ...formData,\n            [name]: value\n        });\n    };\n    const handleSelectChange = (name, value)=>{\n        setFormData({\n            ...formData,\n            [name]: value\n        });\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.tenderAPI.create({\n                title: formData.title,\n                description: formData.description,\n                category: formData.category,\n                budget: parseInt(formData.budget),\n                duration: parseInt(formData.duration),\n                submissionDeadline: formData.submissionDeadline,\n                requirements: formData.requirements,\n                technicalSpecs: formData.technicalSpecs\n            });\n            console.log(\"Tender created:\", response.data);\n            alert(\"تم إنشاء المناقصة بنجاح!\");\n            // Reset form\n            setFormData({\n                title: \"\",\n                description: \"\",\n                category: \"\",\n                budget: \"\",\n                duration: \"\",\n                submissionDeadline: \"\",\n                requirements: \"\",\n                technicalSpecs: \"\"\n            });\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error creating tender:\", error);\n            const errorMessage = ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || error.message || \"خطأ غير معروف\";\n            alert(\"حدث خطأ في إنشاء المناقصة: \" + errorMessage);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n        allowedRoles: [\n            \"government\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold\",\n                            children: \"إنشاء مناقصة جديدة\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"املأ المعلومات التالية لإنشاء مناقصة حكومية جديدة\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"المعلومات الأساسية\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                            htmlFor: \"title\",\n                                                            children: \"عنوان المناقصة *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                            lineNumber: 101,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                            id: \"title\",\n                                                            name: \"title\",\n                                                            value: formData.title,\n                                                            onChange: handleChange,\n                                                            placeholder: \"أدخل عنوان المناقصة\",\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                            lineNumber: 102,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 100,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                            htmlFor: \"category\",\n                                                            children: \"فئة المناقصة *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                            lineNumber: 112,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                            onValueChange: (value)=>handleSelectChange(\"category\", value),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                                        placeholder: \"اختر فئة المناقصة\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                                        lineNumber: 115,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                                    lineNumber: 114,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                            value: \"construction\",\n                                                                            children: \"إنشاءات ومقاولات\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                                            lineNumber: 118,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                            value: \"supplies\",\n                                                                            children: \"توريد مواد\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                                            lineNumber: 119,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                            value: \"services\",\n                                                                            children: \"خدمات\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                                            lineNumber: 120,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                            value: \"consulting\",\n                                                                            children: \"استشارات\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                                            lineNumber: 121,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                            value: \"maintenance\",\n                                                                            children: \"صيانة\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                                            lineNumber: 122,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                            value: \"technology\",\n                                                                            children: \"تقنية معلومات\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                                            lineNumber: 123,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                                    lineNumber: 117,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                            lineNumber: 113,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                            htmlFor: \"budget\",\n                                                            children: \"الميزانية المتوقعة (ر.س) *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                            lineNumber: 128,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                            id: \"budget\",\n                                                            name: \"budget\",\n                                                            type: \"number\",\n                                                            value: formData.budget,\n                                                            onChange: handleChange,\n                                                            placeholder: \"أدخل الميزانية المتوقعة\",\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                            lineNumber: 129,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                            htmlFor: \"executionPeriod\",\n                                                            children: \"مدة التنفيذ (بالأشهر) *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                            lineNumber: 140,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                            id: \"executionPeriod\",\n                                                            name: \"executionPeriod\",\n                                                            type: \"number\",\n                                                            value: formData.executionPeriod,\n                                                            onChange: handleChange,\n                                                            placeholder: \"أدخل مدة التنفيذ\",\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                            lineNumber: 141,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"description\",\n                                                    children: \"وصف المناقصة *\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    id: \"description\",\n                                                    name: \"description\",\n                                                    value: formData.description,\n                                                    onChange: handleChange,\n                                                    className: \"w-full h-32 p-3 border rounded-md resize-none\",\n                                                    placeholder: \"اكتب وصفاً مفصلاً للمناقصة والأعمال المطلوبة\",\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"التوقيتات المهمة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"submissionDeadline\",\n                                                    children: \"آخر موعد لتقديم العطاءات *\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                    id: \"submissionDeadline\",\n                                                    name: \"submissionDeadline\",\n                                                    type: \"datetime-local\",\n                                                    value: formData.submissionDeadline,\n                                                    onChange: handleChange,\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"المتطلبات والمواصفات\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"requirements\",\n                                                    children: \"متطلبات المتقدمين *\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    id: \"requirements\",\n                                                    name: \"requirements\",\n                                                    value: formData.requirements,\n                                                    onChange: handleChange,\n                                                    className: \"w-full h-32 p-3 border rounded-md resize-none\",\n                                                    placeholder: \"اذكر المتطلبات الواجب توفرها في الشركات المتقدمة (التراخيص، الخبرة، إلخ)\",\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"specifications\",\n                                                    children: \"المواصفات الفنية\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    id: \"specifications\",\n                                                    name: \"specifications\",\n                                                    value: formData.specifications,\n                                                    onChange: handleChange,\n                                                    className: \"w-full h-32 p-3 border rounded-md resize-none\",\n                                                    placeholder: \"اذكر المواصفات الفنية التفصيلية للمشروع أو الخدمة المطلوبة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-4 space-x-reverse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    type: \"button\",\n                                    variant: \"outline\",\n                                    children: \"حفظ كمسودة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    type: \"submit\",\n                                    children: \"نشر المناقصة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n            lineNumber: 83,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n        lineNumber: 82,\n        columnNumber: 5\n    }, this);\n}\n_s(CreateTenderPage, \"rBqNklUhb+M6fsMMaGPvX/kjVws=\");\n_c = CreateTenderPage;\nvar _c;\n$RefreshReg$(_c, \"CreateTenderPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/government/create-tender/page.tsx\n"));

/***/ })

});