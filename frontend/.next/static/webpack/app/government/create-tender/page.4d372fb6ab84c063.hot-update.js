"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/government/create-tender/page",{

/***/ "(app-pages-browser)/./app/government/create-tender/page.tsx":
/*!***********************************************!*\
  !*** ./app/government/create-tender/page.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CreateTenderPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,FileText!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,FileText!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,FileText!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction CreateTenderPage() {\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        description: \"\",\n        budget: \"\",\n        category: \"\",\n        startDate: \"\",\n        submissionDeadline: \"\",\n        executionPeriod: \"\",\n        requirements: \"\",\n        specifications: \"\"\n    });\n    // Category mapping from Arabic to English\n    const categoryMapping = {\n        \"إنشاءات ومقاولات\": \"construction\",\n        \"توريد مواد\": \"manufacturing\",\n        \"خدمات\": \"other\",\n        \"استشارات\": \"consulting\",\n        \"صيانة\": \"other\",\n        \"تقنية معلومات\": \"it_technology\"\n    };\n    const handleChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData({\n            ...formData,\n            [name]: value\n        });\n    };\n    const handleSelectChange = (name, value)=>{\n        setFormData({\n            ...formData,\n            [name]: value\n        });\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.tenderAPI.create({\n                title: formData.title,\n                description: formData.description,\n                category: categoryMapping[formData.category] || \"other\",\n                budget: parseInt(formData.budget),\n                startDate: formData.startDate || new Date().toISOString(),\n                deadline: formData.submissionDeadline,\n                duration: parseInt(formData.executionPeriod),\n                requirements: formData.requirements.split(\"\\n\").filter((req)=>req.trim() !== \"\"),\n                technicalSpecs: formData.specifications\n            });\n            console.log(\"Tender created:\", response.data);\n            alert(\"تم إنشاء المناقصة بنجاح!\");\n            // Reset form\n            setFormData({\n                title: \"\",\n                description: \"\",\n                budget: \"\",\n                category: \"\",\n                startDate: \"\",\n                submissionDeadline: \"\",\n                executionPeriod: \"\",\n                requirements: \"\",\n                specifications: \"\"\n            });\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error creating tender:\", error);\n            const errorMessage = ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || error.message || \"خطأ غير معروف\";\n            alert(\"حدث خطأ في إنشاء المناقصة: \" + errorMessage);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DashboardLayout, {\n        allowedRoles: [\n            \"government\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold\",\n                            children: \"إنشاء مناقصة جديدة\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"املأ المعلومات التالية لإنشاء مناقصة حكومية جديدة\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"المعلومات الأساسية\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                            htmlFor: \"title\",\n                                                            children: \"عنوان المناقصة *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                            lineNumber: 103,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                            id: \"title\",\n                                                            name: \"title\",\n                                                            value: formData.title,\n                                                            onChange: handleChange,\n                                                            placeholder: \"أدخل عنوان المناقصة\",\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                            lineNumber: 104,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                            htmlFor: \"category\",\n                                                            children: \"فئة المناقصة *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                            lineNumber: 114,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                            onValueChange: (value)=>handleSelectChange(\"category\", value),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                                        placeholder: \"اختر فئة المناقصة\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                                        lineNumber: 117,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                                    lineNumber: 116,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                            value: \"construction\",\n                                                                            children: \"إنشاءات ومقاولات\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                                            lineNumber: 120,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                            value: \"supplies\",\n                                                                            children: \"توريد مواد\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                                            lineNumber: 121,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                            value: \"services\",\n                                                                            children: \"خدمات\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                                            lineNumber: 122,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                            value: \"consulting\",\n                                                                            children: \"استشارات\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                                            lineNumber: 123,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                            value: \"maintenance\",\n                                                                            children: \"صيانة\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                                            lineNumber: 124,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                            value: \"technology\",\n                                                                            children: \"تقنية معلومات\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                                            lineNumber: 125,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                                    lineNumber: 119,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                            lineNumber: 115,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                            htmlFor: \"budget\",\n                                                            children: \"الميزانية المتوقعة (ر.س) *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                            lineNumber: 130,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                            id: \"budget\",\n                                                            name: \"budget\",\n                                                            type: \"number\",\n                                                            value: formData.budget,\n                                                            onChange: handleChange,\n                                                            placeholder: \"أدخل الميزانية المتوقعة\",\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                            lineNumber: 131,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                            htmlFor: \"executionPeriod\",\n                                                            children: \"مدة التنفيذ (بالأشهر) *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                            lineNumber: 142,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                            id: \"executionPeriod\",\n                                                            name: \"executionPeriod\",\n                                                            type: \"number\",\n                                                            value: formData.executionPeriod,\n                                                            onChange: handleChange,\n                                                            placeholder: \"أدخل مدة التنفيذ\",\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                            lineNumber: 143,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"description\",\n                                                    children: \"وصف المناقصة *\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    id: \"description\",\n                                                    name: \"description\",\n                                                    value: formData.description,\n                                                    onChange: handleChange,\n                                                    className: \"w-full h-32 p-3 border rounded-md resize-none\",\n                                                    placeholder: \"اكتب وصفاً مفصلاً للمناقصة والأعمال المطلوبة\",\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"التوقيتات المهمة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"startDate\",\n                                                        children: \"تاريخ بداية المناقصة *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"startDate\",\n                                                        name: \"startDate\",\n                                                        type: \"datetime-local\",\n                                                        value: formData.startDate,\n                                                        onChange: handleChange,\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"submissionDeadline\",\n                                                        children: \"آخر موعد لتقديم العطاءات *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"submissionDeadline\",\n                                                        name: \"submissionDeadline\",\n                                                        type: \"datetime-local\",\n                                                        value: formData.submissionDeadline,\n                                                        onChange: handleChange,\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"المتطلبات والمواصفات\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"requirements\",\n                                                    children: \"متطلبات المتقدمين *\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    id: \"requirements\",\n                                                    name: \"requirements\",\n                                                    value: formData.requirements,\n                                                    onChange: handleChange,\n                                                    className: \"w-full h-32 p-3 border rounded-md resize-none\",\n                                                    placeholder: \"اذكر المتطلبات الواجب توفرها في الشركات المتقدمة (التراخيص، الخبرة، إلخ)\",\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"specifications\",\n                                                    children: \"المواصفات الفنية\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    id: \"specifications\",\n                                                    name: \"specifications\",\n                                                    value: formData.specifications,\n                                                    onChange: handleChange,\n                                                    className: \"w-full h-32 p-3 border rounded-md resize-none\",\n                                                    placeholder: \"اذكر المواصفات الفنية التفصيلية للمشروع أو الخدمة المطلوبة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-4 space-x-reverse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    type: \"button\",\n                                    variant: \"outline\",\n                                    children: \"حفظ كمسودة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    type: \"submit\",\n                                    children: \"نشر المناقصة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n            lineNumber: 85,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n        lineNumber: 84,\n        columnNumber: 5\n    }, this);\n}\n_s(CreateTenderPage, \"rBqNklUhb+M6fsMMaGPvX/kjVws=\");\n_c = CreateTenderPage;\nvar _c;\n$RefreshReg$(_c, \"CreateTenderPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/government/create-tender/page.tsx\n"));

/***/ })

});