"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/government/create-tender/page",{

/***/ "(app-pages-browser)/./app/government/create-tender/page.tsx":
/*!***********************************************!*\
  !*** ./app/government/create-tender/page.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CreateTenderPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,FileText!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,FileText!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,FileText!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction CreateTenderPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        description: \"\",\n        budget: \"\",\n        category: \"\",\n        startDate: \"\",\n        submissionDeadline: \"\",\n        executionPeriod: \"\",\n        requirements: \"\",\n        specifications: \"\"\n    });\n    // Category mapping from Arabic to English\n    const categoryMapping = {\n        \"إنشاءات ومقاولات\": \"construction\",\n        \"توريد مواد\": \"manufacturing\",\n        \"خدمات\": \"other\",\n        \"استشارات\": \"consulting\",\n        \"صيانة\": \"other\",\n        \"تقنية معلومات\": \"it_technology\"\n    };\n    const handleChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData({\n            ...formData,\n            [name]: value\n        });\n    };\n    const handleSelectChange = (name, value)=>{\n        setFormData({\n            ...formData,\n            [name]: value\n        });\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // Frontend validation\n        if (!formData.title || formData.title.trim().length < 3) {\n            toast({\n                title: \"خطأ في العنوان\",\n                description: \"عنوان المناقصة يجب أن يكون 3 أحرف على الأقل\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        if (!formData.description || formData.description.trim().length < 10) {\n            toast({\n                title: \"خطأ في الوصف\",\n                description: \"وصف المناقصة يجب أن يكون 10 أحرف على الأقل\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        if (!formData.category) {\n            toast({\n                title: \"خطأ في الفئة\",\n                description: \"يرجى اختيار فئة المناقصة\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        if (!formData.budget || parseInt(formData.budget) <= 0) {\n            toast({\n                title: \"خطأ في الميزانية\",\n                description: \"يرجى إدخال ميزانية صحيحة\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        if (!formData.submissionDeadline) {\n            toast({\n                title: \"خطأ في التاريخ\",\n                description: \"يرجى تحديد آخر موعد لتقديم العطاءات\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        if (!formData.requirements || formData.requirements.trim().length === 0) {\n            toast({\n                title: \"خطأ في المتطلبات\",\n                description: \"يرجى إدخال متطلبات المتقدمين\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Prepare requirements array\n        const requirementsArray = formData.requirements.split(\"\\n\").filter((req)=>req.trim() !== \"\");\n        if (requirementsArray.length === 0) {\n            toast({\n                title: \"خطأ في المتطلبات\",\n                description: \"يجب إدخال متطلب واحد على الأقل\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.tenderAPI.create({\n                title: formData.title.trim(),\n                description: formData.description.trim(),\n                category: categoryMapping[formData.category] || \"other\",\n                budget: parseInt(formData.budget),\n                startDate: formData.startDate || new Date().toISOString(),\n                deadline: formData.submissionDeadline,\n                location: {\n                    country: \"Saudi Arabia\"\n                },\n                requirements: requirementsArray\n            });\n            console.log(\"Tender created:\", response.data);\n            // Show success message\n            toast({\n                title: \"تم إنشاء المناقصة\",\n                description: \"تم إنشاء المناقصة بنجاح وإضافتها إلى قائمة مناقصاتك\"\n            });\n            // Redirect to tenders list\n            router.push(\"/government/tenders\");\n        } catch (error) {\n            var _error_response_data, _error_response, _error_response_data1, _error_response1;\n            console.error(\"Error creating tender:\", error);\n            let errorMessage = \"خطأ غير معروف\";\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.errors) && Array.isArray(error.response.data.errors)) {\n                // Handle validation errors\n                const validationErrors = error.response.data.errors.map((err)=>err.msg).join(\", \");\n                errorMessage = \"أخطاء في البيانات: \".concat(validationErrors);\n            } else if ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data1 = _error_response1.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.message) {\n                errorMessage = error.response.data.message;\n            } else if (error.message) {\n                errorMessage = error.message;\n            }\n            toast({\n                title: \"خطأ في إنشاء المناقصة\",\n                description: errorMessage,\n                variant: \"destructive\"\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold\",\n                            children: \"إنشاء مناقصة جديدة\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"املأ المعلومات التالية لإنشاء مناقصة حكومية جديدة\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"المعلومات الأساسية\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                            htmlFor: \"title\",\n                                                            children: \"عنوان المناقصة *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                            lineNumber: 183,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            id: \"title\",\n                                                            name: \"title\",\n                                                            value: formData.title,\n                                                            onChange: handleChange,\n                                                            placeholder: \"أدخل عنوان المناقصة\",\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                            lineNumber: 184,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                            htmlFor: \"category\",\n                                                            children: \"فئة المناقصة *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                            lineNumber: 194,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                            onValueChange: (value)=>handleSelectChange(\"category\", value),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                        placeholder: \"اختر فئة المناقصة\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                                        lineNumber: 197,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                                    lineNumber: 196,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: \"construction\",\n                                                                            children: \"إنشاءات ومقاولات\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                                            lineNumber: 200,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: \"supplies\",\n                                                                            children: \"توريد مواد\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                                            lineNumber: 201,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: \"services\",\n                                                                            children: \"خدمات\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                                            lineNumber: 202,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: \"consulting\",\n                                                                            children: \"استشارات\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                                            lineNumber: 203,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: \"maintenance\",\n                                                                            children: \"صيانة\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                                            lineNumber: 204,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: \"technology\",\n                                                                            children: \"تقنية معلومات\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                                            lineNumber: 205,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                                    lineNumber: 199,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                            lineNumber: 195,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                            htmlFor: \"budget\",\n                                                            children: \"الميزانية المتوقعة (ر.س) *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                            lineNumber: 210,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            id: \"budget\",\n                                                            name: \"budget\",\n                                                            type: \"number\",\n                                                            value: formData.budget,\n                                                            onChange: handleChange,\n                                                            placeholder: \"أدخل الميزانية المتوقعة\",\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                            lineNumber: 211,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                            htmlFor: \"executionPeriod\",\n                                                            children: \"مدة التنفيذ (بالأشهر) *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                            lineNumber: 222,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            id: \"executionPeriod\",\n                                                            name: \"executionPeriod\",\n                                                            type: \"number\",\n                                                            value: formData.executionPeriod,\n                                                            onChange: handleChange,\n                                                            placeholder: \"أدخل مدة التنفيذ\",\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                            lineNumber: 223,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"description\",\n                                                    children: \"وصف المناقصة *\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    id: \"description\",\n                                                    name: \"description\",\n                                                    value: formData.description,\n                                                    onChange: handleChange,\n                                                    className: \"w-full h-32 p-3 border rounded-md resize-none\",\n                                                    placeholder: \"اكتب وصفاً مفصلاً للمناقصة والأعمال المطلوبة\",\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"التوقيتات المهمة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: \"startDate\",\n                                                        children: \"تاريخ بداية المناقصة *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        id: \"startDate\",\n                                                        name: \"startDate\",\n                                                        type: \"datetime-local\",\n                                                        value: formData.startDate,\n                                                        onChange: handleChange,\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: \"submissionDeadline\",\n                                                        children: \"آخر موعد لتقديم العطاءات *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        id: \"submissionDeadline\",\n                                                        name: \"submissionDeadline\",\n                                                        type: \"datetime-local\",\n                                                        value: formData.submissionDeadline,\n                                                        onChange: handleChange,\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"المتطلبات والمواصفات\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"requirements\",\n                                                    children: \"متطلبات المتقدمين *\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    id: \"requirements\",\n                                                    name: \"requirements\",\n                                                    value: formData.requirements,\n                                                    onChange: handleChange,\n                                                    className: \"w-full h-32 p-3 border rounded-md resize-none\",\n                                                    placeholder: \"اذكر المتطلبات الواجب توفرها في الشركات المتقدمة (التراخيص، الخبرة، إلخ)\",\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"specifications\",\n                                                    children: \"المواصفات الفنية\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    id: \"specifications\",\n                                                    name: \"specifications\",\n                                                    value: formData.specifications,\n                                                    onChange: handleChange,\n                                                    className: \"w-full h-32 p-3 border rounded-md resize-none\",\n                                                    placeholder: \"اذكر المواصفات الفنية التفصيلية للمشروع أو الخدمة المطلوبة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-4 space-x-reverse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    type: \"button\",\n                                    variant: \"outline\",\n                                    children: \"حفظ كمسودة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    type: \"submit\",\n                                    children: \"نشر المناقصة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/create-tender/page.tsx\",\n            lineNumber: 165,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n_s(CreateTenderPage, \"x1YeKibBVZwiqCWlk4P6SlQKBWM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast\n    ];\n});\n_c = CreateTenderPage;\nvar _c;\n$RefreshReg$(_c, \"CreateTenderPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/government/create-tender/page.tsx\n"));

/***/ })

});