"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/government/dashboard/page",{

/***/ "(app-pages-browser)/./app/government/dashboard/page.tsx":
/*!*******************************************!*\
  !*** ./app/government/dashboard/page.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GovernmentDashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.tsx\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./components/DashboardLayout.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/line-chart.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pie-chart.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/AreaChart.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Legend.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Area.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/PieChart.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/polar/Pie.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Cell.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/BarChart.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Bar.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction GovernmentDashboard() {\n    var _user_profile;\n    _s();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [recentTenders, setRecentTenders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [recentApplications, setRecentApplications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [chartData, setChartData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        budgetOverTime: [],\n        tenderPerformance: [],\n        categoryDistribution: [],\n        applicationTrends: []\n    });\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    // Chart colors\n    const COLORS = {\n        primary: \"#059669\",\n        secondary: \"#7C3AED\",\n        success: \"#10B981\",\n        warning: \"#F59E0B\",\n        danger: \"#EF4444\",\n        info: \"#06B6D4\"\n    };\n    const PIE_COLORS = [\n        \"#059669\",\n        \"#7C3AED\",\n        \"#10B981\",\n        \"#F59E0B\",\n        \"#EF4444\",\n        \"#06B6D4\"\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const userData = localStorage.getItem(\"user\");\n        if (userData) {\n            setUser(JSON.parse(userData));\n        }\n        loadDashboardData();\n    }, []);\n    const loadDashboardData = async ()=>{\n        try {\n            setLoading(true);\n            // Load government stats\n            const statsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_8__[\"default\"].get(\"/government/dashboard/stats\");\n            if (statsResponse.data.success) {\n                setStats(statsResponse.data.data);\n            }\n            // Load recent tenders\n            const tendersResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_8__[\"default\"].get(\"/government/tenders?limit=5&sort=-createdAt\");\n            if (tendersResponse.data.success) {\n                setRecentTenders(tendersResponse.data.data.tenders);\n            }\n            // Load recent applications\n            const applicationsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_8__[\"default\"].get(\"/government/applications/recent?limit=5\");\n            if (applicationsResponse.data.success) {\n                setRecentApplications(applicationsResponse.data.data.applications);\n            }\n            // Load notifications\n            const notificationsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_8__[\"default\"].get(\"/government/notifications?limit=5\");\n            if (notificationsResponse.data.success) {\n                setNotifications(notificationsResponse.data.data.notifications);\n            }\n            // Load chart data\n            const chartResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_8__[\"default\"].get(\"/government/analytics/charts\");\n            if (chartResponse.data.success) {\n                setChartData(chartResponse.data.data);\n            }\n        } catch (error) {\n            console.error(\"Error loading dashboard data:\", error);\n            // Fallback to sample data\n            setStats({\n                activeTenders: 12,\n                completedTenders: 8,\n                totalApplications: 45,\n                approvedApplications: 28,\n                totalBudget: 2500000,\n                pendingReviews: 15,\n                totalProjects: 20,\n                averageTenderValue: 125000,\n                successRate: 85\n            });\n            setRecentTenders([]);\n            setRecentApplications([]);\n            setNotifications([]);\n            // Sample chart data\n            setChartData({\n                budgetOverTime: [\n                    {\n                        month: \"يناير\",\n                        budget: 450000,\n                        tenders: 8\n                    },\n                    {\n                        month: \"فبراير\",\n                        budget: 520000,\n                        tenders: 12\n                    },\n                    {\n                        month: \"مارس\",\n                        budget: 380000,\n                        tenders: 6\n                    },\n                    {\n                        month: \"أبريل\",\n                        budget: 670000,\n                        tenders: 15\n                    },\n                    {\n                        month: \"مايو\",\n                        budget: 580000,\n                        tenders: 11\n                    },\n                    {\n                        month: \"يونيو\",\n                        budget: 730000,\n                        tenders: 18\n                    }\n                ],\n                tenderPerformance: [\n                    {\n                        status: \"مكتملة\",\n                        count: 25,\n                        color: COLORS.success\n                    },\n                    {\n                        status: \"نشطة\",\n                        count: 12,\n                        color: COLORS.primary\n                    },\n                    {\n                        status: \"قيد المراجعة\",\n                        count: 8,\n                        color: COLORS.warning\n                    },\n                    {\n                        status: \"ملغية\",\n                        count: 3,\n                        color: COLORS.danger\n                    }\n                ],\n                categoryDistribution: [\n                    {\n                        name: \"إنشاءات\",\n                        value: 35,\n                        color: PIE_COLORS[0]\n                    },\n                    {\n                        name: \"تقنية المعلومات\",\n                        value: 25,\n                        color: PIE_COLORS[1]\n                    },\n                    {\n                        name: \"استشارات\",\n                        value: 20,\n                        color: PIE_COLORS[2]\n                    },\n                    {\n                        name: \"رعاية صحية\",\n                        value: 12,\n                        color: PIE_COLORS[3]\n                    },\n                    {\n                        name: \"أخرى\",\n                        value: 8,\n                        color: PIE_COLORS[4]\n                    }\n                ],\n                applicationTrends: [\n                    {\n                        day: \"السبت\",\n                        applications: 8,\n                        approved: 5\n                    },\n                    {\n                        day: \"الأحد\",\n                        applications: 12,\n                        approved: 8\n                    },\n                    {\n                        day: \"الاثنين\",\n                        applications: 15,\n                        approved: 10\n                    },\n                    {\n                        day: \"الثلاثاء\",\n                        applications: 18,\n                        approved: 12\n                    },\n                    {\n                        day: \"الأربعاء\",\n                        applications: 14,\n                        approved: 9\n                    },\n                    {\n                        day: \"الخميس\",\n                        applications: 20,\n                        approved: 15\n                    },\n                    {\n                        day: \"الجمعة\",\n                        applications: 10,\n                        approved: 7\n                    }\n                ]\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const formatPrice = (price)=>{\n        return new Intl.NumberFormat(\"ar-SA\", {\n            style: \"currency\",\n            currency: \"SAR\",\n            minimumFractionDigits: 0\n        }).format(price);\n    };\n    const formatTimeRemaining = (deadline)=>{\n        const now = new Date();\n        const end = new Date(deadline);\n        const diff = end.getTime() - now.getTime();\n        if (diff <= 0) return \"انتهى\";\n        const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n        const hours = Math.floor(diff % (1000 * 60 * 60 * 24) / (1000 * 60 * 60));\n        if (days > 0) return \"\".concat(days, \" يوم\");\n        if (hours > 0) return \"\".concat(hours, \" ساعة\");\n        return \"أقل من ساعة\";\n    };\n    const formatRelativeTime = (dateString)=>{\n        const now = new Date();\n        const date = new Date(dateString);\n        const diff = now.getTime() - date.getTime();\n        const minutes = Math.floor(diff / (1000 * 60));\n        const hours = Math.floor(diff / (1000 * 60 * 60));\n        const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n        if (days > 0) return \"منذ \".concat(days, \" يوم\");\n        if (hours > 0) return \"منذ \".concat(hours, \" ساعة\");\n        if (minutes > 0) return \"منذ \".concat(minutes, \" دقيقة\");\n        return \"الآن\";\n    };\n    const getTenderStatusBadge = (status)=>{\n        switch(status){\n            case \"active\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: \"bg-green-100 text-green-800\",\n                    children: \"نشط\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 270,\n                    columnNumber: 16\n                }, this);\n            case \"completed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: \"bg-blue-100 text-blue-800\",\n                    children: \"مكتمل\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 272,\n                    columnNumber: 16\n                }, this);\n            case \"review\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: \"bg-yellow-100 text-yellow-800\",\n                    children: \"قيد المراجعة\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 274,\n                    columnNumber: 16\n                }, this);\n            case \"cancelled\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    variant: \"destructive\",\n                    children: \"ملغي\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 276,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    variant: \"outline\",\n                    children: status\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 278,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getApplicationStatusBadge = (status)=>{\n        switch(status){\n            case \"approved\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: \"bg-green-100 text-green-800\",\n                    children: \"موافق عليه\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 285,\n                    columnNumber: 16\n                }, this);\n            case \"rejected\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    variant: \"destructive\",\n                    children: \"مرفوض\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 287,\n                    columnNumber: 16\n                }, this);\n            case \"review\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: \"bg-yellow-100 text-yellow-800\",\n                    children: \"قيد المراجعة\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 289,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    variant: \"outline\",\n                    children: status\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 291,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            allowedRoles: [\n                \"government\"\n            ],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-96\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 300,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-4 text-gray-600\",\n                            children: \"جاري تحميل بيانات الجهة الحكومية...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 301,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 299,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                lineNumber: 298,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n            lineNumber: 297,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        allowedRoles: [\n            \"government\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    className: \"bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold\",\n                                        children: [\n                                            \"مرحباً، \",\n                                            (user === null || user === void 0 ? void 0 : (_user_profile = user.profile) === null || _user_profile === void 0 ? void 0 : _user_profile.governmentEntity) || \"الجهة الحكومية\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-green-100 mt-2\",\n                                        children: \"إدارة المناقصات الحكومية ومتابعة المشاريع العامة\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: [\n                                                    (stats === null || stats === void 0 ? void 0 : stats.successRate) || 0,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-green-100\",\n                                                children: \"معدل النجاح\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: (stats === null || stats === void 0 ? void 0 : stats.totalProjects) || 0\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-green-100\",\n                                                children: \"إجمالي المشاريع\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 312,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            className: \"h-16 text-lg bg-green-600 hover:bg-green-700\",\n                            size: \"lg\",\n                            onClick: ()=>router.push(\"/government/create-tender\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-6 w-6 ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 342,\n                                    columnNumber: 13\n                                }, this),\n                                \"إنشاء مناقصة جديدة\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 337,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            className: \"h-16 text-lg border-blue-200 text-blue-700 hover:bg-blue-50\",\n                            size: \"lg\",\n                            onClick: ()=>router.push(\"/government/tenders\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-6 w-6 ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 13\n                                }, this),\n                                \"إدارة المناقصات\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 345,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            className: \"h-16 text-lg border-purple-200 text-purple-700 hover:bg-purple-50\",\n                            size: \"lg\",\n                            onClick: ()=>router.push(\"/government/applications\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-6 w-6 ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 13\n                                }, this),\n                                \"مراجعة الطلبات\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 354,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            className: \"h-16 text-lg border-orange-200 text-orange-700 hover:bg-orange-50\",\n                            size: \"lg\",\n                            onClick: ()=>router.push(\"/government/profile\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-6 w-6 ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 13\n                                }, this),\n                                \"الملف الشخصي\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 363,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 336,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-gradient-to-br from-green-50 to-green-100 border-green-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-green-600\",\n                                                    children: \"المناقصات النشطة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-3xl font-bold text-green-900\",\n                                                    children: (stats === null || stats === void 0 ? void 0 : stats.activeTenders) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-green-600 mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-3 w-3 inline ml-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                            lineNumber: 383,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"نشطة الآن\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 379,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-12 w-12 bg-green-200 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-6 w-6 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 378,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                lineNumber: 377,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 376,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-blue-600\",\n                                                    children: \"المناقصات المكتملة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 398,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-3xl font-bold text-blue-900\",\n                                                    children: (stats === null || stats === void 0 ? void 0 : stats.completedTenders) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-blue-600 mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-3 w-3 inline ml-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                            lineNumber: 401,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"مكتملة\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 400,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-12 w-12 bg-blue-200 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-6 w-6 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 405,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                lineNumber: 395,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 394,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-purple-600\",\n                                                    children: \"إجمالي الميزانية\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-3xl font-bold text-purple-900\",\n                                                    children: formatPrice((stats === null || stats === void 0 ? void 0 : stats.totalBudget) || 0)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 417,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-purple-600 mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"h-3 w-3 inline ml-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                            lineNumber: 419,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"استثمار عام\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-12 w-12 bg-purple-200 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-6 w-6 text-purple-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 412,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-orange-600\",\n                                                    children: \"الطلبات المعلقة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-3xl font-bold text-orange-900\",\n                                                    children: (stats === null || stats === void 0 ? void 0 : stats.pendingReviews) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 435,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-orange-600 mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-3 w-3 inline ml-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                            lineNumber: 437,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"تحتاج مراجعة\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 436,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-12 w-12 bg-orange-200 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-6 w-6 text-orange-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 441,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 432,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                lineNumber: 431,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 430,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 375,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"h-5 w-5 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"الميزانية الشهرية\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 454,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"تطور الميزانية وعدد المناقصات خلال الأشهر الماضية\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 458,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 453,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_21__.ResponsiveContainer, {\n                                        width: \"100%\",\n                                        height: 300,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_22__.AreaChart, {\n                                            data: chartData.budgetOverTime,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_23__.CartesianGrid, {\n                                                    strokeDasharray: \"3 3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_24__.XAxis, {\n                                                    dataKey: \"month\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 466,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_25__.YAxis, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_26__.Tooltip, {\n                                                    formatter: (value, name)=>[\n                                                            name === \"budget\" ? formatPrice(value) : value,\n                                                            name === \"budget\" ? \"الميزانية\" : \"المناقصات\"\n                                                        ]\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 468,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_27__.Legend, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_28__.Area, {\n                                                    type: \"monotone\",\n                                                    dataKey: \"budget\",\n                                                    stackId: \"1\",\n                                                    stroke: COLORS.primary,\n                                                    fill: COLORS.primary,\n                                                    fillOpacity: 0.6,\n                                                    name: \"الميزانية\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 473,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_28__.Area, {\n                                                    type: \"monotone\",\n                                                    dataKey: \"tenders\",\n                                                    stackId: \"2\",\n                                                    stroke: COLORS.success,\n                                                    fill: COLORS.success,\n                                                    fillOpacity: 0.8,\n                                                    name: \"المناقصات\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 482,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 464,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 463,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 462,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 452,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                    className: \"h-5 w-5 text-purple-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 500,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"أداء المناقصات\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 499,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"توزيع المناقصات حسب الحالة\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 503,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 498,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_21__.ResponsiveContainer, {\n                                        width: \"100%\",\n                                        height: 300,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_30__.PieChart, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_31__.Pie, {\n                                                    data: chartData.tenderPerformance,\n                                                    cx: \"50%\",\n                                                    cy: \"50%\",\n                                                    labelLine: false,\n                                                    label: (param)=>{\n                                                        let { status, percent } = param;\n                                                        return \"\".concat(status, \" \").concat((percent * 100).toFixed(0), \"%\");\n                                                    },\n                                                    outerRadius: 80,\n                                                    fill: \"#8884d8\",\n                                                    dataKey: \"count\",\n                                                    children: chartData.tenderPerformance.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_32__.Cell, {\n                                                            fill: entry.color\n                                                        }, \"cell-\".concat(index), false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                            lineNumber: 521,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_26__.Tooltip, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 524,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 509,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 507,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 497,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 450,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                    className: \"h-5 w-5 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 537,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"توزيع الفئات\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 536,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"توزيع المناقصات حسب الفئات المختلفة\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 540,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 535,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_21__.ResponsiveContainer, {\n                                        width: \"100%\",\n                                        height: 300,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_30__.PieChart, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_31__.Pie, {\n                                                    data: chartData.categoryDistribution,\n                                                    cx: \"50%\",\n                                                    cy: \"50%\",\n                                                    innerRadius: 60,\n                                                    outerRadius: 100,\n                                                    paddingAngle: 5,\n                                                    dataKey: \"value\",\n                                                    children: chartData.categoryDistribution.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_32__.Cell, {\n                                                            fill: entry.color\n                                                        }, \"cell-\".concat(index), false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                            lineNumber: 557,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 547,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_26__.Tooltip, {\n                                                    formatter: (value)=>\"\".concat(value, \"%\")\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 560,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_27__.Legend, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 561,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 546,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 545,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 544,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 534,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                    className: \"h-5 w-5 text-orange-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 571,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"اتجاه الطلبات\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 570,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"الطلبات المقدمة والموافق عليها خلال الأسبوع\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 574,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 569,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_21__.ResponsiveContainer, {\n                                        width: \"100%\",\n                                        height: 300,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_35__.BarChart, {\n                                            data: chartData.applicationTrends,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_23__.CartesianGrid, {\n                                                    strokeDasharray: \"3 3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 581,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_24__.XAxis, {\n                                                    dataKey: \"day\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 582,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_25__.YAxis, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 583,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_26__.Tooltip, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 584,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_27__.Legend, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 585,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_36__.Bar, {\n                                                    dataKey: \"applications\",\n                                                    fill: COLORS.primary,\n                                                    name: \"الطلبات\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 586,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_36__.Bar, {\n                                                    dataKey: \"approved\",\n                                                    fill: COLORS.success,\n                                                    name: \"الموافق عليها\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 587,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 580,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 579,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 578,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 568,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 532,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-5 w-5 text-green-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                            lineNumber: 601,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"المناقصات الحديثة\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 600,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>router.push(\"/government/tenders\"),\n                                                    children: [\n                                                        \"عرض الكل\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                            lineNumber: 610,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 604,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 599,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"آخر المناقصات التي تم إنشاؤها\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 613,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 598,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: recentTenders.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: recentTenders.slice(0, 3).map((tender)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer\",\n                                                onClick: ()=>router.push(\"/tenders/\".concat(tender._id)),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-gray-900\",\n                                                                children: tender.title\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                lineNumber: 627,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-4 mt-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"h-4 w-4 inline ml-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                                lineNumber: 630,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            formatPrice(tender.budget)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                        lineNumber: 629,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                                                className: \"h-4 w-4 inline ml-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                                lineNumber: 634,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            tender.applicationsCount,\n                                                                            \" طلب\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                        lineNumber: 633,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                                                                className: \"h-4 w-4 inline ml-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                                lineNumber: 638,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            tender.viewsCount,\n                                                                            \" مشاهدة\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                        lineNumber: 637,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                lineNumber: 628,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                        lineNumber: 626,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-end gap-2\",\n                                                        children: [\n                                                            getTenderStatusBadge(tender.status),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-3 w-3 inline ml-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                        lineNumber: 646,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    formatTimeRemaining(tender.deadline)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                lineNumber: 645,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                        lineNumber: 643,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, tender._id, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 621,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 619,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 655,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                children: \"لا توجد مناقصات حديثة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 656,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-4\",\n                                                children: \"ابدأ بإنشاء مناقصتك الأولى\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 657,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: ()=>router.push(\"/government/create-tender\"),\n                                                children: \"إنشاء مناقصة جديدة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 658,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 654,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 617,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 597,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-5 w-5 text-blue-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                            lineNumber: 671,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"الطلبات الحديثة\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 670,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>router.push(\"/government/applications\"),\n                                                    children: [\n                                                        \"عرض الكل\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                            lineNumber: 680,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 674,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 669,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"آخر الطلبات المقدمة على المناقصات\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 683,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 668,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: recentApplications.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: recentApplications.slice(0, 3).map((application)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-gray-900\",\n                                                                children: application.tender.title\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                lineNumber: 696,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-4 mt-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                                            className: \"h-4 w-4 inline ml-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                            lineNumber: 699,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        application.applicant.profile.companyName || application.applicant.profile.fullName\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                    lineNumber: 698,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                lineNumber: 697,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 mt-1\",\n                                                                children: formatRelativeTime(application.submittedAt)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                lineNumber: 703,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                        lineNumber: 695,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-end gap-2\",\n                                                        children: [\n                                                            getApplicationStatusBadge(application.status),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>router.push(\"/government/applications/\".concat(application._id)),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                                                        className: \"h-4 w-4 ml-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                        lineNumber: 714,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"مراجعة\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                lineNumber: 709,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                        lineNumber: 707,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, application._id, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 691,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 689,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 723,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                children: \"لا توجد طلبات حديثة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 724,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"ستظهر هنا الطلبات المقدمة على مناقصاتك\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 725,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 722,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 687,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 667,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 595,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n            lineNumber: 310,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n        lineNumber: 309,\n        columnNumber: 5\n    }, this);\n}\n_s(GovernmentDashboard, \"9xoAi1BuwjIaM/cSa1syjT/e89k=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast,\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter\n    ];\n});\n_c = GovernmentDashboard;\nvar _c;\n$RefreshReg$(_c, \"GovernmentDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/government/dashboard/page.tsx\n"));

/***/ })

});