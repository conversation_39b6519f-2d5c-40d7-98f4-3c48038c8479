"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/government/dashboard/page",{

/***/ "(app-pages-browser)/./app/government/dashboard/page.tsx":
/*!*******************************************!*\
  !*** ./app/government/dashboard/page.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GovernmentDashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.tsx\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./components/DashboardLayout.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/line-chart.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pie-chart.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/AreaChart.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Legend.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Area.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/PieChart.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/polar/Pie.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Cell.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/BarChart.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Bar.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction GovernmentDashboard() {\n    var _user_profile;\n    _s();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [recentTenders, setRecentTenders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [recentApplications, setRecentApplications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [chartData, setChartData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        budgetOverTime: [],\n        tenderPerformance: [],\n        categoryDistribution: [],\n        applicationTrends: []\n    });\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    // Chart colors\n    const COLORS = {\n        primary: \"#059669\",\n        secondary: \"#7C3AED\",\n        success: \"#10B981\",\n        warning: \"#F59E0B\",\n        danger: \"#EF4444\",\n        info: \"#06B6D4\"\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const userData = localStorage.getItem(\"user\");\n        if (userData) {\n            setUser(JSON.parse(userData));\n        }\n        loadDashboardData();\n    }, []);\n    const loadDashboardData = async ()=>{\n        try {\n            setLoading(true);\n            // Load government stats\n            const statsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_8__[\"default\"].get(\"/government/dashboard/stats\");\n            if (statsResponse.data.success) {\n                setStats(statsResponse.data.data);\n            }\n            // Load recent tenders\n            const tendersResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_8__[\"default\"].get(\"/government/tenders?limit=5&sort=-createdAt\");\n            if (tendersResponse.data.success) {\n                setRecentTenders(tendersResponse.data.data.tenders || []);\n            }\n            // Load recent applications\n            const applicationsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_8__[\"default\"].get(\"/government/applications/recent?limit=5\");\n            if (applicationsResponse.data.success) {\n                setRecentApplications(applicationsResponse.data.data.applications || []);\n            }\n            // Load notifications\n            const notificationsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_8__[\"default\"].get(\"/government/notifications?limit=5\");\n            if (notificationsResponse.data.success) {\n                setNotifications(notificationsResponse.data.data.notifications || []);\n            }\n            // Load chart data\n            const chartResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_8__[\"default\"].get(\"/government/analytics/charts\");\n            if (chartResponse.data.success) {\n                setChartData({\n                    budgetOverTime: chartResponse.data.data.budgetOverTime || [],\n                    tenderPerformance: chartResponse.data.data.tenderPerformance || [],\n                    categoryDistribution: chartResponse.data.data.categoryDistribution || [],\n                    applicationTrends: chartResponse.data.data.applicationTrends || []\n                });\n            }\n        } catch (error) {\n            console.error(\"Error loading dashboard data:\", error);\n            toast({\n                title: \"خطأ في التحميل\",\n                description: \"حدث خطأ في تحميل بيانات لوحة التحكم\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const formatPrice = (price)=>{\n        return new Intl.NumberFormat(\"ar-SA\", {\n            style: \"currency\",\n            currency: \"SAR\",\n            minimumFractionDigits: 0\n        }).format(price);\n    };\n    const formatTimeRemaining = (deadline)=>{\n        const now = new Date();\n        const end = new Date(deadline);\n        const diff = end.getTime() - now.getTime();\n        if (diff <= 0) return \"انتهى\";\n        const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n        const hours = Math.floor(diff % (1000 * 60 * 60 * 24) / (1000 * 60 * 60));\n        if (days > 0) return \"\".concat(days, \" يوم\");\n        if (hours > 0) return \"\".concat(hours, \" ساعة\");\n        return \"أقل من ساعة\";\n    };\n    const formatRelativeTime = (dateString)=>{\n        const now = new Date();\n        const date = new Date(dateString);\n        const diff = now.getTime() - date.getTime();\n        const minutes = Math.floor(diff / (1000 * 60));\n        const hours = Math.floor(diff / (1000 * 60 * 60));\n        const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n        if (days > 0) return \"منذ \".concat(days, \" يوم\");\n        if (hours > 0) return \"منذ \".concat(hours, \" ساعة\");\n        if (minutes > 0) return \"منذ \".concat(minutes, \" دقيقة\");\n        return \"الآن\";\n    };\n    const getTenderStatusBadge = (status)=>{\n        switch(status){\n            case \"active\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: \"bg-green-100 text-green-800\",\n                    children: \"نشط\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 16\n                }, this);\n            case \"completed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: \"bg-blue-100 text-blue-800\",\n                    children: \"مكتمل\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 16\n                }, this);\n            case \"review\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: \"bg-yellow-100 text-yellow-800\",\n                    children: \"قيد المراجعة\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 235,\n                    columnNumber: 16\n                }, this);\n            case \"cancelled\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    variant: \"destructive\",\n                    children: \"ملغي\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 237,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    variant: \"outline\",\n                    children: status\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getApplicationStatusBadge = (status)=>{\n        switch(status){\n            case \"approved\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: \"bg-green-100 text-green-800\",\n                    children: \"موافق عليه\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 16\n                }, this);\n            case \"rejected\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    variant: \"destructive\",\n                    children: \"مرفوض\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 248,\n                    columnNumber: 16\n                }, this);\n            case \"review\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: \"bg-yellow-100 text-yellow-800\",\n                    children: \"قيد المراجعة\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    variant: \"outline\",\n                    children: status\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 252,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            allowedRoles: [\n                \"government\"\n            ],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-96\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-4 text-gray-600\",\n                            children: \"جاري تحميل بيانات الجهة الحكومية...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 260,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                lineNumber: 259,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n            lineNumber: 258,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        allowedRoles: [\n            \"government\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    className: \"bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold\",\n                                        children: [\n                                            \"مرحباً، \",\n                                            (user === null || user === void 0 ? void 0 : (_user_profile = user.profile) === null || _user_profile === void 0 ? void 0 : _user_profile.governmentEntity) || \"الجهة الحكومية\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-green-100 mt-2\",\n                                        children: \"إدارة المناقصات الحكومية ومتابعة المشاريع العامة\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: [\n                                                    (stats === null || stats === void 0 ? void 0 : stats.successRate) || 0,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-green-100\",\n                                                children: \"معدل النجاح\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: (stats === null || stats === void 0 ? void 0 : stats.totalProjects) || 0\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-green-100\",\n                                                children: \"إجمالي المشاريع\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 273,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            className: \"h-16 text-lg bg-green-600 hover:bg-green-700\",\n                            size: \"lg\",\n                            onClick: ()=>router.push(\"/government/create-tender\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-6 w-6 ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 13\n                                }, this),\n                                \"إنشاء مناقصة جديدة\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            className: \"h-16 text-lg border-blue-200 text-blue-700 hover:bg-blue-50\",\n                            size: \"lg\",\n                            onClick: ()=>router.push(\"/government/tenders\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-6 w-6 ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 13\n                                }, this),\n                                \"إدارة المناقصات\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            className: \"h-16 text-lg border-purple-200 text-purple-700 hover:bg-purple-50\",\n                            size: \"lg\",\n                            onClick: ()=>router.push(\"/government/applications\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-6 w-6 ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 13\n                                }, this),\n                                \"مراجعة الطلبات\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            className: \"h-16 text-lg border-orange-200 text-orange-700 hover:bg-orange-50\",\n                            size: \"lg\",\n                            onClick: ()=>router.push(\"/government/profile\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-6 w-6 ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 13\n                                }, this),\n                                \"الملف الشخصي\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 297,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-gradient-to-br from-green-50 to-green-100 border-green-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-green-600\",\n                                                    children: \"المناقصات النشطة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-3xl font-bold text-green-900\",\n                                                    children: (stats === null || stats === void 0 ? void 0 : stats.activeTenders) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 342,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-green-600 mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-3 w-3 inline ml-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                            lineNumber: 344,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"نشطة الآن\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-12 w-12 bg-green-200 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-6 w-6 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 337,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-blue-600\",\n                                                    children: \"المناقصات المكتملة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-3xl font-bold text-blue-900\",\n                                                    children: (stats === null || stats === void 0 ? void 0 : stats.completedTenders) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-blue-600 mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-3 w-3 inline ml-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                            lineNumber: 362,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"مكتملة\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-12 w-12 bg-blue-200 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-6 w-6 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 367,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 355,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-purple-600\",\n                                                    children: \"إجمالي الميزانية\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-3xl font-bold text-purple-900\",\n                                                    children: formatPrice((stats === null || stats === void 0 ? void 0 : stats.totalBudget) || 0)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-purple-600 mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"h-3 w-3 inline ml-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"استثمار عام\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 376,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-12 w-12 bg-purple-200 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-6 w-6 text-purple-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 384,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 375,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                lineNumber: 374,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 373,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-orange-600\",\n                                                    children: \"الطلبات المعلقة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 395,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-3xl font-bold text-orange-900\",\n                                                    children: (stats === null || stats === void 0 ? void 0 : stats.pendingReviews) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 396,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-orange-600 mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-3 w-3 inline ml-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                            lineNumber: 398,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"تحتاج مراجعة\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-12 w-12 bg-orange-200 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-6 w-6 text-orange-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                lineNumber: 392,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 391,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 336,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"h-5 w-5 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"الميزانية الشهرية\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"تطور الميزانية وعدد المناقصات خلال الأشهر الماضية\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: chartData.budgetOverTime && chartData.budgetOverTime.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_21__.ResponsiveContainer, {\n                                        width: \"100%\",\n                                        height: 300,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_22__.AreaChart, {\n                                            data: chartData.budgetOverTime,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_23__.CartesianGrid, {\n                                                    strokeDasharray: \"3 3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 427,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_24__.XAxis, {\n                                                    dataKey: \"month\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_25__.YAxis, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_26__.Tooltip, {\n                                                    formatter: (value, name)=>[\n                                                            name === \"budget\" ? formatPrice(value) : value,\n                                                            name === \"budget\" ? \"الميزانية\" : \"المناقصات\"\n                                                        ]\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_27__.Legend, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_28__.Area, {\n                                                    type: \"monotone\",\n                                                    dataKey: \"budget\",\n                                                    stackId: \"1\",\n                                                    stroke: COLORS.primary,\n                                                    fill: COLORS.primary,\n                                                    fillOpacity: 0.6,\n                                                    name: \"الميزانية\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 435,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_28__.Area, {\n                                                    type: \"monotone\",\n                                                    dataKey: \"tenders\",\n                                                    stackId: \"2\",\n                                                    stroke: COLORS.success,\n                                                    fill: COLORS.success,\n                                                    fillOpacity: 0.8,\n                                                    name: \"المناقصات\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 426,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center h-[300px] text-gray-500\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                    className: \"h-12 w-12 mx-auto mb-2 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"لا توجد بيانات للعرض\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 457,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 456,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 413,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                    className: \"h-5 w-5 text-purple-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 470,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"أداء المناقصات\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"توزيع المناقصات حسب الحالة\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 473,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 468,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_21__.ResponsiveContainer, {\n                                        width: \"100%\",\n                                        height: 300,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_31__.PieChart, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_32__.Pie, {\n                                                    data: chartData.tenderPerformance,\n                                                    cx: \"50%\",\n                                                    cy: \"50%\",\n                                                    labelLine: false,\n                                                    label: (param)=>{\n                                                        let { status, percent } = param;\n                                                        return \"\".concat(status, \" \").concat((percent * 100).toFixed(0), \"%\");\n                                                    },\n                                                    outerRadius: 80,\n                                                    fill: \"#8884d8\",\n                                                    dataKey: \"count\",\n                                                    children: chartData.tenderPerformance.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_33__.Cell, {\n                                                            fill: entry.color\n                                                        }, \"cell-\".concat(index), false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                            lineNumber: 491,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 480,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_26__.Tooltip, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 494,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 479,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 478,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 477,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 467,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 411,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                    className: \"h-5 w-5 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 507,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"توزيع الفئات\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 506,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"توزيع المناقصات حسب الفئات المختلفة\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 510,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 505,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_21__.ResponsiveContainer, {\n                                        width: \"100%\",\n                                        height: 300,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_31__.PieChart, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_32__.Pie, {\n                                                    data: chartData.categoryDistribution,\n                                                    cx: \"50%\",\n                                                    cy: \"50%\",\n                                                    innerRadius: 60,\n                                                    outerRadius: 100,\n                                                    paddingAngle: 5,\n                                                    dataKey: \"value\",\n                                                    children: chartData.categoryDistribution.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_33__.Cell, {\n                                                            fill: entry.color\n                                                        }, \"cell-\".concat(index), false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                            lineNumber: 527,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 517,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_26__.Tooltip, {\n                                                    formatter: (value)=>\"\".concat(value, \"%\")\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 530,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_27__.Legend, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 531,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 516,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 514,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 504,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                    className: \"h-5 w-5 text-orange-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 541,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"اتجاه الطلبات\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 540,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"الطلبات المقدمة والموافق عليها خلال الأسبوع\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 544,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 539,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_21__.ResponsiveContainer, {\n                                        width: \"100%\",\n                                        height: 300,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_35__.BarChart, {\n                                            data: chartData.applicationTrends,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_23__.CartesianGrid, {\n                                                    strokeDasharray: \"3 3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 551,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_24__.XAxis, {\n                                                    dataKey: \"day\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 552,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_25__.YAxis, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 553,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_26__.Tooltip, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 554,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_27__.Legend, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 555,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_36__.Bar, {\n                                                    dataKey: \"applications\",\n                                                    fill: COLORS.primary,\n                                                    name: \"الطلبات\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 556,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_36__.Bar, {\n                                                    dataKey: \"approved\",\n                                                    fill: COLORS.success,\n                                                    name: \"الموافق عليها\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 557,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 550,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 549,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 548,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 538,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 502,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-5 w-5 text-green-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                            lineNumber: 571,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"المناقصات الحديثة\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 570,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>router.push(\"/government/tenders\"),\n                                                    children: [\n                                                        \"عرض الكل\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                            lineNumber: 580,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 574,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 569,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"آخر المناقصات التي تم إنشاؤها\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 583,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 568,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: recentTenders.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: recentTenders.slice(0, 3).map((tender)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer\",\n                                                onClick: ()=>router.push(\"/tenders/\".concat(tender._id)),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-gray-900\",\n                                                                children: tender.title\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                lineNumber: 597,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-4 mt-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"h-4 w-4 inline ml-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                                lineNumber: 600,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            formatPrice(tender.budget)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                        lineNumber: 599,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                                                className: \"h-4 w-4 inline ml-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                                lineNumber: 604,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            tender.applicationsCount,\n                                                                            \" طلب\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                        lineNumber: 603,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                                                                className: \"h-4 w-4 inline ml-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                                lineNumber: 608,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            tender.viewsCount,\n                                                                            \" مشاهدة\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                        lineNumber: 607,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                lineNumber: 598,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                        lineNumber: 596,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-end gap-2\",\n                                                        children: [\n                                                            getTenderStatusBadge(tender.status),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-3 w-3 inline ml-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                        lineNumber: 616,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    formatTimeRemaining(tender.deadline)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                lineNumber: 615,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                        lineNumber: 613,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, tender._id, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 591,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 589,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 625,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                children: \"لا توجد مناقصات حديثة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 626,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-4\",\n                                                children: \"ابدأ بإنشاء مناقصتك الأولى\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 627,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: ()=>router.push(\"/government/create-tender\"),\n                                                children: \"إنشاء مناقصة جديدة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 628,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 624,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 587,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 567,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-5 w-5 text-blue-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                            lineNumber: 641,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"الطلبات الحديثة\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 640,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>router.push(\"/government/applications\"),\n                                                    children: [\n                                                        \"عرض الكل\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                            lineNumber: 650,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 644,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 639,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"آخر الطلبات المقدمة على المناقصات\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 653,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 638,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: recentApplications.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: recentApplications.slice(0, 3).map((application)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-gray-900\",\n                                                                children: application.tender.title\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                lineNumber: 666,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-4 mt-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                                            className: \"h-4 w-4 inline ml-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                            lineNumber: 669,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        application.applicant.profile.companyName || application.applicant.profile.fullName\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                    lineNumber: 668,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                lineNumber: 667,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 mt-1\",\n                                                                children: formatRelativeTime(application.submittedAt)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                lineNumber: 673,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                        lineNumber: 665,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-end gap-2\",\n                                                        children: [\n                                                            getApplicationStatusBadge(application.status),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>router.push(\"/government/applications/\".concat(application._id)),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                                                        className: \"h-4 w-4 ml-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                        lineNumber: 684,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"مراجعة\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                lineNumber: 679,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                        lineNumber: 677,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, application._id, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 661,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 659,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 693,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                children: \"لا توجد طلبات حديثة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 694,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"ستظهر هنا الطلبات المقدمة على مناقصاتك\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 695,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 692,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 657,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 637,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 565,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n            lineNumber: 271,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n        lineNumber: 270,\n        columnNumber: 5\n    }, this);\n}\n_s(GovernmentDashboard, \"9xoAi1BuwjIaM/cSa1syjT/e89k=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast,\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter\n    ];\n});\n_c = GovernmentDashboard;\nvar _c;\n$RefreshReg$(_c, \"GovernmentDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/government/dashboard/page.tsx\n"));

/***/ })

});