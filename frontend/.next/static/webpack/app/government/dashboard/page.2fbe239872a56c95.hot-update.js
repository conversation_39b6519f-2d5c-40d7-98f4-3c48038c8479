"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/government/dashboard/page",{

/***/ "(app-pages-browser)/./app/government/dashboard/page.tsx":
/*!*******************************************!*\
  !*** ./app/government/dashboard/page.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GovernmentDashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/line-chart.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pie-chart.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/AreaChart.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Legend.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Area.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/PieChart.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/polar/Pie.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Cell.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/BarChart.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Bar.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction GovernmentDashboard() {\n    var _user_profile;\n    _s();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [recentTenders, setRecentTenders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [recentApplications, setRecentApplications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [chartData, setChartData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        budgetOverTime: [],\n        tenderPerformance: [],\n        categoryDistribution: [],\n        applicationTrends: []\n    });\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    // Chart colors\n    const COLORS = {\n        primary: \"#059669\",\n        secondary: \"#7C3AED\",\n        success: \"#10B981\",\n        warning: \"#F59E0B\",\n        danger: \"#EF4444\",\n        info: \"#06B6D4\"\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const userData = localStorage.getItem(\"user\");\n        if (userData) {\n            setUser(JSON.parse(userData));\n        }\n        loadDashboardData();\n    }, []);\n    const loadDashboardData = async ()=>{\n        try {\n            setLoading(true);\n            // Load government stats\n            const statsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"/government/dashboard/stats\");\n            if (statsResponse.data.success) {\n                setStats(statsResponse.data.data);\n            }\n            // Load recent tenders\n            const tendersResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"/government/tenders?limit=5&sort=-createdAt\");\n            if (tendersResponse.data.success) {\n                const tenders = (tendersResponse.data.data.tenders || []).filter((tender)=>tender && tender._id);\n                setRecentTenders(tenders);\n            }\n            // Load recent applications\n            const applicationsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"/government/applications/recent?limit=5\");\n            if (applicationsResponse.data.success) {\n                const applications = (applicationsResponse.data.data.applications || []).filter((app)=>app && app._id);\n                setRecentApplications(applications);\n            }\n            // Load notifications\n            const notificationsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"/government/notifications?limit=5\");\n            if (notificationsResponse.data.success) {\n                setNotifications(notificationsResponse.data.data.notifications || []);\n            }\n            // Load chart data\n            const chartResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"/government/analytics/charts\");\n            if (chartResponse.data.success) {\n                setChartData({\n                    budgetOverTime: chartResponse.data.data.budgetOverTime || [],\n                    tenderPerformance: chartResponse.data.data.tenderPerformance || [],\n                    categoryDistribution: chartResponse.data.data.categoryDistribution || [],\n                    applicationTrends: chartResponse.data.data.applicationTrends || []\n                });\n            }\n        } catch (error) {\n            console.error(\"Error loading dashboard data:\", error);\n            toast({\n                title: \"خطأ في التحميل\",\n                description: \"حدث خطأ في تحميل بيانات لوحة التحكم\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const formatPrice = (price)=>{\n        return new Intl.NumberFormat(\"ar-SA\", {\n            style: \"currency\",\n            currency: \"SAR\",\n            minimumFractionDigits: 0\n        }).format(price);\n    };\n    const formatTimeRemaining = (deadline)=>{\n        const now = new Date();\n        const end = new Date(deadline);\n        const diff = end.getTime() - now.getTime();\n        if (diff <= 0) return \"انتهى\";\n        const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n        const hours = Math.floor(diff % (1000 * 60 * 60 * 24) / (1000 * 60 * 60));\n        if (days > 0) return \"\".concat(days, \" يوم\");\n        if (hours > 0) return \"\".concat(hours, \" ساعة\");\n        return \"أقل من ساعة\";\n    };\n    const formatRelativeTime = (dateString)=>{\n        const now = new Date();\n        const date = new Date(dateString);\n        const diff = now.getTime() - date.getTime();\n        const minutes = Math.floor(diff / (1000 * 60));\n        const hours = Math.floor(diff / (1000 * 60 * 60));\n        const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n        if (days > 0) return \"منذ \".concat(days, \" يوم\");\n        if (hours > 0) return \"منذ \".concat(hours, \" ساعة\");\n        if (minutes > 0) return \"منذ \".concat(minutes, \" دقيقة\");\n        return \"الآن\";\n    };\n    const getTenderStatusBadge = (status)=>{\n        switch(status){\n            case \"active\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: \"bg-green-100 text-green-800\",\n                    children: \"نشط\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 16\n                }, this);\n            case \"completed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: \"bg-blue-100 text-blue-800\",\n                    children: \"مكتمل\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 16\n                }, this);\n            case \"review\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: \"bg-yellow-100 text-yellow-800\",\n                    children: \"قيد المراجعة\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 16\n                }, this);\n            case \"cancelled\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    variant: \"destructive\",\n                    children: \"ملغي\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    variant: \"outline\",\n                    children: status\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 235,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getApplicationStatusBadge = (status)=>{\n        switch(status){\n            case \"approved\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: \"bg-green-100 text-green-800\",\n                    children: \"موافق عليه\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 242,\n                    columnNumber: 16\n                }, this);\n            case \"rejected\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    variant: \"destructive\",\n                    children: \"مرفوض\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 16\n                }, this);\n            case \"review\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: \"bg-yellow-100 text-yellow-800\",\n                    children: \"قيد المراجعة\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    variant: \"outline\",\n                    children: status\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 248,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-96\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                        lineNumber: 256,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"جاري تحميل بيانات الجهة الحكومية...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                lineNumber: 255,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n            lineNumber: 254,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    className: \"bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold\",\n                                        children: [\n                                            \"مرحباً، \",\n                                            (user === null || user === void 0 ? void 0 : (_user_profile = user.profile) === null || _user_profile === void 0 ? void 0 : _user_profile.governmentEntity) || \"الجهة الحكومية\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-green-100 mt-2\",\n                                        children: \"إدارة المناقصات الحكومية ومتابعة المشاريع العامة\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: [\n                                                    (stats === null || stats === void 0 ? void 0 : stats.successRate) || 0,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-green-100\",\n                                                children: \"معدل النجاح\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: (stats === null || stats === void 0 ? void 0 : stats.totalProjects) || 0\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-green-100\",\n                                                children: \"إجمالي المشاريع\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 267,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SearchWidget, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 291,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            className: \"h-16 text-lg bg-green-600 hover:bg-green-700\",\n                            size: \"lg\",\n                            onClick: ()=>router.push(\"/government/create-tender\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-6 w-6 ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 13\n                                }, this),\n                                \"إنشاء مناقصة جديدة\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            className: \"h-16 text-lg border-blue-200 text-blue-700 hover:bg-blue-50\",\n                            size: \"lg\",\n                            onClick: ()=>router.push(\"/government/tenders\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-6 w-6 ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 13\n                                }, this),\n                                \"إدارة المناقصات\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            className: \"h-16 text-lg border-purple-200 text-purple-700 hover:bg-purple-50\",\n                            size: \"lg\",\n                            onClick: ()=>router.push(\"/government/applications\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-6 w-6 ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 13\n                                }, this),\n                                \"مراجعة الطلبات\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            className: \"h-16 text-lg border-orange-200 text-orange-700 hover:bg-orange-50\",\n                            size: \"lg\",\n                            onClick: ()=>router.push(\"/government/profile\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-6 w-6 ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 13\n                                }, this),\n                                \"الملف الشخصي\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 294,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-gradient-to-br from-green-50 to-green-100 border-green-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-green-600\",\n                                                    children: \"المناقصات النشطة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-3xl font-bold text-green-900\",\n                                                    children: (stats === null || stats === void 0 ? void 0 : stats.activeTenders) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-green-600 mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-3 w-3 inline ml-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                            lineNumber: 341,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"نشطة الآن\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-12 w-12 bg-green-200 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-6 w-6 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 346,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 334,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-blue-600\",\n                                                    children: \"المناقصات المكتملة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-3xl font-bold text-blue-900\",\n                                                    children: (stats === null || stats === void 0 ? void 0 : stats.completedTenders) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-blue-600 mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-3 w-3 inline ml-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                            lineNumber: 359,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"مكتملة\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-12 w-12 bg-blue-200 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-6 w-6 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 352,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-purple-600\",\n                                                    children: \"إجمالي الميزانية\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-3xl font-bold text-purple-900\",\n                                                    children: formatPrice((stats === null || stats === void 0 ? void 0 : stats.totalBudget) || 0)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-purple-600 mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-3 w-3 inline ml-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"استثمار عام\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-12 w-12 bg-purple-200 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-6 w-6 text-purple-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 381,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 370,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-orange-600\",\n                                                    children: \"الطلبات المعلقة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-3xl font-bold text-orange-900\",\n                                                    children: (stats === null || stats === void 0 ? void 0 : stats.pendingReviews) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 393,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-orange-600 mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-3 w-3 inline ml-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                            lineNumber: 395,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"تحتاج مراجعة\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 394,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-12 w-12 bg-orange-200 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-6 w-6 text-orange-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 390,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                lineNumber: 389,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 388,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 333,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-5 w-5 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 413,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"الميزانية الشهرية\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"تطور الميزانية وعدد المناقصات خلال الأشهر الماضية\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 416,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: chartData.budgetOverTime && chartData.budgetOverTime.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_20__.ResponsiveContainer, {\n                                        width: \"100%\",\n                                        height: 300,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_21__.AreaChart, {\n                                            data: chartData.budgetOverTime,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_22__.CartesianGrid, {\n                                                    strokeDasharray: \"3 3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_23__.XAxis, {\n                                                    dataKey: \"month\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 425,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_24__.YAxis, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 426,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_25__.Tooltip, {\n                                                    formatter: (value, name)=>[\n                                                            name === \"budget\" ? formatPrice(value) : value,\n                                                            name === \"budget\" ? \"الميزانية\" : \"المناقصات\"\n                                                        ]\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 427,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_26__.Legend, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_27__.Area, {\n                                                    type: \"monotone\",\n                                                    dataKey: \"budget\",\n                                                    stackId: \"1\",\n                                                    stroke: COLORS.primary,\n                                                    fill: COLORS.primary,\n                                                    fillOpacity: 0.6,\n                                                    name: \"الميزانية\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 432,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_27__.Area, {\n                                                    type: \"monotone\",\n                                                    dataKey: \"tenders\",\n                                                    stackId: \"2\",\n                                                    stroke: COLORS.success,\n                                                    fill: COLORS.success,\n                                                    fillOpacity: 0.8,\n                                                    name: \"المناقصات\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 441,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center h-[300px] text-gray-500\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                    className: \"h-12 w-12 mx-auto mb-2 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"لا توجد بيانات للعرض\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 456,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 454,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 420,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 410,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                    className: \"h-5 w-5 text-purple-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"أداء المناقصات\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 466,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"توزيع المناقصات حسب الحالة\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 470,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 465,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: chartData.tenderPerformance && chartData.tenderPerformance.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_20__.ResponsiveContainer, {\n                                        width: \"100%\",\n                                        height: 300,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_30__.PieChart, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_31__.Pie, {\n                                                    data: chartData.tenderPerformance,\n                                                    cx: \"50%\",\n                                                    cy: \"50%\",\n                                                    labelLine: false,\n                                                    label: (param)=>{\n                                                        let { status, percent } = param;\n                                                        return \"\".concat(status, \" \").concat((percent * 100).toFixed(0), \"%\");\n                                                    },\n                                                    outerRadius: 80,\n                                                    fill: \"#8884d8\",\n                                                    dataKey: \"count\",\n                                                    children: chartData.tenderPerformance.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_32__.Cell, {\n                                                            fill: entry.color\n                                                        }, \"cell-\".concat(index), false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                            lineNumber: 489,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 478,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_25__.Tooltip, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 492,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 477,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center h-[300px] text-gray-500\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                    className: \"h-12 w-12 mx-auto mb-2 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 498,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"لا توجد بيانات للعرض\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 497,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 496,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 474,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 464,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 408,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                    className: \"h-5 w-5 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 513,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"توزيع الفئات\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 512,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"توزيع المناقصات حسب الفئات المختلفة\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 516,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 511,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: chartData.categoryDistribution && chartData.categoryDistribution.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_20__.ResponsiveContainer, {\n                                        width: \"100%\",\n                                        height: 300,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_30__.PieChart, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_31__.Pie, {\n                                                    data: chartData.categoryDistribution,\n                                                    cx: \"50%\",\n                                                    cy: \"50%\",\n                                                    innerRadius: 60,\n                                                    outerRadius: 100,\n                                                    paddingAngle: 5,\n                                                    dataKey: \"value\",\n                                                    children: chartData.categoryDistribution.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_32__.Cell, {\n                                                            fill: entry.color\n                                                        }, \"cell-\".concat(index), false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                            lineNumber: 534,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 524,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_25__.Tooltip, {\n                                                    formatter: (value)=>\"\".concat(value, \"%\")\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 537,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_26__.Legend, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 538,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 523,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 522,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center h-[300px] text-gray-500\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                    className: \"h-12 w-12 mx-auto mb-2 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 544,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"لا توجد بيانات للعرض\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 545,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 543,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 542,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 520,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 510,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                    className: \"h-5 w-5 text-orange-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 556,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"اتجاه الطلبات\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 555,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"الطلبات المقدمة والموافق عليها خلال الأسبوع\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 559,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 554,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: chartData.applicationTrends && chartData.applicationTrends.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_20__.ResponsiveContainer, {\n                                        width: \"100%\",\n                                        height: 300,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_34__.BarChart, {\n                                            data: chartData.applicationTrends,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_22__.CartesianGrid, {\n                                                    strokeDasharray: \"3 3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 567,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_23__.XAxis, {\n                                                    dataKey: \"day\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 568,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_24__.YAxis, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 569,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_25__.Tooltip, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 570,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_26__.Legend, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 571,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_35__.Bar, {\n                                                    dataKey: \"applications\",\n                                                    fill: COLORS.primary,\n                                                    name: \"الطلبات\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 572,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_35__.Bar, {\n                                                    dataKey: \"approved\",\n                                                    fill: COLORS.success,\n                                                    name: \"الموافق عليها\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 573,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 566,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 565,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center h-[300px] text-gray-500\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                    className: \"h-12 w-12 mx-auto mb-2 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 579,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"لا توجد بيانات للعرض\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 580,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 578,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 577,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 563,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 553,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 508,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-5 w-5 text-green-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                            lineNumber: 595,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"المناقصات الحديثة\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 594,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>router.push(\"/government/tenders\"),\n                                                    children: [\n                                                        \"عرض الكل\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                            lineNumber: 604,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 598,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 593,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"آخر المناقصات التي تم إنشاؤها\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 607,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 592,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: recentTenders.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: recentTenders.slice(0, 3).filter((tender)=>tender && tender._id).map((tender)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer\",\n                                                onClick: ()=>router.push(\"/tenders/\".concat(tender._id)),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-gray-900\",\n                                                                children: tender.title\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                lineNumber: 621,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-4 mt-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                className: \"h-4 w-4 inline ml-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                                lineNumber: 624,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            formatPrice(tender.budget)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                        lineNumber: 623,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                                                className: \"h-4 w-4 inline ml-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                                lineNumber: 628,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            tender.applicationsCount,\n                                                                            \" طلب\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                        lineNumber: 627,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                                                                className: \"h-4 w-4 inline ml-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                                lineNumber: 632,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            tender.viewsCount,\n                                                                            \" مشاهدة\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                        lineNumber: 631,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                lineNumber: 622,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                        lineNumber: 620,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-end gap-2\",\n                                                        children: [\n                                                            getTenderStatusBadge(tender.status),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"h-3 w-3 inline ml-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                        lineNumber: 640,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    formatTimeRemaining(tender.deadline)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                lineNumber: 639,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                        lineNumber: 637,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, tender._id, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 615,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 613,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 649,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                children: \"لا توجد مناقصات حديثة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 650,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-4\",\n                                                children: \"ابدأ بإنشاء مناقصتك الأولى\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 651,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: ()=>router.push(\"/government/create-tender\"),\n                                                children: \"إنشاء مناقصة جديدة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 652,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 648,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 611,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 591,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-5 w-5 text-blue-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                            lineNumber: 665,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"الطلبات الحديثة\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 664,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>router.push(\"/government/applications\"),\n                                                    children: [\n                                                        \"عرض الكل\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                            lineNumber: 674,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 668,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 663,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"آخر الطلبات المقدمة على المناقصات\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 677,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 662,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: recentApplications.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: recentApplications.slice(0, 3).filter((application)=>application && application._id).map((application)=>{\n                                            var _application_applicant_profile, _application_applicant, _application_applicant_profile1, _application_applicant1;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-gray-900\",\n                                                                children: application.tender.title\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                lineNumber: 690,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-4 mt-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                                            className: \"h-4 w-4 inline ml-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                            lineNumber: 693,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        ((_application_applicant = application.applicant) === null || _application_applicant === void 0 ? void 0 : (_application_applicant_profile = _application_applicant.profile) === null || _application_applicant_profile === void 0 ? void 0 : _application_applicant_profile.companyName) || ((_application_applicant1 = application.applicant) === null || _application_applicant1 === void 0 ? void 0 : (_application_applicant_profile1 = _application_applicant1.profile) === null || _application_applicant_profile1 === void 0 ? void 0 : _application_applicant_profile1.fullName) || \"غير محدد\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                    lineNumber: 692,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                lineNumber: 691,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 mt-1\",\n                                                                children: formatRelativeTime(application.submittedAt)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                lineNumber: 697,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                        lineNumber: 689,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-end gap-2\",\n                                                        children: [\n                                                            getApplicationStatusBadge(application.status),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>router.push(\"/government/applications/\".concat(application._id)),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                                                        className: \"h-4 w-4 ml-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                        lineNumber: 708,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"مراجعة\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                lineNumber: 703,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                        lineNumber: 701,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, application._id, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 685,\n                                                columnNumber: 21\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 683,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 717,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                children: \"لا توجد طلبات حديثة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 718,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"ستظهر هنا الطلبات المقدمة على مناقصاتك\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 719,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 716,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 681,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 661,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 589,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n            lineNumber: 265,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n_s(GovernmentDashboard, \"9xoAi1BuwjIaM/cSa1syjT/e89k=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast,\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter\n    ];\n});\n_c = GovernmentDashboard;\nvar _c;\n$RefreshReg$(_c, \"GovernmentDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/government/dashboard/page.tsx\n"));

/***/ })

});