"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/government/dashboard/page",{

/***/ "(app-pages-browser)/./app/government/dashboard/page.tsx":
/*!*******************************************!*\
  !*** ./app/government/dashboard/page.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GovernmentDashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.tsx\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./components/DashboardLayout.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/line-chart.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pie-chart.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,BarChart3,Briefcase,Building,CheckCircle,Clock,DollarSign,Eye,FileText,LineChart,PieChart,PlusCircle,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/AreaChart.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Legend.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Area.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/PieChart.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/polar/Pie.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Cell.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/BarChart.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Bar.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction GovernmentDashboard() {\n    var _user_profile;\n    _s();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [recentTenders, setRecentTenders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [recentApplications, setRecentApplications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [chartData, setChartData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        budgetOverTime: [],\n        tenderPerformance: [],\n        categoryDistribution: [],\n        applicationTrends: []\n    });\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    // Chart colors\n    const COLORS = {\n        primary: \"#059669\",\n        secondary: \"#7C3AED\",\n        success: \"#10B981\",\n        warning: \"#F59E0B\",\n        danger: \"#EF4444\",\n        info: \"#06B6D4\"\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const userData = localStorage.getItem(\"user\");\n        if (userData) {\n            setUser(JSON.parse(userData));\n        }\n        loadDashboardData();\n    }, []);\n    const loadDashboardData = async ()=>{\n        try {\n            setLoading(true);\n            // Load government stats\n            const statsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_8__[\"default\"].get(\"/government/dashboard/stats\");\n            if (statsResponse.data.success) {\n                setStats(statsResponse.data.data);\n            }\n            // Load recent tenders\n            const tendersResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_8__[\"default\"].get(\"/government/tenders?limit=5&sort=-createdAt\");\n            if (tendersResponse.data.success) {\n                setRecentTenders(tendersResponse.data.data.tenders || []);\n            }\n            // Load recent applications\n            const applicationsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_8__[\"default\"].get(\"/government/applications/recent?limit=5\");\n            if (applicationsResponse.data.success) {\n                setRecentApplications(applicationsResponse.data.data.applications || []);\n            }\n            // Load notifications\n            const notificationsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_8__[\"default\"].get(\"/government/notifications?limit=5\");\n            if (notificationsResponse.data.success) {\n                setNotifications(notificationsResponse.data.data.notifications || []);\n            }\n            // Load chart data\n            const chartResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_8__[\"default\"].get(\"/government/analytics/charts\");\n            if (chartResponse.data.success) {\n                setChartData({\n                    budgetOverTime: chartResponse.data.data.budgetOverTime || [],\n                    tenderPerformance: chartResponse.data.data.tenderPerformance || [],\n                    categoryDistribution: chartResponse.data.data.categoryDistribution || [],\n                    applicationTrends: chartResponse.data.data.applicationTrends || []\n                });\n            }\n        } catch (error) {\n            console.error(\"Error loading dashboard data:\", error);\n            toast({\n                title: \"خطأ في التحميل\",\n                description: \"حدث خطأ في تحميل بيانات لوحة التحكم\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const formatPrice = (price)=>{\n        return new Intl.NumberFormat(\"ar-SA\", {\n            style: \"currency\",\n            currency: \"SAR\",\n            minimumFractionDigits: 0\n        }).format(price);\n    };\n    const formatTimeRemaining = (deadline)=>{\n        const now = new Date();\n        const end = new Date(deadline);\n        const diff = end.getTime() - now.getTime();\n        if (diff <= 0) return \"انتهى\";\n        const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n        const hours = Math.floor(diff % (1000 * 60 * 60 * 24) / (1000 * 60 * 60));\n        if (days > 0) return \"\".concat(days, \" يوم\");\n        if (hours > 0) return \"\".concat(hours, \" ساعة\");\n        return \"أقل من ساعة\";\n    };\n    const formatRelativeTime = (dateString)=>{\n        const now = new Date();\n        const date = new Date(dateString);\n        const diff = now.getTime() - date.getTime();\n        const minutes = Math.floor(diff / (1000 * 60));\n        const hours = Math.floor(diff / (1000 * 60 * 60));\n        const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n        if (days > 0) return \"منذ \".concat(days, \" يوم\");\n        if (hours > 0) return \"منذ \".concat(hours, \" ساعة\");\n        if (minutes > 0) return \"منذ \".concat(minutes, \" دقيقة\");\n        return \"الآن\";\n    };\n    const getTenderStatusBadge = (status)=>{\n        switch(status){\n            case \"active\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: \"bg-green-100 text-green-800\",\n                    children: \"نشط\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 16\n                }, this);\n            case \"completed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: \"bg-blue-100 text-blue-800\",\n                    children: \"مكتمل\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 16\n                }, this);\n            case \"review\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: \"bg-yellow-100 text-yellow-800\",\n                    children: \"قيد المراجعة\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 16\n                }, this);\n            case \"cancelled\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    variant: \"destructive\",\n                    children: \"ملغي\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    variant: \"outline\",\n                    children: status\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getApplicationStatusBadge = (status)=>{\n        switch(status){\n            case \"approved\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: \"bg-green-100 text-green-800\",\n                    children: \"موافق عليه\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 240,\n                    columnNumber: 16\n                }, this);\n            case \"rejected\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    variant: \"destructive\",\n                    children: \"مرفوض\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 242,\n                    columnNumber: 16\n                }, this);\n            case \"review\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: \"bg-yellow-100 text-yellow-800\",\n                    children: \"قيد المراجعة\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    variant: \"outline\",\n                    children: status\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            allowedRoles: [\n                \"government\"\n            ],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-96\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-4 text-gray-600\",\n                            children: \"جاري تحميل بيانات الجهة الحكومية...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                lineNumber: 253,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n            lineNumber: 252,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        allowedRoles: [\n            \"government\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    className: \"bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold\",\n                                        children: [\n                                            \"مرحباً، \",\n                                            (user === null || user === void 0 ? void 0 : (_user_profile = user.profile) === null || _user_profile === void 0 ? void 0 : _user_profile.governmentEntity) || \"الجهة الحكومية\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-green-100 mt-2\",\n                                        children: \"إدارة المناقصات الحكومية ومتابعة المشاريع العامة\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: [\n                                                    (stats === null || stats === void 0 ? void 0 : stats.successRate) || 0,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-green-100\",\n                                                children: \"معدل النجاح\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: (stats === null || stats === void 0 ? void 0 : stats.totalProjects) || 0\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-green-100\",\n                                                children: \"إجمالي المشاريع\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 267,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            className: \"h-16 text-lg bg-green-600 hover:bg-green-700\",\n                            size: \"lg\",\n                            onClick: ()=>router.push(\"/government/create-tender\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-6 w-6 ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 13\n                                }, this),\n                                \"إنشاء مناقصة جديدة\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            className: \"h-16 text-lg border-blue-200 text-blue-700 hover:bg-blue-50\",\n                            size: \"lg\",\n                            onClick: ()=>router.push(\"/government/tenders\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-6 w-6 ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 13\n                                }, this),\n                                \"إدارة المناقصات\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 300,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            className: \"h-16 text-lg border-purple-200 text-purple-700 hover:bg-purple-50\",\n                            size: \"lg\",\n                            onClick: ()=>router.push(\"/government/applications\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-6 w-6 ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 13\n                                }, this),\n                                \"مراجعة الطلبات\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 309,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            className: \"h-16 text-lg border-orange-200 text-orange-700 hover:bg-orange-50\",\n                            size: \"lg\",\n                            onClick: ()=>router.push(\"/government/profile\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-6 w-6 ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 13\n                                }, this),\n                                \"الملف الشخصي\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 291,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-gradient-to-br from-green-50 to-green-100 border-green-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-green-600\",\n                                                    children: \"المناقصات النشطة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-3xl font-bold text-green-900\",\n                                                    children: (stats === null || stats === void 0 ? void 0 : stats.activeTenders) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-green-600 mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-3 w-3 inline ml-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                            lineNumber: 338,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"نشطة الآن\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-12 w-12 bg-green-200 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-6 w-6 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 331,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-blue-600\",\n                                                    children: \"المناقصات المكتملة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-3xl font-bold text-blue-900\",\n                                                    children: (stats === null || stats === void 0 ? void 0 : stats.completedTenders) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 354,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-blue-600 mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-3 w-3 inline ml-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                            lineNumber: 356,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"مكتملة\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-12 w-12 bg-blue-200 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-6 w-6 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                lineNumber: 350,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 349,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-purple-600\",\n                                                    children: \"إجمالي الميزانية\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-3xl font-bold text-purple-900\",\n                                                    children: formatPrice((stats === null || stats === void 0 ? void 0 : stats.totalBudget) || 0)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-purple-600 mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"h-3 w-3 inline ml-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"استثمار عام\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-12 w-12 bg-purple-200 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-6 w-6 text-purple-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 378,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 367,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-orange-600\",\n                                                    children: \"الطلبات المعلقة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-3xl font-bold text-orange-900\",\n                                                    children: (stats === null || stats === void 0 ? void 0 : stats.pendingReviews) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 390,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-orange-600 mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-3 w-3 inline ml-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                            lineNumber: 392,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"تحتاج مراجعة\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 391,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-12 w-12 bg-orange-200 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-6 w-6 text-orange-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                lineNumber: 386,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 385,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 330,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"h-5 w-5 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"الميزانية الشهرية\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"تطور الميزانية وعدد المناقصات خلال الأشهر الماضية\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 413,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: chartData.budgetOverTime && chartData.budgetOverTime.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_21__.ResponsiveContainer, {\n                                        width: \"100%\",\n                                        height: 300,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_22__.AreaChart, {\n                                            data: chartData.budgetOverTime,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_23__.CartesianGrid, {\n                                                    strokeDasharray: \"3 3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_24__.XAxis, {\n                                                    dataKey: \"month\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_25__.YAxis, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_26__.Tooltip, {\n                                                    formatter: (value, name)=>[\n                                                            name === \"budget\" ? formatPrice(value) : value,\n                                                            name === \"budget\" ? \"الميزانية\" : \"المناقصات\"\n                                                        ]\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_27__.Legend, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_28__.Area, {\n                                                    type: \"monotone\",\n                                                    dataKey: \"budget\",\n                                                    stackId: \"1\",\n                                                    stroke: COLORS.primary,\n                                                    fill: COLORS.primary,\n                                                    fillOpacity: 0.6,\n                                                    name: \"الميزانية\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_28__.Area, {\n                                                    type: \"monotone\",\n                                                    dataKey: \"tenders\",\n                                                    stackId: \"2\",\n                                                    stroke: COLORS.success,\n                                                    fill: COLORS.success,\n                                                    fillOpacity: 0.8,\n                                                    name: \"المناقصات\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 438,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center h-[300px] text-gray-500\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                    className: \"h-12 w-12 mx-auto mb-2 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"لا توجد بيانات للعرض\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 451,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 417,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 407,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                    className: \"h-5 w-5 text-purple-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 464,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"أداء المناقصات\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 463,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"توزيع المناقصات حسب الحالة\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 467,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 462,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: chartData.tenderPerformance && chartData.tenderPerformance.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_21__.ResponsiveContainer, {\n                                        width: \"100%\",\n                                        height: 300,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_31__.PieChart, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_32__.Pie, {\n                                                    data: chartData.tenderPerformance,\n                                                    cx: \"50%\",\n                                                    cy: \"50%\",\n                                                    labelLine: false,\n                                                    label: (param)=>{\n                                                        let { status, percent } = param;\n                                                        return \"\".concat(status, \" \").concat((percent * 100).toFixed(0), \"%\");\n                                                    },\n                                                    outerRadius: 80,\n                                                    fill: \"#8884d8\",\n                                                    dataKey: \"count\",\n                                                    children: chartData.tenderPerformance.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_33__.Cell, {\n                                                            fill: entry.color\n                                                        }, \"cell-\".concat(index), false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                            lineNumber: 486,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 475,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_26__.Tooltip, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 489,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 474,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center h-[300px] text-gray-500\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                    className: \"h-12 w-12 mx-auto mb-2 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 495,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"لا توجد بيانات للعرض\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 496,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 493,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 471,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 461,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 405,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                    className: \"h-5 w-5 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"توزيع الفئات\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 509,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"توزيع المناقصات حسب الفئات المختلفة\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 513,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 508,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: chartData.categoryDistribution && chartData.categoryDistribution.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_21__.ResponsiveContainer, {\n                                        width: \"100%\",\n                                        height: 300,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_31__.PieChart, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_32__.Pie, {\n                                                    data: chartData.categoryDistribution,\n                                                    cx: \"50%\",\n                                                    cy: \"50%\",\n                                                    innerRadius: 60,\n                                                    outerRadius: 100,\n                                                    paddingAngle: 5,\n                                                    dataKey: \"value\",\n                                                    children: chartData.categoryDistribution.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_33__.Cell, {\n                                                            fill: entry.color\n                                                        }, \"cell-\".concat(index), false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                            lineNumber: 531,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 521,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_26__.Tooltip, {\n                                                    formatter: (value)=>\"\".concat(value, \"%\")\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 534,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_27__.Legend, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 535,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 520,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 519,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center h-[300px] text-gray-500\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                    className: \"h-12 w-12 mx-auto mb-2 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 541,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"لا توجد بيانات للعرض\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 542,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 540,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 539,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 517,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 507,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                    className: \"h-5 w-5 text-orange-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 553,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"اتجاه الطلبات\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 552,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"الطلبات المقدمة والموافق عليها خلال الأسبوع\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 556,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 551,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: chartData.applicationTrends && chartData.applicationTrends.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_21__.ResponsiveContainer, {\n                                        width: \"100%\",\n                                        height: 300,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_35__.BarChart, {\n                                            data: chartData.applicationTrends,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_23__.CartesianGrid, {\n                                                    strokeDasharray: \"3 3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 564,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_24__.XAxis, {\n                                                    dataKey: \"day\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 565,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_25__.YAxis, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 566,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_26__.Tooltip, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 567,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_27__.Legend, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 568,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_36__.Bar, {\n                                                    dataKey: \"applications\",\n                                                    fill: COLORS.primary,\n                                                    name: \"الطلبات\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 569,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_36__.Bar, {\n                                                    dataKey: \"approved\",\n                                                    fill: COLORS.success,\n                                                    name: \"الموافق عليها\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 570,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 563,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 562,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center h-[300px] text-gray-500\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                    className: \"h-12 w-12 mx-auto mb-2 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 576,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"لا توجد بيانات للعرض\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 577,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 575,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 574,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 560,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 550,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 505,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-5 w-5 text-green-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                            lineNumber: 592,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"المناقصات الحديثة\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 591,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>router.push(\"/government/tenders\"),\n                                                    children: [\n                                                        \"عرض الكل\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                            lineNumber: 601,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 595,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 590,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"آخر المناقصات التي تم إنشاؤها\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 604,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 589,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: recentTenders.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: recentTenders.slice(0, 3).map((tender)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer\",\n                                                onClick: ()=>router.push(\"/tenders/\".concat(tender._id)),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-gray-900\",\n                                                                children: tender.title\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                lineNumber: 618,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-4 mt-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"h-4 w-4 inline ml-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                                lineNumber: 621,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            formatPrice(tender.budget)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                        lineNumber: 620,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                                                className: \"h-4 w-4 inline ml-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                                lineNumber: 625,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            tender.applicationsCount,\n                                                                            \" طلب\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                        lineNumber: 624,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                                                                className: \"h-4 w-4 inline ml-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                                lineNumber: 629,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            tender.viewsCount,\n                                                                            \" مشاهدة\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                        lineNumber: 628,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                lineNumber: 619,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                        lineNumber: 617,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-end gap-2\",\n                                                        children: [\n                                                            getTenderStatusBadge(tender.status),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-3 w-3 inline ml-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                        lineNumber: 637,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    formatTimeRemaining(tender.deadline)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                lineNumber: 636,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                        lineNumber: 634,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, tender._id, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 612,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 610,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 646,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                children: \"لا توجد مناقصات حديثة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 647,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-4\",\n                                                children: \"ابدأ بإنشاء مناقصتك الأولى\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 648,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: ()=>router.push(\"/government/create-tender\"),\n                                                children: \"إنشاء مناقصة جديدة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 649,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 645,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 608,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 588,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-5 w-5 text-blue-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                            lineNumber: 662,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"الطلبات الحديثة\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 661,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>router.push(\"/government/applications\"),\n                                                    children: [\n                                                        \"عرض الكل\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                            lineNumber: 671,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                    lineNumber: 665,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 660,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"آخر الطلبات المقدمة على المناقصات\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                            lineNumber: 674,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 659,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: recentApplications.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: recentApplications.slice(0, 3).map((application)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-gray-900\",\n                                                                children: application.tender.title\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                lineNumber: 687,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-4 mt-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                                            className: \"h-4 w-4 inline ml-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                            lineNumber: 690,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        application.applicant.profile.companyName || application.applicant.profile.fullName\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                    lineNumber: 689,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                lineNumber: 688,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 mt-1\",\n                                                                children: formatRelativeTime(application.submittedAt)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                lineNumber: 694,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                        lineNumber: 686,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-end gap-2\",\n                                                        children: [\n                                                            getApplicationStatusBadge(application.status),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>router.push(\"/government/applications/\".concat(application._id)),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                                                        className: \"h-4 w-4 ml-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                        lineNumber: 705,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"مراجعة\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                                lineNumber: 700,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                        lineNumber: 698,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, application._id, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 682,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 680,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_BarChart3_Briefcase_Building_CheckCircle_Clock_DollarSign_Eye_FileText_LineChart_PieChart_PlusCircle_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 714,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                children: \"لا توجد طلبات حديثة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 715,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"ستظهر هنا الطلبات المقدمة على مناقصاتك\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                                lineNumber: 716,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                        lineNumber: 713,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                                    lineNumber: 678,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                            lineNumber: 658,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n                    lineNumber: 586,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n            lineNumber: 265,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/dashboard/page.tsx\",\n        lineNumber: 264,\n        columnNumber: 5\n    }, this);\n}\n_s(GovernmentDashboard, \"9xoAi1BuwjIaM/cSa1syjT/e89k=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast,\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter\n    ];\n});\n_c = GovernmentDashboard;\nvar _c;\n$RefreshReg$(_c, \"GovernmentDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/government/dashboard/page.tsx\n"));

/***/ })

});