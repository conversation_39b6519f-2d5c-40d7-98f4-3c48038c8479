"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/government/applications/[id]/page",{

/***/ "(app-pages-browser)/./app/government/applications/[id]/page.tsx":
/*!***************************************************!*\
  !*** ./app/government/applications/[id]/page.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ApplicationReviewPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_CheckCircle_Clock_Download_Edit_Eye_FileText_MessageSquare_Star_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,CheckCircle,Clock,Download,Edit,Eye,FileText,MessageSquare,Star,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_CheckCircle_Clock_Download_Edit_Eye_FileText_MessageSquare_Star_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,CheckCircle,Clock,Download,Edit,Eye,FileText,MessageSquare,Star,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_CheckCircle_Clock_Download_Edit_Eye_FileText_MessageSquare_Star_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,CheckCircle,Clock,Download,Edit,Eye,FileText,MessageSquare,Star,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_CheckCircle_Clock_Download_Edit_Eye_FileText_MessageSquare_Star_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,CheckCircle,Clock,Download,Edit,Eye,FileText,MessageSquare,Star,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_CheckCircle_Clock_Download_Edit_Eye_FileText_MessageSquare_Star_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,CheckCircle,Clock,Download,Edit,Eye,FileText,MessageSquare,Star,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_CheckCircle_Clock_Download_Edit_Eye_FileText_MessageSquare_Star_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,CheckCircle,Clock,Download,Edit,Eye,FileText,MessageSquare,Star,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_CheckCircle_Clock_Download_Edit_Eye_FileText_MessageSquare_Star_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,CheckCircle,Clock,Download,Edit,Eye,FileText,MessageSquare,Star,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_CheckCircle_Clock_Download_Edit_Eye_FileText_MessageSquare_Star_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,CheckCircle,Clock,Download,Edit,Eye,FileText,MessageSquare,Star,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_CheckCircle_Clock_Download_Edit_Eye_FileText_MessageSquare_Star_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,CheckCircle,Clock,Download,Edit,Eye,FileText,MessageSquare,Star,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_CheckCircle_Clock_Download_Edit_Eye_FileText_MessageSquare_Star_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,CheckCircle,Clock,Download,Edit,Eye,FileText,MessageSquare,Star,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_CheckCircle_Clock_Download_Edit_Eye_FileText_MessageSquare_Star_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,CheckCircle,Clock,Download,Edit,Eye,FileText,MessageSquare,Star,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_CheckCircle_Clock_Download_Edit_Eye_FileText_MessageSquare_Star_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,CheckCircle,Clock,Download,Edit,Eye,FileText,MessageSquare,Star,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction ApplicationReviewPage() {\n    var _application_documents;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    const [application, setApplication] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [updating, setUpdating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [reviewNotes, setReviewNotes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [score, setScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadApplication();\n    }, [\n        params.id\n    ]);\n    const loadApplication = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_10__[\"default\"].get(\"/government/applications/\".concat(params.id));\n            if (response.data.success) {\n                var _appData_score;\n                const appData = response.data.data;\n                setApplication(appData);\n                setReviewNotes(appData.reviewNotes || \"\");\n                setScore(((_appData_score = appData.score) === null || _appData_score === void 0 ? void 0 : _appData_score.toString()) || \"\");\n            } else {\n                toast({\n                    title: \"خطأ\",\n                    description: \"لم يتم العثور على الطلب\",\n                    variant: \"destructive\"\n                });\n                router.push(\"/government/applications\");\n            }\n        } catch (error) {\n            console.error(\"Error loading application:\", error);\n            toast({\n                title: \"خطأ في التحميل\",\n                description: \"حدث خطأ في تحميل بيانات الطلب\",\n                variant: \"destructive\"\n            });\n            router.push(\"/government/applications\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const updateApplicationStatus = async (newStatus)=>{\n        try {\n            setUpdating(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_10__[\"default\"].patch(\"/government/applications/\".concat(params.id, \"/status\"), {\n                status: newStatus,\n                reviewNotes,\n                score: score ? parseInt(score) : undefined\n            });\n            if (response.data.success) {\n                setApplication({\n                    ...application,\n                    status: newStatus,\n                    reviewNotes,\n                    score: score ? parseInt(score) : undefined\n                });\n                toast({\n                    title: \"تم التحديث\",\n                    description: \"تم تحديث حالة الطلب إلى \".concat(getStatusText(newStatus))\n                });\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            toast({\n                title: \"خطأ في التحديث\",\n                description: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"حدث خطأ في تحديث الطلب\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setUpdating(false);\n        }\n    };\n    const formatPrice = (price)=>{\n        return new Intl.NumberFormat(\"ar-SA\", {\n            style: \"currency\",\n            currency: \"SAR\",\n            minimumFractionDigits: 0\n        }).format(price);\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"ar-SA\", {\n            year: \"numeric\",\n            month: \"long\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    const getStatusBadge = (status)=>{\n        switch(status){\n            case \"accepted\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    className: \"bg-green-100 text-green-800\",\n                    children: \"موافق عليه\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 16\n                }, this);\n            case \"rejected\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"destructive\",\n                    children: \"مرفوض\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 16\n                }, this);\n            case \"pending\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    className: \"bg-yellow-100 text-yellow-800\",\n                    children: \"قيد المراجعة\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"outline\",\n                    children: status\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getStatusText = (status)=>{\n        switch(status){\n            case \"accepted\":\n                return \"موافق عليه\";\n            case \"rejected\":\n                return \"مرفوض\";\n            case \"pending\":\n                return \"قيد المراجعة\";\n            default:\n                return status;\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DashboardLayout, {\n            allowedRoles: [\n                \"government\"\n            ],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-96\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-4 text-gray-600\",\n                            children: \"جاري تحميل تفاصيل الطلب...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                lineNumber: 183,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n            lineNumber: 182,\n            columnNumber: 7\n        }, this);\n    }\n    if (!application) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DashboardLayout, {\n            allowedRoles: [\n                \"government\"\n            ],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_CheckCircle_Clock_Download_Edit_Eye_FileText_MessageSquare_Star_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                        children: \"الطلب غير موجود\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-6\",\n                        children: \"لم يتم العثور على الطلب المطلوب\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        onClick: ()=>router.push(\"/government/applications\"),\n                        children: \"العودة للطلبات\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                lineNumber: 196,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n            lineNumber: 195,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DashboardLayout, {\n        allowedRoles: [\n            \"government\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"ghost\",\n                                    onClick: ()=>router.push(\"/government/applications\"),\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_CheckCircle_Clock_Download_Edit_Eye_FileText_MessageSquare_Star_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"العودة للطلبات\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-3xl font-bold\",\n                                            children: \"مراجعة الطلب\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"تفاصيل ومراجعة طلب المشاركة في المناقصة\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: getStatusBadge(application.status)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                    lineNumber: 212,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2 space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_CheckCircle_Clock_Download_Edit_Eye_FileText_MessageSquare_Star_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-5 w-5 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"معلومات المناقصة\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-lg\",\n                                                            children: application.tender.title\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                            lineNumber: 245,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-4 mt-2 text-sm text-gray-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"الميزانية: \",\n                                                                        formatPrice(application.tender.budget)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                                    lineNumber: 247,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"الموعد النهائي: \",\n                                                                        formatDate(application.tender.deadline)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                                    lineNumber: 248,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                            lineNumber: 246,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"outline\",\n                                                    onClick: ()=>router.push(\"/tenders/\".concat(application.tender._id)),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_CheckCircle_Clock_Download_Edit_Eye_FileText_MessageSquare_Star_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-4 w-4 ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                            lineNumber: 255,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"عرض المناقصة\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_CheckCircle_Clock_Download_Edit_Eye_FileText_MessageSquare_Star_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-5 w-5 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"معلومات المتقدم\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    className: \"text-sm font-medium text-gray-600\",\n                                                                    children: \"اسم الشركة/المتقدم\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                                    lineNumber: 272,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: application.applicant.profile.companyName || application.applicant.profile.fullName\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                                    lineNumber: 273,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                            lineNumber: 271,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    className: \"text-sm font-medium text-gray-600\",\n                                                                    children: \"البريد الإلكتروني\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                                    lineNumber: 278,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: application.applicant.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                                    lineNumber: 279,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        application.applicant.profile.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    className: \"text-sm font-medium text-gray-600\",\n                                                                    children: \"الهاتف\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                                    lineNumber: 283,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: application.applicant.profile.phone\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                                    lineNumber: 284,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        application.applicant.profile.website && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    className: \"text-sm font-medium text-gray-600\",\n                                                                    children: \"الموقع الإلكتروني\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                                    lineNumber: 289,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                        href: application.applicant.profile.website,\n                                                                        target: \"_blank\",\n                                                                        rel: \"noopener noreferrer\",\n                                                                        className: \"text-blue-600 hover:underline\",\n                                                                        children: application.applicant.profile.website\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                                        lineNumber: 291,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                                    lineNumber: 290,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                            lineNumber: 288,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 17\n                                                }, this),\n                                                application.applicant.profile.address && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"العنوان\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium\",\n                                                            children: application.applicant.profile.address\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 19\n                                                }, this),\n                                                application.applicant.profile.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"وصف الشركة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                            lineNumber: 308,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-700\",\n                                                            children: application.applicant.profile.description\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                            lineNumber: 309,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_CheckCircle_Clock_Download_Edit_Eye_FileText_MessageSquare_Star_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-5 w-5 text-purple-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"تفاصيل الطلب\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    className: \"text-sm font-medium text-gray-600\",\n                                                                    children: \"تاريخ التقديم\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                                    lineNumber: 326,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: formatDate(application.submittedAt)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                                    lineNumber: 327,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                            lineNumber: 325,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        application.proposedBudget && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    className: \"text-sm font-medium text-gray-600\",\n                                                                    children: \"الميزانية المقترحة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                                    lineNumber: 331,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: formatPrice(application.proposedBudget)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                                    lineNumber: 332,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                            lineNumber: 330,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        application.timeline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    className: \"text-sm font-medium text-gray-600\",\n                                                                    children: \"الجدول الزمني المقترح\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                                    lineNumber: 337,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: application.timeline\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                                    lineNumber: 338,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                            lineNumber: 336,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 17\n                                                }, this),\n                                                application.experience && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"الخبرة والمؤهلات\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                            lineNumber: 345,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-700 whitespace-pre-line\",\n                                                            children: application.experience\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                            lineNumber: 346,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 19\n                                                }, this),\n                                                application.notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"ملاحظات إضافية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                            lineNumber: 352,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-700 whitespace-pre-line\",\n                                                            children: application.notes\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                            lineNumber: 353,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                    lineNumber: 351,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 13\n                                }, this),\n                                application.documents && application.documents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_CheckCircle_Clock_Download_Edit_Eye_FileText_MessageSquare_Star_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-5 w-5 text-orange-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"المستندات المرفقة\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: application.documents.map((document, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_CheckCircle_Clock_Download_Edit_Eye_FileText_MessageSquare_Star_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                                        lineNumber: 373,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: [\n                                                                            \"مستند \",\n                                                                            index + 1\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                                        lineNumber: 374,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                                lineNumber: 372,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_CheckCircle_Clock_Download_Edit_Eye_FileText_MessageSquare_Star_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"h-4 w-4 ml-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                                        lineNumber: 377,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"تحميل\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                                lineNumber: 376,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                lineNumber: 369,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_CheckCircle_Clock_Download_Edit_Eye_FileText_MessageSquare_Star_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"مراجعة الطلب\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                            lineNumber: 392,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                            htmlFor: \"score\",\n                                                            children: \"التقييم (من 100)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                            lineNumber: 400,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            id: \"score\",\n                                                            type: \"number\",\n                                                            min: \"0\",\n                                                            max: \"100\",\n                                                            value: score,\n                                                            onChange: (e)=>setScore(e.target.value),\n                                                            className: \"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500\",\n                                                            placeholder: \"أدخل التقييم\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                            lineNumber: 401,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                            htmlFor: \"reviewNotes\",\n                                                            children: \"ملاحظات المراجعة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                            lineNumber: 414,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                                            id: \"reviewNotes\",\n                                                            value: reviewNotes,\n                                                            onChange: (e)=>setReviewNotes(e.target.value),\n                                                            placeholder: \"أدخل ملاحظاتك حول الطلب...\",\n                                                            rows: 4\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                            lineNumber: 415,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                    lineNumber: 413,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_8__.Separator, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        (application === null || application === void 0 ? void 0 : application.status) === \"pending\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            onClick: ()=>updateApplicationStatus(\"accepted\"),\n                                                            disabled: updating,\n                                                            className: \"w-full bg-green-600 hover:bg-green-700\",\n                                                            children: [\n                                                                updating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                                    lineNumber: 435,\n                                                                    columnNumber: 25\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_CheckCircle_Clock_Download_Edit_Eye_FileText_MessageSquare_Star_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-4 w-4 ml-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                                    lineNumber: 437,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"الموافقة على الطلب\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                            lineNumber: 429,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        (application === null || application === void 0 ? void 0 : application.status) === \"pending\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            onClick: ()=>updateApplicationStatus(\"rejected\"),\n                                                            disabled: updating,\n                                                            variant: \"destructive\",\n                                                            className: \"w-full\",\n                                                            children: [\n                                                                updating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                                    lineNumber: 452,\n                                                                    columnNumber: 25\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_CheckCircle_Clock_Download_Edit_Eye_FileText_MessageSquare_Star_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 ml-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                                    lineNumber: 454,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"رفض الطلب\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                            lineNumber: 445,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        ((application === null || application === void 0 ? void 0 : application.status) === \"accepted\" || (application === null || application === void 0 ? void 0 : application.status) === \"rejected\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            onClick: ()=>updateApplicationStatus(\"pending\"),\n                                                            disabled: updating,\n                                                            variant: \"outline\",\n                                                            className: \"w-full\",\n                                                            children: [\n                                                                updating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 ml-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                                    lineNumber: 469,\n                                                                    columnNumber: 25\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_CheckCircle_Clock_Download_Edit_Eye_FileText_MessageSquare_Star_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"h-4 w-4 ml-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                                    lineNumber: 471,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"إعادة للمراجعة\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                            lineNumber: 462,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        (application === null || application === void 0 ? void 0 : application.status) === \"accepted\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center p-4 bg-green-50 rounded-lg border border-green-200\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_CheckCircle_Clock_Download_Edit_Eye_FileText_MessageSquare_Star_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-8 w-8 text-green-600 mx-auto mb-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                                    lineNumber: 480,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-green-800 font-medium\",\n                                                                    children: \"تم الموافقة على هذا الطلب\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                                    lineNumber: 481,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-green-600 text-sm\",\n                                                                    children: \"يمكنك إعادته للمراجعة إذا لزم الأمر\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                                    lineNumber: 482,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                            lineNumber: 479,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        (application === null || application === void 0 ? void 0 : application.status) === \"rejected\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center p-4 bg-red-50 rounded-lg border border-red-200\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_CheckCircle_Clock_Download_Edit_Eye_FileText_MessageSquare_Star_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-8 w-8 text-red-600 mx-auto mb-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                                    lineNumber: 488,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-red-800 font-medium\",\n                                                                    children: \"تم رفض هذا الطلب\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                                    lineNumber: 489,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-red-600 text-sm\",\n                                                                    children: \"يمكنك إعادته للمراجعة إذا لزم الأمر\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                                    lineNumber: 490,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                            lineNumber: 487,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                    lineNumber: 426,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 13\n                                }, this),\n                                (application.score || application.reviewNotes) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_CheckCircle_Clock_Download_Edit_Eye_FileText_MessageSquare_Star_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"h-5 w-5 text-yellow-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                        lineNumber: 502,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"المراجعة الحالية\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                application.score && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"التقييم\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                            lineNumber: 509,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_CheckCircle_Clock_Download_Edit_Eye_FileText_MessageSquare_Star_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-yellow-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                                    lineNumber: 511,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: [\n                                                                        application.score,\n                                                                        \"/100\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                                    lineNumber: 512,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                            lineNumber: 510,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 21\n                                                }, this),\n                                                application.reviewNotes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"ملاحظات المراجعة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                            lineNumber: 519,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-700 text-sm whitespace-pre-line\",\n                                                            children: application.reviewNotes\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                            lineNumber: 520,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                    lineNumber: 518,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                            lineNumber: 506,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                    lineNumber: 499,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"ملخص الطلب\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                lineNumber: 530,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                            lineNumber: 529,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-3 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600\",\n                                                            children: \"الحالة:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                            lineNumber: 534,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: getStatusText(application.status)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                            lineNumber: 535,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600\",\n                                                            children: \"تاريخ التقديم:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                            lineNumber: 538,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: new Date(application.submittedAt).toLocaleDateString(\"ar-SA\")\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                            lineNumber: 539,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                    lineNumber: 537,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600\",\n                                                            children: \"المستندات:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                            lineNumber: 542,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: [\n                                                                ((_application_documents = application.documents) === null || _application_documents === void 0 ? void 0 : _application_documents.length) || 0,\n                                                                \" مستند\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                            lineNumber: 543,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                    lineNumber: 541,\n                                                    columnNumber: 17\n                                                }, this),\n                                                application.proposedBudget && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600\",\n                                                            children: \"الميزانية المقترحة:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                            lineNumber: 547,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: formatPrice(application.proposedBudget)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                            lineNumber: 548,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                                    lineNumber: 546,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                            lineNumber: 532,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                                    lineNumber: 528,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                            lineNumber: 389,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n                    lineNumber: 232,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n            lineNumber: 210,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/[id]/page.tsx\",\n        lineNumber: 209,\n        columnNumber: 5\n    }, this);\n}\n_s(ApplicationReviewPage, \"Lld7hIkTGrpvbDFdJBRwJ2HrOf8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast\n    ];\n});\n_c = ApplicationReviewPage;\nvar _c;\n$RefreshReg$(_c, \"ApplicationReviewPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/government/applications/[id]/page.tsx\n"));

/***/ })

});