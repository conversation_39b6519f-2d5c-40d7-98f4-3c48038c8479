"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/government/applications/page",{

/***/ "(app-pages-browser)/./app/government/applications/page.tsx":
/*!**********************************************!*\
  !*** ./app/government/applications/page.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GovernmentApplicationsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./components/ui/table.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.tsx\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./components/DashboardLayout.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CheckCircle,Clock,Eye,FileText,Mail,RefreshCw,Search,Star,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CheckCircle,Clock,Eye,FileText,Mail,RefreshCw,Search,Star,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CheckCircle,Clock,Eye,FileText,Mail,RefreshCw,Search,Star,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CheckCircle,Clock,Eye,FileText,Mail,RefreshCw,Search,Star,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CheckCircle,Clock,Eye,FileText,Mail,RefreshCw,Search,Star,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CheckCircle,Clock,Eye,FileText,Mail,RefreshCw,Search,Star,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CheckCircle,Clock,Eye,FileText,Mail,RefreshCw,Search,Star,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CheckCircle,Clock,Eye,FileText,Mail,RefreshCw,Search,Star,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CheckCircle,Clock,Eye,FileText,Mail,RefreshCw,Search,Star,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CheckCircle,Clock,Eye,FileText,Mail,RefreshCw,Search,Star,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CheckCircle,Clock,Eye,FileText,Mail,RefreshCw,Search,Star,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction GovernmentApplicationsPage() {\n    _s();\n    const [applications, setApplications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredApplications, setFilteredApplications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [tenderFilter, setTenderFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        total: 0,\n        pending: 0,\n        accepted: 0,\n        rejected: 0\n    });\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadApplications();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        filterApplications();\n    }, [\n        applications,\n        searchTerm,\n        statusFilter,\n        tenderFilter\n    ]);\n    const loadApplications = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_11__[\"default\"].get(\"/government/applications/recent?limit=100\");\n            if (response.data.success) {\n                const applicationsData = response.data.data.applications || [];\n                setApplications(applicationsData);\n                calculateStats(applicationsData);\n            } else {\n                setApplications([]);\n                calculateStats([]);\n                toast({\n                    title: \"خطأ في التحميل\",\n                    description: \"حدث خطأ في تحميل بيانات الطلبات\",\n                    variant: \"destructive\"\n                });\n            }\n        } catch (error) {\n            console.error(\"Error loading applications:\", error);\n            setApplications([]);\n            calculateStats([]);\n            toast({\n                title: \"خطأ في التحميل\",\n                description: \"حدث خطأ في تحميل بيانات الطلبات\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const calculateStats = (applicationsData)=>{\n        if (!applicationsData || applicationsData.length === 0) {\n            setStats({\n                total: 0,\n                pending: 0,\n                accepted: 0,\n                rejected: 0\n            });\n            return;\n        }\n        const total = applicationsData.length;\n        const pending = applicationsData.filter((app)=>app.status === \"pending\").length;\n        const accepted = applicationsData.filter((app)=>app.status === \"accepted\").length;\n        const rejected = applicationsData.filter((app)=>app.status === \"rejected\").length;\n        setStats({\n            total,\n            pending,\n            accepted,\n            rejected\n        });\n    };\n    const filterApplications = ()=>{\n        if (!applications || applications.length === 0) {\n            setFilteredApplications([]);\n            return;\n        }\n        let filtered = applications;\n        if (searchTerm) {\n            filtered = filtered.filter((app)=>app.tender.title.toLowerCase().includes(searchTerm.toLowerCase()) || app.applicant.profile.companyName && app.applicant.profile.companyName.toLowerCase().includes(searchTerm.toLowerCase()) || app.applicant.profile.fullName && app.applicant.profile.fullName.toLowerCase().includes(searchTerm.toLowerCase()));\n        }\n        if (statusFilter !== \"all\") {\n            filtered = filtered.filter((app)=>app.status === statusFilter);\n        }\n        if (tenderFilter !== \"all\") {\n            filtered = filtered.filter((app)=>app.tender._id === tenderFilter);\n        }\n        setFilteredApplications(filtered);\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"ar-SA\");\n    };\n    const formatRelativeTime = (dateString)=>{\n        const now = new Date();\n        const date = new Date(dateString);\n        const diff = now.getTime() - date.getTime();\n        const minutes = Math.floor(diff / (1000 * 60));\n        const hours = Math.floor(diff / (1000 * 60 * 60));\n        const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n        if (days > 0) return \"منذ \".concat(days, \" يوم\");\n        if (hours > 0) return \"منذ \".concat(hours, \" ساعة\");\n        if (minutes > 0) return \"منذ \".concat(minutes, \" دقيقة\");\n        return \"الآن\";\n    };\n    const getStatusBadge = (status)=>{\n        switch(status){\n            case \"approved\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: \"bg-green-100 text-green-800\",\n                    children: \"موافق عليه\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 16\n                }, this);\n            case \"rejected\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    variant: \"destructive\",\n                    children: \"مرفوض\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 16\n                }, this);\n            case \"review\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: \"bg-yellow-100 text-yellow-800\",\n                    children: \"قيد المراجعة\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    variant: \"outline\",\n                    children: status\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getUniqueTenders = ()=>{\n        if (!applications || applications.length === 0) return [];\n        const tenders = Array.from(new Set(applications.map((app)=>app.tender.title)));\n        return tenders.map((title)=>{\n            const app = applications.find((a)=>a.tender.title === title);\n            return {\n                title,\n                id: app === null || app === void 0 ? void 0 : app.tender._id\n            };\n        });\n    };\n    const handleUpdateStatus = async (applicationId, newStatus)=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_11__[\"default\"].patch(\"/government/applications/\".concat(applicationId, \"/status\"), {\n                status: newStatus\n            });\n            if (response.data.success) {\n                setApplications(applications.map((app)=>app._id === applicationId ? {\n                        ...app,\n                        status: newStatus\n                    } : app));\n                toast({\n                    title: \"تم التحديث\",\n                    description: \"تم تحديث حالة الطلب إلى \".concat(newStatus === \"approved\" ? \"موافق عليه\" : newStatus === \"rejected\" ? \"مرفوض\" : \"قيد المراجعة\")\n                });\n            }\n        } catch (error) {\n            toast({\n                title: \"خطأ في التحديث\",\n                description: \"حدث خطأ في تحديث حالة الطلب\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            allowedRoles: [\n                \"government\"\n            ],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-96\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-4 text-gray-600\",\n                            children: \"جاري تحميل الطلبات...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                    lineNumber: 221,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                lineNumber: 220,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n            lineNumber: 219,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n        allowedRoles: [\n            \"government\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    className: \"bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold\",\n                                        children: \"طلبات المشاركة\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-green-100 mt-1\",\n                                        children: \"مراجعة وإدارة طلبات المشاركة في المناقصات\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: ()=>window.open(\"/tenders\", \"_blank\"),\n                                        variant: \"outline\",\n                                        className: \"bg-white/10 border-white/20 text-white hover:bg-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"عرض المناقصات العامة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: loadApplications,\n                                        variant: \"outline\",\n                                        className: \"bg-white/10 border-white/20 text-white hover:bg-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-4 w-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"تحديث\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                    lineNumber: 234,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-blue-600\",\n                                                    children: \"إجمالي الطلبات\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-3xl font-bold text-blue-900\",\n                                                    children: stats.total\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-8 w-8 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-gradient-to-br from-yellow-50 to-yellow-100 border-yellow-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-yellow-600\",\n                                                    children: \"قيد المراجعة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-3xl font-bold text-yellow-900\",\n                                                    children: stats.pending\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-8 w-8 text-yellow-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-gradient-to-br from-green-50 to-green-100 border-green-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-green-600\",\n                                                    children: \"موافق عليها\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-3xl font-bold text-green-900\",\n                                                    children: stats.accepted\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-8 w-8 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-gradient-to-br from-red-50 to-red-100 border-red-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-red-600\",\n                                                    children: \"مرفوضة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-3xl font-bold text-red-900\",\n                                                    children: stats.rejected\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-8 w-8 text-red-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                    lineNumber: 262,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                placeholder: \"البحث في الطلبات...\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                className: \"pr-10\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                    value: statusFilter,\n                                    onValueChange: setStatusFilter,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                            className: \"w-full md:w-48\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                placeholder: \"فلترة حسب الحالة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                    value: \"all\",\n                                                    children: \"جميع الحالات\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                    value: \"review\",\n                                                    children: \"قيد المراجعة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                    value: \"approved\",\n                                                    children: \"موافق عليه\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                    lineNumber: 334,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                    value: \"rejected\",\n                                                    children: \"مرفوض\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                    value: tenderFilter,\n                                    onValueChange: setTenderFilter,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                            className: \"w-full md:w-64\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                placeholder: \"فلترة حسب المناقصة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                    value: \"all\",\n                                                    children: \"جميع المناقصات\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 19\n                                                }, this),\n                                                getUniqueTenders().map((tender)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                        value: tender.id || \"\",\n                                                        children: tender.title\n                                                    }, tender.id || tender.title, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                    lineNumber: 313,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-5 w-5 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"قائمة الطلبات\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    children: [\n                                        \"مراجعة وإدارة طلبات المشاركة في المناقصات (\",\n                                        (filteredApplications || []).length,\n                                        \" من \",\n                                        (applications || []).length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                    lineNumber: 362,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                            lineNumber: 357,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: (filteredApplications || []).length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.Table, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableRow, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                        className: \"w-[250px]\",\n                                                        children: \"تفاصيل المناقصة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                        className: \"w-[200px]\",\n                                                        children: \"المتقدم\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                        children: \"تاريخ التقديم\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                        children: \"التقييم\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                        children: \"الحالة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                        className: \"text-center\",\n                                                        children: \"الإجراءات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableBody, {\n                                            children: (filteredApplications || []).map((app)=>{\n                                                var _app_tender, _app_applicant_profile, _app_applicant, _app_applicant_profile1, _app_applicant1, _app_applicant2;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableRow, {\n                                                    className: \"hover:bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium text-gray-900\",\n                                                                        children: ((_app_tender = app.tender) === null || _app_tender === void 0 ? void 0 : _app_tender.title) || \"غير محدد\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                        lineNumber: 385,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2 text-xs text-gray-500\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                className: \"h-3 w-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                                lineNumber: 387,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            (app.documents || []).length,\n                                                                            \" مستند مرفق\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                        lineNumber: 386,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                lineNumber: 384,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                            lineNumber: 383,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                className: \"h-4 w-4 text-gray-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                                lineNumber: 395,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium\",\n                                                                                children: ((_app_applicant = app.applicant) === null || _app_applicant === void 0 ? void 0 : (_app_applicant_profile = _app_applicant.profile) === null || _app_applicant_profile === void 0 ? void 0 : _app_applicant_profile.companyName) || ((_app_applicant1 = app.applicant) === null || _app_applicant1 === void 0 ? void 0 : (_app_applicant_profile1 = _app_applicant1.profile) === null || _app_applicant_profile1 === void 0 ? void 0 : _app_applicant_profile1.fullName) || \"غير محدد\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                                lineNumber: 396,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                        lineNumber: 394,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2 text-sm text-gray-500\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                className: \"h-3 w-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                                lineNumber: 401,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            ((_app_applicant2 = app.applicant) === null || _app_applicant2 === void 0 ? void 0 : _app_applicant2.email) || \"غير محدد\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                        lineNumber: 400,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                lineNumber: 393,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                            lineNumber: 392,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-medium\",\n                                                                        children: formatDate(app.submittedAt)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                        lineNumber: 408,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                className: \"h-3 w-3 inline ml-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                                lineNumber: 410,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            formatRelativeTime(app.submittedAt)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                        lineNumber: 409,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                lineNumber: 407,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                            children: app.score ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-yellow-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                        lineNumber: 418,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: [\n                                                                            app.score,\n                                                                            \"/100\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                        lineNumber: 419,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                lineNumber: 417,\n                                                                columnNumber: 29\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-400\",\n                                                                children: \"غير مقيم\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                lineNumber: 422,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                            lineNumber: 415,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                            children: getStatusBadge(app.status)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                            lineNumber: 425,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"sm\",\n                                                                        onClick: ()=>router.push(\"/government/applications/\".concat(app._id)),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                className: \"h-4 w-4 ml-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                                lineNumber: 433,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            \"عرض\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                        lineNumber: 428,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    app.status === \"review\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                variant: \"default\",\n                                                                                size: \"sm\",\n                                                                                className: \"bg-green-600 hover:bg-green-700\",\n                                                                                onClick: ()=>handleUpdateStatus(app._id, \"approved\"),\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                        className: \"h-4 w-4 ml-1\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                                        lineNumber: 444,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    \"موافقة\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                                lineNumber: 438,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                variant: \"destructive\",\n                                                                                size: \"sm\",\n                                                                                onClick: ()=>handleUpdateStatus(app._id, \"rejected\"),\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                        className: \"h-4 w-4 ml-1\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                                        lineNumber: 452,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    \"رفض\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                                lineNumber: 447,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                lineNumber: 427,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                            lineNumber: 426,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, app._id, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 23\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                        lineNumber: 466,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: searchTerm || statusFilter !== \"all\" || tenderFilter !== \"all\" ? \"لا توجد طلبات تطابق البحث\" : \"لا توجد طلبات مشاركة حتى الآن\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-4\",\n                                        children: searchTerm || statusFilter !== \"all\" || tenderFilter !== \"all\" ? \"جرب تغيير معايير البحث أو الفلترة\" : \"ستظهر هنا طلبات المشاركة عندما تقوم الشركات بالتقديم على مناقصاتك\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 17\n                                    }, this),\n                                    !searchTerm && statusFilter === \"all\" && tenderFilter === \"all\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-md mx-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"h-5 w-5 text-blue-600 mt-0.5\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                            lineNumber: 484,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                        lineNumber: 483,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                    lineNumber: 482,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-blue-800\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium mb-1\",\n                                                            children: \"كيف تعمل طلبات المشاركة؟\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                            lineNumber: 488,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"text-xs space-y-1 text-blue-700\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"• الشركات تتصفح مناقصاتك المنشورة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                    lineNumber: 490,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"• تقوم بتقديم طلبات المشاركة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                    lineNumber: 491,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"• ستظهر هنا لمراجعتها والموافقة عليها\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                    lineNumber: 492,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                            lineNumber: 489,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                    lineNumber: 487,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                            lineNumber: 481,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                        lineNumber: 480,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                lineNumber: 465,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                            lineNumber: 366,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                    lineNumber: 356,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n            lineNumber: 232,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n        lineNumber: 231,\n        columnNumber: 5\n    }, this);\n}\n_s(GovernmentApplicationsPage, \"bPHLd22jFf6/8C3mN4JZkq6BJZA=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast,\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter\n    ];\n});\n_c = GovernmentApplicationsPage;\nvar _c;\n$RefreshReg$(_c, \"GovernmentApplicationsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/government/applications/page.tsx\n"));

/***/ })

});