"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/government/applications/page",{

/***/ "(app-pages-browser)/./app/government/applications/page.tsx":
/*!**********************************************!*\
  !*** ./app/government/applications/page.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GovernmentApplicationsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./components/ui/table.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.tsx\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./components/DashboardLayout.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CheckCircle,Clock,Eye,FileText,Mail,RefreshCw,Search,Star,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CheckCircle,Clock,Eye,FileText,Mail,RefreshCw,Search,Star,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CheckCircle,Clock,Eye,FileText,Mail,RefreshCw,Search,Star,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CheckCircle,Clock,Eye,FileText,Mail,RefreshCw,Search,Star,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CheckCircle,Clock,Eye,FileText,Mail,RefreshCw,Search,Star,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CheckCircle,Clock,Eye,FileText,Mail,RefreshCw,Search,Star,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CheckCircle,Clock,Eye,FileText,Mail,RefreshCw,Search,Star,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CheckCircle,Clock,Eye,FileText,Mail,RefreshCw,Search,Star,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CheckCircle,Clock,Eye,FileText,Mail,RefreshCw,Search,Star,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CheckCircle,Clock,Eye,FileText,Mail,RefreshCw,Search,Star,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CheckCircle,Clock,Eye,FileText,Mail,RefreshCw,Search,Star,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction GovernmentApplicationsPage() {\n    _s();\n    const [applications, setApplications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredApplications, setFilteredApplications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [tenderFilter, setTenderFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        total: 0,\n        pending: 0,\n        approved: 0,\n        rejected: 0\n    });\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadApplications();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        filterApplications();\n    }, [\n        applications,\n        searchTerm,\n        statusFilter,\n        tenderFilter\n    ]);\n    const loadApplications = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_11__[\"default\"].get(\"/government/applications/recent?limit=100\");\n            if (response.data.success) {\n                setApplications(response.data.data.applications);\n                calculateStats(response.data.data.applications);\n            } else {\n                toast({\n                    title: \"خطأ في التحميل\",\n                    description: \"حدث خطأ في تحميل بيانات الطلبات\",\n                    variant: \"destructive\"\n                });\n            }\n        } catch (error) {\n            console.error(\"Error loading applications:\", error);\n            toast({\n                title: \"خطأ في التحميل\",\n                description: \"حدث خطأ في تحميل بيانات الطلبات\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const calculateStats = (applicationsData)=>{\n        if (!applicationsData || applicationsData.length === 0) {\n            setStats({\n                total: 0,\n                pending: 0,\n                approved: 0,\n                rejected: 0\n            });\n            return;\n        }\n        const total = applicationsData.length;\n        const pending = applicationsData.filter((app)=>app.status === \"pending\" || app.status === \"review\").length;\n        const approved = applicationsData.filter((app)=>app.status === \"approved\").length;\n        const rejected = applicationsData.filter((app)=>app.status === \"rejected\").length;\n        setStats({\n            total,\n            pending,\n            approved,\n            rejected\n        });\n    };\n    const filterApplications = ()=>{\n        if (!applications || applications.length === 0) {\n            setFilteredApplications([]);\n            return;\n        }\n        let filtered = applications;\n        if (searchTerm) {\n            filtered = filtered.filter((app)=>app.tender.title.toLowerCase().includes(searchTerm.toLowerCase()) || app.applicant.profile.companyName && app.applicant.profile.companyName.toLowerCase().includes(searchTerm.toLowerCase()) || app.applicant.profile.fullName && app.applicant.profile.fullName.toLowerCase().includes(searchTerm.toLowerCase()));\n        }\n        if (statusFilter !== \"all\") {\n            filtered = filtered.filter((app)=>app.status === statusFilter);\n        }\n        if (tenderFilter !== \"all\") {\n            filtered = filtered.filter((app)=>app.tender._id === tenderFilter);\n        }\n        setFilteredApplications(filtered);\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"ar-SA\");\n    };\n    const formatRelativeTime = (dateString)=>{\n        const now = new Date();\n        const date = new Date(dateString);\n        const diff = now.getTime() - date.getTime();\n        const minutes = Math.floor(diff / (1000 * 60));\n        const hours = Math.floor(diff / (1000 * 60 * 60));\n        const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n        if (days > 0) return \"منذ \".concat(days, \" يوم\");\n        if (hours > 0) return \"منذ \".concat(hours, \" ساعة\");\n        if (minutes > 0) return \"منذ \".concat(minutes, \" دقيقة\");\n        return \"الآن\";\n    };\n    const getStatusBadge = (status)=>{\n        switch(status){\n            case \"approved\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: \"bg-green-100 text-green-800\",\n                    children: \"موافق عليه\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 16\n                }, this);\n            case \"rejected\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    variant: \"destructive\",\n                    children: \"مرفوض\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 16\n                }, this);\n            case \"review\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: \"bg-yellow-100 text-yellow-800\",\n                    children: \"قيد المراجعة\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    variant: \"outline\",\n                    children: status\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getUniqueTenders = ()=>{\n        if (!applications || applications.length === 0) return [];\n        const tenders = Array.from(new Set(applications.map((app)=>app.tender.title)));\n        return tenders.map((title)=>{\n            const app = applications.find((a)=>a.tender.title === title);\n            return {\n                title,\n                id: app === null || app === void 0 ? void 0 : app.tender._id\n            };\n        });\n    };\n    const handleUpdateStatus = async (applicationId, newStatus)=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_11__[\"default\"].patch(\"/government/applications/\".concat(applicationId, \"/status\"), {\n                status: newStatus\n            });\n            if (response.data.success) {\n                setApplications(applications.map((app)=>app._id === applicationId ? {\n                        ...app,\n                        status: newStatus\n                    } : app));\n                toast({\n                    title: \"تم التحديث\",\n                    description: \"تم تحديث حالة الطلب إلى \".concat(newStatus === \"approved\" ? \"موافق عليه\" : newStatus === \"rejected\" ? \"مرفوض\" : \"قيد المراجعة\")\n                });\n            }\n        } catch (error) {\n            toast({\n                title: \"خطأ في التحديث\",\n                description: \"حدث خطأ في تحديث حالة الطلب\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            allowedRoles: [\n                \"government\"\n            ],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-96\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-4 text-gray-600\",\n                            children: \"جاري تحميل الطلبات...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                lineNumber: 215,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n            lineNumber: 214,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n        allowedRoles: [\n            \"government\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    className: \"bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold\",\n                                        children: \"طلبات المشاركة\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-green-100 mt-1\",\n                                        children: \"مراجعة وإدارة طلبات المشاركة في المناقصات\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: ()=>window.open(\"/tenders\", \"_blank\"),\n                                        variant: \"outline\",\n                                        className: \"bg-white/10 border-white/20 text-white hover:bg-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"عرض المناقصات العامة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: loadApplications,\n                                        variant: \"outline\",\n                                        className: \"bg-white/10 border-white/20 text-white hover:bg-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-4 w-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"تحديث\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-blue-600\",\n                                                    children: \"إجمالي الطلبات\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-3xl font-bold text-blue-900\",\n                                                    children: stats.total\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-8 w-8 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-gradient-to-br from-yellow-50 to-yellow-100 border-yellow-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-yellow-600\",\n                                                    children: \"قيد المراجعة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-3xl font-bold text-yellow-900\",\n                                                    children: stats.pending\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-8 w-8 text-yellow-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-gradient-to-br from-green-50 to-green-100 border-green-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-green-600\",\n                                                    children: \"موافق عليها\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-3xl font-bold text-green-900\",\n                                                    children: stats.approved\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-8 w-8 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-gradient-to-br from-red-50 to-red-100 border-red-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-red-600\",\n                                                    children: \"مرفوضة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-3xl font-bold text-red-900\",\n                                                    children: stats.rejected\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-8 w-8 text-red-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                    lineNumber: 257,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                placeholder: \"البحث في الطلبات...\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                className: \"pr-10\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                    value: statusFilter,\n                                    onValueChange: setStatusFilter,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                            className: \"w-full md:w-48\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                placeholder: \"فلترة حسب الحالة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                    value: \"all\",\n                                                    children: \"جميع الحالات\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                    value: \"review\",\n                                                    children: \"قيد المراجعة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                    value: \"approved\",\n                                                    children: \"موافق عليه\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                    value: \"rejected\",\n                                                    children: \"مرفوض\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                    value: tenderFilter,\n                                    onValueChange: setTenderFilter,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                            className: \"w-full md:w-64\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                placeholder: \"فلترة حسب المناقصة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                    value: \"all\",\n                                                    children: \"جميع المناقصات\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 19\n                                                }, this),\n                                                getUniqueTenders().map((tender)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                        value: tender.id,\n                                                        children: tender.title\n                                                    }, tender.id, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                    lineNumber: 308,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-5 w-5 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"قائمة الطلبات\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    children: [\n                                        \"مراجعة وإدارة طلبات المشاركة في المناقصات (\",\n                                        filteredApplications.length,\n                                        \" من \",\n                                        applications.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                            lineNumber: 352,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: filteredApplications.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.Table, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableRow, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                        className: \"w-[250px]\",\n                                                        children: \"تفاصيل المناقصة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                        className: \"w-[200px]\",\n                                                        children: \"المتقدم\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                        children: \"تاريخ التقديم\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                        children: \"التقييم\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                        children: \"الحالة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                        className: \"text-center\",\n                                                        children: \"الإجراءات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                lineNumber: 366,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableBody, {\n                                            children: filteredApplications.map((app)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableRow, {\n                                                    className: \"hover:bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium text-gray-900\",\n                                                                        children: app.tender.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                        lineNumber: 380,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2 text-xs text-gray-500\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                className: \"h-3 w-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                                lineNumber: 382,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            app.documents.length,\n                                                                            \" مستند مرفق\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                        lineNumber: 381,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                lineNumber: 379,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                            lineNumber: 378,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                className: \"h-4 w-4 text-gray-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                                lineNumber: 390,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium\",\n                                                                                children: app.applicant.profile.companyName || app.applicant.profile.fullName\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                                lineNumber: 391,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                        lineNumber: 389,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2 text-sm text-gray-500\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                className: \"h-3 w-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                                lineNumber: 396,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            app.applicant.email\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                        lineNumber: 395,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                lineNumber: 388,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                            lineNumber: 387,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-medium\",\n                                                                        children: formatDate(app.submittedAt)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                        lineNumber: 403,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                className: \"h-3 w-3 inline ml-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                                lineNumber: 405,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            formatRelativeTime(app.submittedAt)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                        lineNumber: 404,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                lineNumber: 402,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                            lineNumber: 401,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                            children: app.score ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-yellow-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                        lineNumber: 413,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: [\n                                                                            app.score,\n                                                                            \"/100\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                        lineNumber: 414,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                lineNumber: 412,\n                                                                columnNumber: 29\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-400\",\n                                                                children: \"غير مقيم\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                lineNumber: 417,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                            lineNumber: 410,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                            children: getStatusBadge(app.status)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                            lineNumber: 420,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"sm\",\n                                                                        onClick: ()=>router.push(\"/government/applications/\".concat(app._id)),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                className: \"h-4 w-4 ml-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                                lineNumber: 428,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            \"عرض\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                        lineNumber: 423,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    app.status === \"review\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                variant: \"default\",\n                                                                                size: \"sm\",\n                                                                                className: \"bg-green-600 hover:bg-green-700\",\n                                                                                onClick: ()=>handleUpdateStatus(app._id, \"approved\"),\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                        className: \"h-4 w-4 ml-1\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                                        lineNumber: 439,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    \"موافقة\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                                lineNumber: 433,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                variant: \"destructive\",\n                                                                                size: \"sm\",\n                                                                                onClick: ()=>handleUpdateStatus(app._id, \"rejected\"),\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                        className: \"h-4 w-4 ml-1\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                                        lineNumber: 447,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    \"رفض\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                                lineNumber: 442,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                lineNumber: 422,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                            lineNumber: 421,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, app._id, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Mail_RefreshCw_Search_Star_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: searchTerm || statusFilter !== \"all\" || tenderFilter !== \"all\" ? \"لا توجد طلبات تطابق البحث\" : \"لا توجد طلبات مشاركة حتى الآن\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                        lineNumber: 462,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-4\",\n                                        children: searchTerm || statusFilter !== \"all\" || tenderFilter !== \"all\" ? \"جرب تغيير معايير البحث أو الفلترة\" : \"ستظهر هنا طلبات المشاركة عندما تقوم الشركات بالتقديم على مناقصاتك\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 17\n                                    }, this),\n                                    !searchTerm && statusFilter === \"all\" && tenderFilter === \"all\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-md mx-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"h-5 w-5 text-blue-600 mt-0.5\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                            lineNumber: 479,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                        lineNumber: 478,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                    lineNumber: 477,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-blue-800\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium mb-1\",\n                                                            children: \"كيف تعمل طلبات المشاركة؟\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                            lineNumber: 483,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"text-xs space-y-1 text-blue-700\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"• الشركات تتصفح مناقصاتك المنشورة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                    lineNumber: 485,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"• تقوم بتقديم طلبات المشاركة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                    lineNumber: 486,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"• ستظهر هنا لمراجعتها والموافقة عليها\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                                    lineNumber: 487,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                            lineNumber: 484,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                                    lineNumber: 482,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                            lineNumber: 476,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                        lineNumber: 475,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                                lineNumber: 460,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                            lineNumber: 361,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n                    lineNumber: 351,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n            lineNumber: 227,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/applications/page.tsx\",\n        lineNumber: 226,\n        columnNumber: 5\n    }, this);\n}\n_s(GovernmentApplicationsPage, \"u79OghnKmh0hqwoxhj/gBd+iGEE=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast,\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter\n    ];\n});\n_c = GovernmentApplicationsPage;\nvar _c;\n$RefreshReg$(_c, \"GovernmentApplicationsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/government/applications/page.tsx\n"));

/***/ })

});