"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/government/tenders/[id]/edit/page",{

/***/ "(app-pages-browser)/./app/government/tenders/[id]/edit/page.tsx":
/*!***************************************************!*\
  !*** ./app/government/tenders/[id]/edit/page.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditTenderPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./components/DashboardLayout.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction EditTenderPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [tender, setTender] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hasProposals, setHasProposals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        description: \"\",\n        category: \"\",\n        budget: \"\",\n        deadline: \"\",\n        requirements: \"\",\n        location: \"\",\n        status: \"\"\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadTender();\n    }, [\n        params.id\n    ]);\n    const loadTender = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_11__.governmentAPI.tenders.getById(params.id);\n            if (response.data.success) {\n                var _tenderData_proposals, _tenderData_budget, _tenderData_location;\n                const tenderData = response.data.data.tender;\n                setTender(tenderData);\n                // Check if tender has proposals\n                const proposalsCount = ((_tenderData_proposals = tenderData.proposals) === null || _tenderData_proposals === void 0 ? void 0 : _tenderData_proposals.length) || 0;\n                setHasProposals(proposalsCount > 0);\n                // Format deadline for datetime-local input\n                const deadline = new Date(tenderData.deadline);\n                const formattedDeadline = deadline.toISOString().slice(0, 16);\n                setFormData({\n                    title: tenderData.title || \"\",\n                    description: tenderData.description || \"\",\n                    category: tenderData.category || \"\",\n                    budget: ((_tenderData_budget = tenderData.budget) === null || _tenderData_budget === void 0 ? void 0 : _tenderData_budget.toString()) || \"\",\n                    deadline: formattedDeadline,\n                    requirements: tenderData.requirements || \"\",\n                    location: typeof tenderData.location === \"object\" && ((_tenderData_location = tenderData.location) === null || _tenderData_location === void 0 ? void 0 : _tenderData_location.country) ? tenderData.location.country : tenderData.location || \"\",\n                    status: tenderData.status || \"draft\"\n                });\n            } else {\n                toast({\n                    title: \"خطأ\",\n                    description: \"لم يتم العثور على المناقصة\",\n                    variant: \"destructive\"\n                });\n                router.push(\"/government/tenders\");\n            }\n        } catch (error) {\n            console.error(\"Error loading tender:\", error);\n            toast({\n                title: \"خطأ في التحميل\",\n                description: \"حدث خطأ في تحميل بيانات المناقصة\",\n                variant: \"destructive\"\n            });\n            router.push(\"/government/tenders\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // Check if tender has proposals\n        if (hasProposals) {\n            toast({\n                title: \"لا يمكن التحديث\",\n                description: \"لا يمكن تحديث المناقصة بعد تلقي عروض من الشركات. هذا لضمان العدالة في المنافسة.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        if (!formData.title || !formData.description || !formData.category || !formData.budget || !formData.deadline) {\n            toast({\n                title: \"بيانات ناقصة\",\n                description: \"يرجى ملء جميع الحقول المطلوبة\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        const budget = parseFloat(formData.budget);\n        if (isNaN(budget) || budget <= 0) {\n            toast({\n                title: \"خطأ في الميزانية\",\n                description: \"يرجى إدخال ميزانية صحيحة\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        const deadline = new Date(formData.deadline);\n        if (deadline <= new Date()) {\n            toast({\n                title: \"خطأ في التاريخ\",\n                description: \"يجب أن يكون تاريخ الانتهاء في المستقبل\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            setSaving(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_11__.governmentAPI.tenders.update(params.id, {\n                title: formData.title,\n                description: formData.description,\n                category: formData.category,\n                budget,\n                deadline: deadline.toISOString(),\n                requirements: formData.requirements,\n                location: formData.location,\n                status: formData.status\n            });\n            if (response.data.success) {\n                toast({\n                    title: \"تم التحديث\",\n                    description: \"تم تحديث المناقصة بنجاح\"\n                });\n                router.push(\"/government/tenders\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            toast({\n                title: \"خطأ في التحديث\",\n                description: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"حدث خطأ في تحديث المناقصة\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setSaving(false);\n        }\n    };\n    const handleCancel = ()=>{\n        router.push(\"/government/tenders\");\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            allowedRoles: [\n                \"government\"\n            ],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-[400px]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"جاري تحميل بيانات المناقصة...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                lineNumber: 187,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n            lineNumber: 186,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n        allowedRoles: [\n            \"government\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    className: \"flex items-center justify-between\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"icon\",\n                                onClick: ()=>router.push(\"/government/tenders\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold\",\n                                        children: \"تعديل المناقصة\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"تحديث تفاصيل المناقصة\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 9\n                }, this),\n                hasProposals && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"border-orange-200 bg-orange-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"pt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-5 w-5 text-orange-600 mt-0.5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-orange-800 mb-1\",\n                                            children: \"تحذير: لا يمكن تعديل هذه المناقصة\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-orange-700 text-sm mb-3\",\n                                            children: \"تم تلقي عروض من الشركات لهذه المناقصة. لا يمكن تعديل تفاصيل المناقصة بعد تلقي العروض لضمان العدالة في المنافسة.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>router.push(\"/government/tenders/\".concat(params.id)),\n                                                    className: \"border-orange-300 text-orange-700 hover:bg-orange-100\",\n                                                    children: \"عرض المناقصة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>router.push(\"/government/applications\"),\n                                                    className: \"border-orange-300 text-orange-700 hover:bg-orange-100\",\n                                                    children: \"عرض العروض المقدمة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                    lineNumber: 218,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                children: \"تفاصيل المناقصة\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"title\",\n                                                        children: \"عنوان المناقصة *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        id: \"title\",\n                                                        value: formData.title,\n                                                        onChange: (e)=>handleInputChange(\"title\", e.target.value),\n                                                        placeholder: \"أدخل عنوان المناقصة\",\n                                                        required: true,\n                                                        disabled: hasProposals\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"category\",\n                                                        children: \"الفئة *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                        value: formData.category,\n                                                        onValueChange: (value)=>handleInputChange(\"category\", value),\n                                                        disabled: hasProposals,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                                    placeholder: \"اختر الفئة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                    lineNumber: 276,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                lineNumber: 275,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                        value: \"construction\",\n                                                                        children: \"إنشاءات\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                        lineNumber: 279,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                        value: \"technology\",\n                                                                        children: \"تكنولوجيا\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                        lineNumber: 280,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                        value: \"services\",\n                                                                        children: \"خدمات\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                        lineNumber: 281,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                        value: \"supplies\",\n                                                                        children: \"مستلزمات\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                        lineNumber: 282,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                        value: \"consulting\",\n                                                                        children: \"استشارات\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                        lineNumber: 283,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                        value: \"maintenance\",\n                                                                        children: \"صيانة\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                        lineNumber: 284,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                        value: \"other\",\n                                                                        children: \"أخرى\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                        lineNumber: 285,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                lineNumber: 278,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"budget\",\n                                                        children: \"الميزانية (ر.س) *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        id: \"budget\",\n                                                        type: \"number\",\n                                                        value: formData.budget,\n                                                        onChange: (e)=>handleInputChange(\"budget\", e.target.value),\n                                                        placeholder: \"أدخل الميزانية\",\n                                                        min: \"1\",\n                                                        required: true,\n                                                        disabled: hasProposals\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"deadline\",\n                                                        children: \"تاريخ الانتهاء *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                        lineNumber: 305,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        id: \"deadline\",\n                                                        type: \"datetime-local\",\n                                                        value: formData.deadline,\n                                                        onChange: (e)=>handleInputChange(\"deadline\", e.target.value),\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"location\",\n                                                        children: \"الموقع\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        id: \"location\",\n                                                        value: formData.location,\n                                                        onChange: (e)=>handleInputChange(\"location\", e.target.value),\n                                                        placeholder: \"أدخل موقع المناقصة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"status\",\n                                                        children: \"الحالة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                        value: formData.status,\n                                                        onValueChange: (value)=>handleInputChange(\"status\", value),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                                    placeholder: \"اختر الحالة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                    lineNumber: 329,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                        value: \"draft\",\n                                                                        children: \"مسودة\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                        lineNumber: 332,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                        value: \"open\",\n                                                                        children: \"مفتوحة\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                        lineNumber: 333,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                        value: \"closed\",\n                                                                        children: \"مغلقة\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                        lineNumber: 334,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                        value: \"awarded\",\n                                                                        children: \"تم الترسية\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                        lineNumber: 335,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                        value: \"cancelled\",\n                                                                        children: \"ملغية\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                        lineNumber: 336,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                lineNumber: 331,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"description\",\n                                                children: \"وصف المناقصة *\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                id: \"description\",\n                                                value: formData.description,\n                                                onChange: (e)=>handleInputChange(\"description\", e.target.value),\n                                                placeholder: \"أدخل وصف تفصيلي للمناقصة\",\n                                                rows: 4,\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"requirements\",\n                                                children: \"المتطلبات والشروط\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                id: \"requirements\",\n                                                value: formData.requirements,\n                                                onChange: (e)=>handleInputChange(\"requirements\", e.target.value),\n                                                placeholder: \"أدخل المتطلبات والشروط الخاصة بالمناقصة\",\n                                                rows: 4\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-4 pt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                type: \"submit\",\n                                                disabled: saving,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4 ml-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    saving ? \"جاري الحفظ...\" : \"حفظ التغييرات\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                lineNumber: 366,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                type: \"button\",\n                                                variant: \"outline\",\n                                                onClick: handleCancel,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-4 w-4 ml-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"إلغاء\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                    lineNumber: 253,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n            lineNumber: 199,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n        lineNumber: 198,\n        columnNumber: 5\n    }, this);\n}\n_s(EditTenderPage, \"fcinExA0wOV740NZIaFPfPucimM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast\n    ];\n});\n_c = EditTenderPage;\nvar _c;\n$RefreshReg$(_c, \"EditTenderPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/government/tenders/[id]/edit/page.tsx\n"));

/***/ })

});