"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/government/tenders/[id]/edit/page",{

/***/ "(app-pages-browser)/./app/government/tenders/[id]/edit/page.tsx":
/*!***************************************************!*\
  !*** ./app/government/tenders/[id]/edit/page.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditTenderPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./components/DashboardLayout.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction EditTenderPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [tender, setTender] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hasProposals, setHasProposals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        description: \"\",\n        category: \"\",\n        budget: \"\",\n        deadline: \"\",\n        requirements: \"\",\n        location: \"\",\n        status: \"\"\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadTender();\n    }, [\n        params.id\n    ]);\n    const loadTender = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_11__.governmentAPI.tenders.getById(params.id);\n            if (response.data.success) {\n                var _tenderData_proposals, _tenderData_budget, _tenderData_location;\n                const tenderData = response.data.data.tender;\n                setTender(tenderData);\n                // Check if tender has proposals\n                const proposalsCount = ((_tenderData_proposals = tenderData.proposals) === null || _tenderData_proposals === void 0 ? void 0 : _tenderData_proposals.length) || 0;\n                setHasProposals(proposalsCount > 0);\n                // Format deadline for datetime-local input\n                const deadline = new Date(tenderData.deadline);\n                const formattedDeadline = deadline.toISOString().slice(0, 16);\n                setFormData({\n                    title: tenderData.title || \"\",\n                    description: tenderData.description || \"\",\n                    category: tenderData.category || \"\",\n                    budget: ((_tenderData_budget = tenderData.budget) === null || _tenderData_budget === void 0 ? void 0 : _tenderData_budget.toString()) || \"\",\n                    deadline: formattedDeadline,\n                    requirements: tenderData.requirements || \"\",\n                    location: typeof tenderData.location === \"object\" && ((_tenderData_location = tenderData.location) === null || _tenderData_location === void 0 ? void 0 : _tenderData_location.country) ? tenderData.location.country : tenderData.location || \"\",\n                    status: tenderData.status || \"draft\"\n                });\n            } else {\n                toast({\n                    title: \"خطأ\",\n                    description: \"لم يتم العثور على المناقصة\",\n                    variant: \"destructive\"\n                });\n                router.push(\"/government/tenders\");\n            }\n        } catch (error) {\n            console.error(\"Error loading tender:\", error);\n            toast({\n                title: \"خطأ في التحميل\",\n                description: \"حدث خطأ في تحميل بيانات المناقصة\",\n                variant: \"destructive\"\n            });\n            router.push(\"/government/tenders\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // Check if tender has proposals\n        if (hasProposals) {\n            toast({\n                title: \"لا يمكن التحديث\",\n                description: \"لا يمكن تحديث المناقصة بعد تلقي عروض من الشركات. هذا لضمان العدالة في المنافسة.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        if (!formData.title || !formData.description || !formData.category || !formData.budget || !formData.deadline) {\n            toast({\n                title: \"بيانات ناقصة\",\n                description: \"يرجى ملء جميع الحقول المطلوبة\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        const budget = parseFloat(formData.budget);\n        if (isNaN(budget) || budget <= 0) {\n            toast({\n                title: \"خطأ في الميزانية\",\n                description: \"يرجى إدخال ميزانية صحيحة\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        const deadline = new Date(formData.deadline);\n        if (deadline <= new Date()) {\n            toast({\n                title: \"خطأ في التاريخ\",\n                description: \"يجب أن يكون تاريخ الانتهاء في المستقبل\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            setSaving(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_11__.governmentAPI.tenders.update(params.id, {\n                title: formData.title,\n                description: formData.description,\n                category: formData.category,\n                budget,\n                deadline: deadline.toISOString(),\n                requirements: formData.requirements,\n                location: formData.location,\n                status: formData.status\n            });\n            if (response.data.success) {\n                toast({\n                    title: \"تم التحديث\",\n                    description: \"تم تحديث المناقصة بنجاح\"\n                });\n                router.push(\"/government/tenders\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            toast({\n                title: \"خطأ في التحديث\",\n                description: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"حدث خطأ في تحديث المناقصة\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setSaving(false);\n        }\n    };\n    const handleCancel = ()=>{\n        router.push(\"/government/tenders\");\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            allowedRoles: [\n                \"government\"\n            ],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-[400px]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"جاري تحميل بيانات المناقصة...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                lineNumber: 187,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n            lineNumber: 186,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n        allowedRoles: [\n            \"government\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    className: \"flex items-center justify-between\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"icon\",\n                                onClick: ()=>router.push(\"/government/tenders\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold\",\n                                        children: \"تعديل المناقصة\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"تحديث تفاصيل المناقصة\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 9\n                }, this),\n                hasProposals && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"border-orange-200 bg-orange-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"pt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-5 w-5 text-orange-600 mt-0.5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-orange-800 mb-1\",\n                                            children: \"تحذير: لا يمكن تعديل هذه المناقصة\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-orange-700 text-sm mb-3\",\n                                            children: \"تم تلقي عروض من الشركات لهذه المناقصة. لا يمكن تعديل تفاصيل المناقصة بعد تلقي العروض لضمان العدالة في المنافسة.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>router.push(\"/government/tenders/\".concat(params.id)),\n                                                    className: \"border-orange-300 text-orange-700 hover:bg-orange-100\",\n                                                    children: \"عرض المناقصة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>router.push(\"/government/applications\"),\n                                                    className: \"border-orange-300 text-orange-700 hover:bg-orange-100\",\n                                                    children: \"عرض العروض المقدمة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                    lineNumber: 218,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                children: \"تفاصيل المناقصة\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"title\",\n                                                        children: \"عنوان المناقصة *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        id: \"title\",\n                                                        value: formData.title,\n                                                        onChange: (e)=>handleInputChange(\"title\", e.target.value),\n                                                        placeholder: \"أدخل عنوان المناقصة\",\n                                                        required: true,\n                                                        disabled: hasProposals\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"category\",\n                                                        children: \"الفئة *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                        value: formData.category,\n                                                        onValueChange: (value)=>handleInputChange(\"category\", value),\n                                                        disabled: hasProposals,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                                    placeholder: \"اختر الفئة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                    lineNumber: 276,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                lineNumber: 275,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                        value: \"construction\",\n                                                                        children: \"إنشاءات\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                        lineNumber: 279,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                        value: \"technology\",\n                                                                        children: \"تكنولوجيا\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                        lineNumber: 280,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                        value: \"services\",\n                                                                        children: \"خدمات\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                        lineNumber: 281,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                        value: \"supplies\",\n                                                                        children: \"مستلزمات\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                        lineNumber: 282,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                        value: \"consulting\",\n                                                                        children: \"استشارات\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                        lineNumber: 283,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                        value: \"maintenance\",\n                                                                        children: \"صيانة\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                        lineNumber: 284,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                        value: \"other\",\n                                                                        children: \"أخرى\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                        lineNumber: 285,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                lineNumber: 278,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"budget\",\n                                                        children: \"الميزانية (ر.س) *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        id: \"budget\",\n                                                        type: \"number\",\n                                                        value: formData.budget,\n                                                        onChange: (e)=>handleInputChange(\"budget\", e.target.value),\n                                                        placeholder: \"أدخل الميزانية\",\n                                                        min: \"1\",\n                                                        required: true,\n                                                        disabled: hasProposals\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"deadline\",\n                                                        children: \"تاريخ الانتهاء *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                        lineNumber: 305,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        id: \"deadline\",\n                                                        type: \"datetime-local\",\n                                                        value: formData.deadline,\n                                                        onChange: (e)=>handleInputChange(\"deadline\", e.target.value),\n                                                        required: true,\n                                                        disabled: hasProposals\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"location\",\n                                                        children: \"الموقع\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        id: \"location\",\n                                                        value: formData.location,\n                                                        onChange: (e)=>handleInputChange(\"location\", e.target.value),\n                                                        placeholder: \"أدخل موقع المناقصة\",\n                                                        disabled: hasProposals\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"status\",\n                                                        children: \"الحالة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                        value: formData.status,\n                                                        onValueChange: (value)=>handleInputChange(\"status\", value),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                                    placeholder: \"اختر الحالة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                    lineNumber: 331,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                lineNumber: 330,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                        value: \"draft\",\n                                                                        children: \"مسودة\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                        lineNumber: 334,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                        value: \"open\",\n                                                                        children: \"مفتوحة\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                        lineNumber: 335,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                        value: \"closed\",\n                                                                        children: \"مغلقة\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                        lineNumber: 336,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                        value: \"awarded\",\n                                                                        children: \"تم الترسية\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                        lineNumber: 337,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                        value: \"cancelled\",\n                                                                        children: \"ملغية\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                        lineNumber: 338,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                lineNumber: 333,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                        lineNumber: 329,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"description\",\n                                                children: \"وصف المناقصة *\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                id: \"description\",\n                                                value: formData.description,\n                                                onChange: (e)=>handleInputChange(\"description\", e.target.value),\n                                                placeholder: \"أدخل وصف تفصيلي للمناقصة\",\n                                                rows: 4,\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                lineNumber: 346,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"requirements\",\n                                                children: \"المتطلبات والشروط\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                id: \"requirements\",\n                                                value: formData.requirements,\n                                                onChange: (e)=>handleInputChange(\"requirements\", e.target.value),\n                                                placeholder: \"أدخل المتطلبات والشروط الخاصة بالمناقصة\",\n                                                rows: 4\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-4 pt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                type: \"submit\",\n                                                disabled: saving,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4 ml-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    saving ? \"جاري الحفظ...\" : \"حفظ التغييرات\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                type: \"button\",\n                                                variant: \"outline\",\n                                                onClick: handleCancel,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-4 w-4 ml-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"إلغاء\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                    lineNumber: 253,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n            lineNumber: 199,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n        lineNumber: 198,\n        columnNumber: 5\n    }, this);\n}\n_s(EditTenderPage, \"fcinExA0wOV740NZIaFPfPucimM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast\n    ];\n});\n_c = EditTenderPage;\nvar _c;\n$RefreshReg$(_c, \"EditTenderPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/government/tenders/[id]/edit/page.tsx\n"));

/***/ })

});