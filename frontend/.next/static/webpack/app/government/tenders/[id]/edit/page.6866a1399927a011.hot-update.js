"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/government/tenders/[id]/edit/page",{

/***/ "(app-pages-browser)/./app/government/tenders/[id]/edit/page.tsx":
/*!***************************************************!*\
  !*** ./app/government/tenders/[id]/edit/page.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditTenderPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction EditTenderPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [tender, setTender] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hasProposals, setHasProposals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        description: \"\",\n        category: \"\",\n        budget: \"\",\n        deadline: \"\",\n        requirements: \"\",\n        location: \"\",\n        status: \"\"\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadTender();\n    }, [\n        params.id\n    ]);\n    const loadTender = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_10__.governmentAPI.tenders.getById(params.id);\n            if (response.data.success) {\n                var _tenderData_proposals, _tenderData_budget, _tenderData_location;\n                const tenderData = response.data.data.tender;\n                setTender(tenderData);\n                // Check if tender has proposals\n                const proposalsCount = ((_tenderData_proposals = tenderData.proposals) === null || _tenderData_proposals === void 0 ? void 0 : _tenderData_proposals.length) || 0;\n                setHasProposals(proposalsCount > 0);\n                // Format deadline for datetime-local input\n                const deadline = new Date(tenderData.deadline);\n                const formattedDeadline = deadline.toISOString().slice(0, 16);\n                setFormData({\n                    title: tenderData.title || \"\",\n                    description: tenderData.description || \"\",\n                    category: tenderData.category || \"\",\n                    budget: ((_tenderData_budget = tenderData.budget) === null || _tenderData_budget === void 0 ? void 0 : _tenderData_budget.toString()) || \"\",\n                    deadline: formattedDeadline,\n                    requirements: tenderData.requirements || \"\",\n                    location: typeof tenderData.location === \"object\" && ((_tenderData_location = tenderData.location) === null || _tenderData_location === void 0 ? void 0 : _tenderData_location.country) ? tenderData.location.country : tenderData.location || \"\",\n                    status: tenderData.status || \"draft\"\n                });\n            } else {\n                toast({\n                    title: \"خطأ\",\n                    description: \"لم يتم العثور على المناقصة\",\n                    variant: \"destructive\"\n                });\n                router.push(\"/government/tenders\");\n            }\n        } catch (error) {\n            console.error(\"Error loading tender:\", error);\n            toast({\n                title: \"خطأ في التحميل\",\n                description: \"حدث خطأ في تحميل بيانات المناقصة\",\n                variant: \"destructive\"\n            });\n            router.push(\"/government/tenders\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // Check if tender has proposals\n        if (hasProposals) {\n            toast({\n                title: \"لا يمكن التحديث\",\n                description: \"لا يمكن تحديث المناقصة بعد تلقي عروض من الشركات. هذا لضمان العدالة في المنافسة.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        if (!formData.title || !formData.description || !formData.category || !formData.budget || !formData.deadline) {\n            toast({\n                title: \"بيانات ناقصة\",\n                description: \"يرجى ملء جميع الحقول المطلوبة\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        const budget = parseFloat(formData.budget);\n        if (isNaN(budget) || budget <= 0) {\n            toast({\n                title: \"خطأ في الميزانية\",\n                description: \"يرجى إدخال ميزانية صحيحة\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        const deadline = new Date(formData.deadline);\n        if (deadline <= new Date()) {\n            toast({\n                title: \"خطأ في التاريخ\",\n                description: \"يجب أن يكون تاريخ الانتهاء في المستقبل\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            setSaving(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_10__.governmentAPI.tenders.update(params.id, {\n                title: formData.title,\n                description: formData.description,\n                category: formData.category,\n                budget,\n                deadline: deadline.toISOString(),\n                requirements: formData.requirements,\n                location: formData.location,\n                status: formData.status\n            });\n            if (response.data.success) {\n                toast({\n                    title: \"تم التحديث\",\n                    description: \"تم تحديث المناقصة بنجاح\"\n                });\n                router.push(\"/government/tenders\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            toast({\n                title: \"خطأ في التحديث\",\n                description: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"حدث خطأ في تحديث المناقصة\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setSaving(false);\n        }\n    };\n    const handleCancel = ()=>{\n        router.push(\"/government/tenders\");\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-[400px]\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"جاري تحميل بيانات المناقصة...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                lineNumber: 187,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n            lineNumber: 186,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DashboardLayout, {\n        allowedRoles: [\n            \"government\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    className: \"flex items-center justify-between\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"icon\",\n                                onClick: ()=>router.push(\"/government/tenders\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold\",\n                                        children: \"تعديل المناقصة\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"تحديث تفاصيل المناقصة\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                    lineNumber: 198,\n                    columnNumber: 9\n                }, this),\n                hasProposals && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"border-orange-200 bg-orange-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"pt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-5 w-5 text-orange-600 mt-0.5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-orange-800 mb-1\",\n                                            children: \"تحذير: لا يمكن تعديل هذه المناقصة\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-orange-700 text-sm mb-3\",\n                                            children: \"تم تلقي عروض من الشركات لهذه المناقصة. لا يمكن تعديل تفاصيل المناقصة بعد تلقي العروض لضمان العدالة في المنافسة.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>router.push(\"/government/tenders/\".concat(params.id)),\n                                                    className: \"border-orange-300 text-orange-700 hover:bg-orange-100\",\n                                                    children: \"عرض المناقصة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>router.push(\"/government/applications\"),\n                                                    className: \"border-orange-300 text-orange-700 hover:bg-orange-100\",\n                                                    children: \"عرض العروض المقدمة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                children: \"تفاصيل المناقصة\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"title\",\n                                                        children: \"عنوان المناقصة *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        id: \"title\",\n                                                        value: formData.title,\n                                                        onChange: (e)=>handleInputChange(\"title\", e.target.value),\n                                                        placeholder: \"أدخل عنوان المناقصة\",\n                                                        required: true,\n                                                        disabled: hasProposals\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"category\",\n                                                        children: \"الفئة *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                        value: formData.category,\n                                                        onValueChange: (value)=>handleInputChange(\"category\", value),\n                                                        disabled: hasProposals,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                                    placeholder: \"اختر الفئة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                    lineNumber: 274,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                lineNumber: 273,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                        value: \"construction\",\n                                                                        children: \"إنشاءات\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                        lineNumber: 277,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                        value: \"technology\",\n                                                                        children: \"تكنولوجيا\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                        lineNumber: 278,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                        value: \"services\",\n                                                                        children: \"خدمات\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                        lineNumber: 279,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                        value: \"supplies\",\n                                                                        children: \"مستلزمات\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                        lineNumber: 280,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                        value: \"consulting\",\n                                                                        children: \"استشارات\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                        lineNumber: 281,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                        value: \"maintenance\",\n                                                                        children: \"صيانة\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                        lineNumber: 282,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                        value: \"other\",\n                                                                        children: \"أخرى\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                        lineNumber: 283,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                lineNumber: 276,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"budget\",\n                                                        children: \"الميزانية (ر.س) *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        id: \"budget\",\n                                                        type: \"number\",\n                                                        value: formData.budget,\n                                                        onChange: (e)=>handleInputChange(\"budget\", e.target.value),\n                                                        placeholder: \"أدخل الميزانية\",\n                                                        min: \"1\",\n                                                        required: true,\n                                                        disabled: hasProposals\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"deadline\",\n                                                        children: \"تاريخ الانتهاء *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        id: \"deadline\",\n                                                        type: \"datetime-local\",\n                                                        value: formData.deadline,\n                                                        onChange: (e)=>handleInputChange(\"deadline\", e.target.value),\n                                                        required: true,\n                                                        disabled: hasProposals\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"location\",\n                                                        children: \"الموقع\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        id: \"location\",\n                                                        value: formData.location,\n                                                        onChange: (e)=>handleInputChange(\"location\", e.target.value),\n                                                        placeholder: \"أدخل موقع المناقصة\",\n                                                        disabled: hasProposals\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"status\",\n                                                        children: \"الحالة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                        value: formData.status,\n                                                        onValueChange: (value)=>handleInputChange(\"status\", value),\n                                                        disabled: hasProposals,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                                    placeholder: \"اختر الحالة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                    lineNumber: 329,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                        value: \"draft\",\n                                                                        children: \"مسودة\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                        lineNumber: 332,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                        value: \"open\",\n                                                                        children: \"مفتوحة\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                        lineNumber: 333,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                        value: \"closed\",\n                                                                        children: \"مغلقة\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                        lineNumber: 334,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                        value: \"awarded\",\n                                                                        children: \"تم الترسية\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                        lineNumber: 335,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                        value: \"cancelled\",\n                                                                        children: \"ملغية\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                        lineNumber: 336,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                                lineNumber: 331,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"description\",\n                                                children: \"وصف المناقصة *\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                id: \"description\",\n                                                value: formData.description,\n                                                onChange: (e)=>handleInputChange(\"description\", e.target.value),\n                                                placeholder: \"أدخل وصف تفصيلي للمناقصة\",\n                                                rows: 4,\n                                                required: true,\n                                                disabled: hasProposals\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"requirements\",\n                                                children: \"المتطلبات والشروط\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                id: \"requirements\",\n                                                value: formData.requirements,\n                                                onChange: (e)=>handleInputChange(\"requirements\", e.target.value),\n                                                placeholder: \"أدخل المتطلبات والشروط الخاصة بالمناقصة\",\n                                                rows: 4,\n                                                disabled: hasProposals\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-4 pt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                type: \"submit\",\n                                                disabled: saving || hasProposals,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-4 w-4 ml-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    saving ? \"جاري الحفظ...\" : hasProposals ? \"لا يمكن التحديث\" : \"حفظ التغييرات\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                type: \"button\",\n                                                variant: \"outline\",\n                                                onClick: handleCancel,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4 ml-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"إلغاء\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n                    lineNumber: 251,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n            lineNumber: 197,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/tenders/[id]/edit/page.tsx\",\n        lineNumber: 196,\n        columnNumber: 5\n    }, this);\n}\n_s(EditTenderPage, \"fcinExA0wOV740NZIaFPfPucimM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast\n    ];\n});\n_c = EditTenderPage;\nvar _c;\n$RefreshReg$(_c, \"EditTenderPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/government/tenders/[id]/edit/page.tsx\n"));

/***/ })

});