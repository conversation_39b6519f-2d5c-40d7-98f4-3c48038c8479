"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/government/notifications/page",{

/***/ "(app-pages-browser)/./app/government/notifications/page.tsx":
/*!***********************************************!*\
  !*** ./app/government/notifications/page.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GovernmentNotificationsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,Check,CheckCheck,Clock,Eye,FileText,RefreshCw,Send,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,Check,CheckCheck,Clock,Eye,FileText,RefreshCw,Send,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,Check,CheckCheck,Clock,Eye,FileText,RefreshCw,Send,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,Check,CheckCheck,Clock,Eye,FileText,RefreshCw,Send,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,Check,CheckCheck,Clock,Eye,FileText,RefreshCw,Send,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,Check,CheckCheck,Clock,Eye,FileText,RefreshCw,Send,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,Check,CheckCheck,Clock,Eye,FileText,RefreshCw,Send,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,Check,CheckCheck,Clock,Eye,FileText,RefreshCw,Send,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-check.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,Check,CheckCheck,Clock,Eye,FileText,RefreshCw,Send,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,Check,CheckCheck,Clock,Eye,FileText,RefreshCw,Send,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,Check,CheckCheck,Clock,Eye,FileText,RefreshCw,Send,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,Check,CheckCheck,Clock,Eye,FileText,RefreshCw,Send,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction GovernmentNotificationsPage() {\n    _s();\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadNotifications();\n    }, []);\n    const loadNotifications = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_8__[\"default\"].get(\"/government/notifications\");\n            if (response.data.success) {\n                setNotifications(response.data.data.notifications);\n            } else {\n                // Fallback to sample data\n                setNotifications([\n                    {\n                        _id: \"1\",\n                        type: \"new_application\",\n                        title: \"طلب جديد على مناقصة\",\n                        message: \"تم تقديم طلب جديد على مناقصة بناء المستشفى من شركة البناء المتقدمة\",\n                        read: false,\n                        createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),\n                        priority: \"high\",\n                        category: \"tender\",\n                        data: {\n                            tenderId: \"tender123\",\n                            applicantName: \"شركة البناء المتقدمة\"\n                        }\n                    },\n                    {\n                        _id: \"2\",\n                        type: \"tender_deadline_approaching\",\n                        title: \"اقتراب موعد انتهاء مناقصة\",\n                        message: \"مناقصة تطوير نظام إدارة المدارس تنتهي خلال 24 ساعة\",\n                        read: false,\n                        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n                        priority: \"medium\",\n                        category: \"tender\",\n                        data: {\n                            tenderId: \"tender456\"\n                        }\n                    },\n                    {\n                        _id: \"3\",\n                        type: \"application_review_required\",\n                        title: \"مراجعة طلب مطلوبة\",\n                        message: \"يتطلب طلب شركة التقنية الحديثة مراجعة عاجلة لمناقصة الأنظمة الذكية\",\n                        read: true,\n                        createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),\n                        priority: \"high\",\n                        category: \"application\",\n                        data: {\n                            applicationId: \"app789\",\n                            tenderId: \"tender789\"\n                        }\n                    },\n                    {\n                        _id: \"4\",\n                        type: \"tender_published\",\n                        title: \"تم نشر مناقصة جديدة\",\n                        message: \"تم نشر مناقصة تطوير البنية التحتية للنقل بنجاح\",\n                        read: true,\n                        createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),\n                        priority: \"low\",\n                        category: \"tender\",\n                        data: {\n                            tenderId: \"tender101\"\n                        }\n                    },\n                    {\n                        _id: \"5\",\n                        type: \"system_update\",\n                        title: \"تحديث النظام\",\n                        message: \"تم تحديث نظام إدارة المناقصات بميزات جديدة لتحسين الأداء\",\n                        read: true,\n                        createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),\n                        priority: \"low\",\n                        category: \"system\",\n                        data: {}\n                    }\n                ]);\n            }\n        } catch (error) {\n            console.error(\"Error loading notifications:\", error);\n            toast({\n                title: \"خطأ في التحميل\",\n                description: \"حدث خطأ في تحميل الإشعارات\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const markAsRead = async (notificationId)=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_8__[\"default\"].patch(\"/government/notifications/\".concat(notificationId, \"/read\"));\n            setNotifications(notifications.map((notif)=>notif._id === notificationId ? {\n                    ...notif,\n                    read: true\n                } : notif));\n        } catch (error) {\n            toast({\n                title: \"خطأ\",\n                description: \"حدث خطأ في تحديث الإشعار\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const markAllAsRead = async ()=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_8__[\"default\"].patch(\"/government/notifications/mark-all-read\");\n            setNotifications(notifications.map((notif)=>({\n                    ...notif,\n                    read: true\n                })));\n            toast({\n                title: \"تم التحديث\",\n                description: \"تم تحديد جميع الإشعارات كمقروءة\"\n            });\n        } catch (error) {\n            toast({\n                title: \"خطأ\",\n                description: \"حدث خطأ في تحديث الإشعارات\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const deleteNotification = async (notificationId)=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_8__[\"default\"].delete(\"/government/notifications/\".concat(notificationId));\n            setNotifications(notifications.filter((notif)=>notif._id !== notificationId));\n            toast({\n                title: \"تم الحذف\",\n                description: \"تم حذف الإشعار بنجاح\"\n            });\n        } catch (error) {\n            toast({\n                title: \"خطأ\",\n                description: \"حدث خطأ في حذف الإشعار\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleNotificationClick = (notification)=>{\n        if (!notification.read) {\n            markAsRead(notification._id);\n        }\n        // Navigate based on notification type\n        if (notification.data) {\n            switch(notification.type){\n                case \"new_application\":\n                case \"application_review_required\":\n                    if (notification.data.applicationId) {\n                        router.push(\"/government/applications/\".concat(notification.data.applicationId));\n                    } else {\n                        router.push(\"/government/applications\");\n                    }\n                    break;\n                case \"tender_deadline_approaching\":\n                case \"tender_published\":\n                    router.push(\"/tenders/\".concat(notification.data.tenderId));\n                    break;\n                default:\n                    break;\n            }\n        }\n    };\n    const formatRelativeTime = (dateString)=>{\n        const now = new Date();\n        const date = new Date(dateString);\n        const diff = now.getTime() - date.getTime();\n        const minutes = Math.floor(diff / (1000 * 60));\n        const hours = Math.floor(diff / (1000 * 60 * 60));\n        const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n        if (days > 0) return \"منذ \".concat(days, \" يوم\");\n        if (hours > 0) return \"منذ \".concat(hours, \" ساعة\");\n        if (minutes > 0) return \"منذ \".concat(minutes, \" دقيقة\");\n        return \"الآن\";\n    };\n    const getNotificationIcon = (type)=>{\n        switch(type){\n            case \"new_application\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-5 w-5 text-blue-600\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 16\n                }, this);\n            case \"tender_deadline_approaching\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-5 w-5 text-orange-600\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 16\n                }, this);\n            case \"application_review_required\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-5 w-5 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 16\n                }, this);\n            case \"tender_published\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"h-5 w-5 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 16\n                }, this);\n            case \"system_update\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-5 w-5 text-purple-600\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                    lineNumber: 235,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    className: \"h-5 w-5 text-gray-600\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                    lineNumber: 237,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getPriorityBadge = (priority)=>{\n        switch(priority){\n            case \"high\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    variant: \"destructive\",\n                    children: \"عالية\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 16\n                }, this);\n            case \"medium\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: \"bg-yellow-100 text-yellow-800\",\n                    children: \"متوسطة\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 16\n                }, this);\n            case \"low\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    variant: \"secondary\",\n                    children: \"منخفضة\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                    lineNumber: 248,\n                    columnNumber: 16\n                }, this);\n            default:\n                return null;\n        }\n    };\n    const filteredNotifications = notifications.filter((notif)=>{\n        if (activeTab === \"all\") return true;\n        if (activeTab === \"unread\") return !notif.read;\n        if (activeTab === \"read\") return notif.read;\n        return notif.category === activeTab;\n    });\n    const unreadCount = notifications.filter((notif)=>!notif.read).length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DashboardLayout, {\n        allowedRoles: [\n            \"government\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold\",\n                                        children: \"إشعارات الجهة الحكومية\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-green-100 mt-1\",\n                                        children: \"متابعة جميع التحديثات المتعلقة بمناقصاتك وطلبات المشاركة\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: unreadCount\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-green-100\",\n                                                children: \"غير مقروءة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: loadNotifications,\n                                                variant: \"outline\",\n                                                className: \"bg-white/10 border-white/20 text-white hover:bg-white/20\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-4 w-4 ml-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"تحديث\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 17\n                                            }, this),\n                                            unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: markAllAsRead,\n                                                className: \"bg-white text-green-600 hover:bg-gray-100\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-4 w-4 ml-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"تحديد الكل كمقروء\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                    lineNumber: 267,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.Tabs, {\n                    value: activeTab,\n                    onValueChange: setActiveTab,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsList, {\n                            className: \"grid w-full grid-cols-5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                    value: \"all\",\n                                    children: [\n                                        \"الكل (\",\n                                        notifications.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                    value: \"unread\",\n                                    children: [\n                                        \"غير مقروءة (\",\n                                        unreadCount,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                    value: \"tender\",\n                                    children: \"مناقصات\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                    value: \"application\",\n                                    children: \"طلبات\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                    value: \"system\",\n                                    children: \"النظام\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                            value: activeTab,\n                            className: \"mt-6\",\n                            children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center py-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-8 w-8 animate-spin text-green-600 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"جاري تحميل الإشعارات...\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 15\n                            }, this) : filteredNotifications.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: filteredNotifications.map((notification)=>{\n                                    var _notification_data;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        className: \"transition-all hover:shadow-md cursor-pointer \".concat(!notification.read ? \"border-l-4 border-l-green-500 bg-green-50/50\" : \"\"),\n                                        onClick: ()=>handleNotificationClick(notification),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            className: \"p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start gap-3 flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-1\",\n                                                                children: getNotificationIcon(notification.type)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                                lineNumber: 334,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2 mb-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-medium \".concat(!notification.read ? \"font-semibold\" : \"\"),\n                                                                                children: notification.title\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                                                lineNumber: 339,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            !notification.read && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"h-2 w-2 bg-green-600 rounded-full\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                                                lineNumber: 343,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            getPriorityBadge(notification.priority)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                                        lineNumber: 338,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-600 text-sm mb-2\",\n                                                                        children: notification.message\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                                        lineNumber: 347,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-4 text-xs text-gray-500\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center gap-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                        className: \"h-3 w-3\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                                                        lineNumber: 352,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    formatRelativeTime(notification.createdAt)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                                                lineNumber: 351,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            ((_notification_data = notification.data) === null || _notification_data === void 0 ? void 0 : _notification_data.applicantName) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center gap-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                        className: \"h-3 w-3\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                                                        lineNumber: 357,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    notification.data.applicantName\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                                                lineNumber: 356,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                                        lineNumber: 350,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                                lineNumber: 337,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            !notification.read && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"ghost\",\n                                                                size: \"sm\",\n                                                                onClick: (e)=>{\n                                                                    e.stopPropagation();\n                                                                    markAsRead(notification._id);\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                                    lineNumber: 374,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                                lineNumber: 366,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"ghost\",\n                                                                size: \"sm\",\n                                                                onClick: (e)=>{\n                                                                    e.stopPropagation();\n                                                                    deleteNotification(notification._id);\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                                    lineNumber: 385,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                                lineNumber: 377,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, notification._id, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_Check_CheckCheck_Clock_Eye_FileText_RefreshCw_Send_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"لا توجد إشعارات\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: activeTab === \"unread\" ? \"جميع الإشعارات مقروءة\" : \"ستظهر هنا الإشعارات المتعلقة بأنشطة جهتكم الحكومية\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n                    lineNumber: 304,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n            lineNumber: 265,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/government/notifications/page.tsx\",\n        lineNumber: 264,\n        columnNumber: 5\n    }, this);\n}\n_s(GovernmentNotificationsPage, \"qD0cBtdJ8L5k16hunZW7qfcyn2Y=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast,\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter\n    ];\n});\n_c = GovernmentNotificationsPage;\nvar _c;\n$RefreshReg$(_c, \"GovernmentNotificationsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nb3Zlcm5tZW50L25vdGlmaWNhdGlvbnMvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUVrRDtBQUM4QztBQUNqRDtBQUNGO0FBQ2tDO0FBQzNCO0FBRVQ7QUFDaEI7QUFtQk47QUFjTixTQUFTMEI7O0lBQ3RCLE1BQU0sQ0FBQ0MsZUFBZUMsaUJBQWlCLEdBQUczQiwrQ0FBUUEsQ0FBaUIsRUFBRTtJQUNyRSxNQUFNLENBQUM0QixTQUFTQyxXQUFXLEdBQUc3QiwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUM4QixXQUFXQyxhQUFhLEdBQUcvQiwrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLEVBQUVnQyxLQUFLLEVBQUUsR0FBR3RCLGtFQUFRQTtJQUMxQixNQUFNdUIsU0FBU3RCLDBEQUFTQTtJQUV4QlYsZ0RBQVNBLENBQUM7UUFDUmlDO0lBQ0YsR0FBRyxFQUFFO0lBRUwsTUFBTUEsb0JBQW9CO1FBQ3hCLElBQUk7WUFDRkwsV0FBVztZQUNYLE1BQU1NLFdBQVcsTUFBTXZCLGdEQUFHQSxDQUFDd0IsR0FBRyxDQUFDO1lBRS9CLElBQUlELFNBQVNFLElBQUksQ0FBQ0MsT0FBTyxFQUFFO2dCQUN6QlgsaUJBQWlCUSxTQUFTRSxJQUFJLENBQUNBLElBQUksQ0FBQ1gsYUFBYTtZQUNuRCxPQUFPO2dCQUNMLDBCQUEwQjtnQkFDMUJDLGlCQUFpQjtvQkFDZjt3QkFDRVksS0FBSzt3QkFDTEMsTUFBTTt3QkFDTkMsT0FBTzt3QkFDUEMsU0FBUzt3QkFDVEMsTUFBTTt3QkFDTkMsV0FBVyxJQUFJQyxLQUFLQSxLQUFLQyxHQUFHLEtBQUssS0FBSyxLQUFLLE1BQU1DLFdBQVc7d0JBQzVEQyxVQUFVO3dCQUNWQyxVQUFVO3dCQUNWWixNQUFNOzRCQUFFYSxVQUFVOzRCQUFhQyxlQUFlO3dCQUF1QjtvQkFDdkU7b0JBQ0E7d0JBQ0VaLEtBQUs7d0JBQ0xDLE1BQU07d0JBQ05DLE9BQU87d0JBQ1BDLFNBQVM7d0JBQ1RDLE1BQU07d0JBQ05DLFdBQVcsSUFBSUMsS0FBS0EsS0FBS0MsR0FBRyxLQUFLLElBQUksS0FBSyxLQUFLLE1BQU1DLFdBQVc7d0JBQ2hFQyxVQUFVO3dCQUNWQyxVQUFVO3dCQUNWWixNQUFNOzRCQUFFYSxVQUFVO3dCQUFZO29CQUNoQztvQkFDQTt3QkFDRVgsS0FBSzt3QkFDTEMsTUFBTTt3QkFDTkMsT0FBTzt3QkFDUEMsU0FBUzt3QkFDVEMsTUFBTTt3QkFDTkMsV0FBVyxJQUFJQyxLQUFLQSxLQUFLQyxHQUFHLEtBQUssSUFBSSxLQUFLLEtBQUssTUFBTUMsV0FBVzt3QkFDaEVDLFVBQVU7d0JBQ1ZDLFVBQVU7d0JBQ1ZaLE1BQU07NEJBQUVlLGVBQWU7NEJBQVVGLFVBQVU7d0JBQVk7b0JBQ3pEO29CQUNBO3dCQUNFWCxLQUFLO3dCQUNMQyxNQUFNO3dCQUNOQyxPQUFPO3dCQUNQQyxTQUFTO3dCQUNUQyxNQUFNO3dCQUNOQyxXQUFXLElBQUlDLEtBQUtBLEtBQUtDLEdBQUcsS0FBSyxLQUFLLEtBQUssS0FBSyxNQUFNQyxXQUFXO3dCQUNqRUMsVUFBVTt3QkFDVkMsVUFBVTt3QkFDVlosTUFBTTs0QkFBRWEsVUFBVTt3QkFBWTtvQkFDaEM7b0JBQ0E7d0JBQ0VYLEtBQUs7d0JBQ0xDLE1BQU07d0JBQ05DLE9BQU87d0JBQ1BDLFNBQVM7d0JBQ1RDLE1BQU07d0JBQ05DLFdBQVcsSUFBSUMsS0FBS0EsS0FBS0MsR0FBRyxLQUFLLElBQUksS0FBSyxLQUFLLEtBQUssTUFBTUMsV0FBVzt3QkFDckVDLFVBQVU7d0JBQ1ZDLFVBQVU7d0JBQ1ZaLE1BQU0sQ0FBQztvQkFDVDtpQkFDRDtZQUNIO1FBQ0YsRUFBRSxPQUFPZ0IsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsZ0NBQWdDQTtZQUM5Q3JCLE1BQU07Z0JBQ0pTLE9BQU87Z0JBQ1BjLGFBQWE7Z0JBQ2JDLFNBQVM7WUFDWDtRQUNGLFNBQVU7WUFDUjNCLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTTRCLGFBQWEsT0FBT0M7UUFDeEIsSUFBSTtZQUNGLE1BQU05QyxnREFBR0EsQ0FBQytDLEtBQUssQ0FBQyw2QkFBNEMsT0FBZkQsZ0JBQWU7WUFDNUQvQixpQkFBaUJELGNBQWNrQyxHQUFHLENBQUNDLENBQUFBLFFBQ2pDQSxNQUFNdEIsR0FBRyxLQUFLbUIsaUJBQWlCO29CQUFFLEdBQUdHLEtBQUs7b0JBQUVsQixNQUFNO2dCQUFLLElBQUlrQjtRQUU5RCxFQUFFLE9BQU9SLE9BQU87WUFDZHJCLE1BQU07Z0JBQ0pTLE9BQU87Z0JBQ1BjLGFBQWE7Z0JBQ2JDLFNBQVM7WUFDWDtRQUNGO0lBQ0Y7SUFFQSxNQUFNTSxnQkFBZ0I7UUFDcEIsSUFBSTtZQUNGLE1BQU1sRCxnREFBR0EsQ0FBQytDLEtBQUssQ0FBQztZQUNoQmhDLGlCQUFpQkQsY0FBY2tDLEdBQUcsQ0FBQ0MsQ0FBQUEsUUFBVTtvQkFBRSxHQUFHQSxLQUFLO29CQUFFbEIsTUFBTTtnQkFBSztZQUNwRVgsTUFBTTtnQkFDSlMsT0FBTztnQkFDUGMsYUFBYTtZQUNmO1FBQ0YsRUFBRSxPQUFPRixPQUFPO1lBQ2RyQixNQUFNO2dCQUNKUyxPQUFPO2dCQUNQYyxhQUFhO2dCQUNiQyxTQUFTO1lBQ1g7UUFDRjtJQUNGO0lBRUEsTUFBTU8scUJBQXFCLE9BQU9MO1FBQ2hDLElBQUk7WUFDRixNQUFNOUMsZ0RBQUdBLENBQUNvRCxNQUFNLENBQUMsNkJBQTRDLE9BQWZOO1lBQzlDL0IsaUJBQWlCRCxjQUFjdUMsTUFBTSxDQUFDSixDQUFBQSxRQUFTQSxNQUFNdEIsR0FBRyxLQUFLbUI7WUFDN0QxQixNQUFNO2dCQUNKUyxPQUFPO2dCQUNQYyxhQUFhO1lBQ2Y7UUFDRixFQUFFLE9BQU9GLE9BQU87WUFDZHJCLE1BQU07Z0JBQ0pTLE9BQU87Z0JBQ1BjLGFBQWE7Z0JBQ2JDLFNBQVM7WUFDWDtRQUNGO0lBQ0Y7SUFFQSxNQUFNVSwwQkFBMEIsQ0FBQ0M7UUFDL0IsSUFBSSxDQUFDQSxhQUFheEIsSUFBSSxFQUFFO1lBQ3RCYyxXQUFXVSxhQUFhNUIsR0FBRztRQUM3QjtRQUVBLHNDQUFzQztRQUN0QyxJQUFJNEIsYUFBYTlCLElBQUksRUFBRTtZQUNyQixPQUFROEIsYUFBYTNCLElBQUk7Z0JBQ3ZCLEtBQUs7Z0JBQ0wsS0FBSztvQkFDSCxJQUFJMkIsYUFBYTlCLElBQUksQ0FBQ2UsYUFBYSxFQUFFO3dCQUNuQ25CLE9BQU9tQyxJQUFJLENBQUMsNEJBQTRELE9BQWhDRCxhQUFhOUIsSUFBSSxDQUFDZSxhQUFhO29CQUN6RSxPQUFPO3dCQUNMbkIsT0FBT21DLElBQUksQ0FBQztvQkFDZDtvQkFDQTtnQkFDRixLQUFLO2dCQUNMLEtBQUs7b0JBQ0huQyxPQUFPbUMsSUFBSSxDQUFDLFlBQXVDLE9BQTNCRCxhQUFhOUIsSUFBSSxDQUFDYSxRQUFRO29CQUNsRDtnQkFDRjtvQkFDRTtZQUNKO1FBQ0Y7SUFDRjtJQUVBLE1BQU1tQixxQkFBcUIsQ0FBQ0M7UUFDMUIsTUFBTXhCLE1BQU0sSUFBSUQ7UUFDaEIsTUFBTTBCLE9BQU8sSUFBSTFCLEtBQUt5QjtRQUN0QixNQUFNRSxPQUFPMUIsSUFBSTJCLE9BQU8sS0FBS0YsS0FBS0UsT0FBTztRQUV6QyxNQUFNQyxVQUFVQyxLQUFLQyxLQUFLLENBQUNKLE9BQVEsUUFBTyxFQUFDO1FBQzNDLE1BQU1LLFFBQVFGLEtBQUtDLEtBQUssQ0FBQ0osT0FBUSxRQUFPLEtBQUssRUFBQztRQUM5QyxNQUFNTSxPQUFPSCxLQUFLQyxLQUFLLENBQUNKLE9BQVEsUUFBTyxLQUFLLEtBQUssRUFBQztRQUVsRCxJQUFJTSxPQUFPLEdBQUcsT0FBTyxPQUFZLE9BQUxBLE1BQUs7UUFDakMsSUFBSUQsUUFBUSxHQUFHLE9BQU8sT0FBYSxPQUFOQSxPQUFNO1FBQ25DLElBQUlILFVBQVUsR0FBRyxPQUFPLE9BQWUsT0FBUkEsU0FBUTtRQUN2QyxPQUFPO0lBQ1Q7SUFFQSxNQUFNSyxzQkFBc0IsQ0FBQ3ZDO1FBQzNCLE9BQVFBO1lBQ04sS0FBSztnQkFDSCxxQkFBTyw4REFBQ2pCLGtLQUFJQTtvQkFBQ3lELFdBQVU7Ozs7OztZQUN6QixLQUFLO2dCQUNILHFCQUFPLDhEQUFDN0QsbUtBQUtBO29CQUFDNkQsV0FBVTs7Ozs7O1lBQzFCLEtBQUs7Z0JBQ0gscUJBQU8sOERBQUN4RCxtS0FBR0E7b0JBQUN3RCxXQUFVOzs7Ozs7WUFDeEIsS0FBSztnQkFDSCxxQkFBTyw4REFBQzVELG1LQUFRQTtvQkFBQzRELFdBQVU7Ozs7OztZQUM3QixLQUFLO2dCQUNILHFCQUFPLDhEQUFDMUQsbUtBQVFBO29CQUFDMEQsV0FBVTs7Ozs7O1lBQzdCO2dCQUNFLHFCQUFPLDhEQUFDbkUsbUtBQUlBO29CQUFDbUUsV0FBVTs7Ozs7O1FBQzNCO0lBQ0Y7SUFFQSxNQUFNQyxtQkFBbUIsQ0FBQ2pDO1FBQ3hCLE9BQVFBO1lBQ04sS0FBSztnQkFDSCxxQkFBTyw4REFBQzNDLHVEQUFLQTtvQkFBQ21ELFNBQVE7OEJBQWM7Ozs7OztZQUN0QyxLQUFLO2dCQUNILHFCQUFPLDhEQUFDbkQsdURBQUtBO29CQUFDMkUsV0FBVTs4QkFBZ0M7Ozs7OztZQUMxRCxLQUFLO2dCQUNILHFCQUFPLDhEQUFDM0UsdURBQUtBO29CQUFDbUQsU0FBUTs4QkFBWTs7Ozs7O1lBQ3BDO2dCQUNFLE9BQU87UUFDWDtJQUNGO0lBRUEsTUFBTTBCLHdCQUF3QnhELGNBQWN1QyxNQUFNLENBQUNKLENBQUFBO1FBQ2pELElBQUkvQixjQUFjLE9BQU8sT0FBTztRQUNoQyxJQUFJQSxjQUFjLFVBQVUsT0FBTyxDQUFDK0IsTUFBTWxCLElBQUk7UUFDOUMsSUFBSWIsY0FBYyxRQUFRLE9BQU8rQixNQUFNbEIsSUFBSTtRQUMzQyxPQUFPa0IsTUFBTVosUUFBUSxLQUFLbkI7SUFDNUI7SUFFQSxNQUFNcUQsY0FBY3pELGNBQWN1QyxNQUFNLENBQUNKLENBQUFBLFFBQVMsQ0FBQ0EsTUFBTWxCLElBQUksRUFBRXlDLE1BQU07SUFFckUscUJBQ0UsOERBQUNDO1FBQWdCQyxjQUFjO1lBQUM7U0FBYTtrQkFDM0MsNEVBQUNDO1lBQUlQLFdBQVU7OzhCQUViLDhEQUFDTztvQkFBSVAsV0FBVTs4QkFDYiw0RUFBQ087d0JBQUlQLFdBQVU7OzBDQUNiLDhEQUFDTzs7a0RBQ0MsOERBQUNDO3dDQUFHUixXQUFVO2tEQUFxQjs7Ozs7O2tEQUNuQyw4REFBQ1M7d0NBQUVULFdBQVU7a0RBQXNCOzs7Ozs7Ozs7Ozs7MENBSXJDLDhEQUFDTztnQ0FBSVAsV0FBVTs7a0RBQ2IsOERBQUNPO3dDQUFJUCxXQUFVOzswREFDYiw4REFBQ087Z0RBQUlQLFdBQVU7MERBQXNCRzs7Ozs7OzBEQUNyQyw4REFBQ0k7Z0RBQUlQLFdBQVU7MERBQXlCOzs7Ozs7Ozs7Ozs7a0RBRTFDLDhEQUFDTzt3Q0FBSVAsV0FBVTs7MERBQ2IsOERBQUM1RSx5REFBTUE7Z0RBQ0xzRixTQUFTeEQ7Z0RBQ1RzQixTQUFRO2dEQUNSd0IsV0FBVTs7a0VBRVYsOERBQUMvRCxtS0FBU0E7d0RBQUMrRCxXQUFVOzs7Ozs7b0RBQWlCOzs7Ozs7OzRDQUd2Q0csY0FBYyxtQkFDYiw4REFBQy9FLHlEQUFNQTtnREFDTHNGLFNBQVM1QjtnREFDVGtCLFdBQVU7O2tFQUVWLDhEQUFDakUsbUtBQVVBO3dEQUFDaUUsV0FBVTs7Ozs7O29EQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQVVuRCw4REFBQzFFLHFEQUFJQTtvQkFBQ3FGLE9BQU83RDtvQkFBVzhELGVBQWU3RDs7c0NBQ3JDLDhEQUFDdkIseURBQVFBOzRCQUFDd0UsV0FBVTs7OENBQ2xCLDhEQUFDdkUsNERBQVdBO29DQUFDa0YsT0FBTTs7d0NBQU07d0NBQU9qRSxjQUFjMEQsTUFBTTt3Q0FBQzs7Ozs7Ozs4Q0FDckQsOERBQUMzRSw0REFBV0E7b0NBQUNrRixPQUFNOzt3Q0FBUzt3Q0FBYVI7d0NBQVk7Ozs7Ozs7OENBQ3JELDhEQUFDMUUsNERBQVdBO29DQUFDa0YsT0FBTTs4Q0FBUzs7Ozs7OzhDQUM1Qiw4REFBQ2xGLDREQUFXQTtvQ0FBQ2tGLE9BQU07OENBQWM7Ozs7Ozs4Q0FDakMsOERBQUNsRiw0REFBV0E7b0NBQUNrRixPQUFNOzhDQUFTOzs7Ozs7Ozs7Ozs7c0NBRzlCLDhEQUFDcEYsNERBQVdBOzRCQUFDb0YsT0FBTzdEOzRCQUFXa0QsV0FBVTtzQ0FDdENwRCx3QkFDQyw4REFBQzJEO2dDQUFJUCxXQUFVOzBDQUNiLDRFQUFDTztvQ0FBSVAsV0FBVTs7c0RBQ2IsOERBQUMvRCxtS0FBU0E7NENBQUMrRCxXQUFVOzs7Ozs7c0RBQ3JCLDhEQUFDUzs0Q0FBRVQsV0FBVTtzREFBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7dUNBRy9CRSxzQkFBc0JFLE1BQU0sR0FBRyxrQkFDakMsOERBQUNHO2dDQUFJUCxXQUFVOzBDQUNaRSxzQkFBc0J0QixHQUFHLENBQUMsQ0FBQ087d0NBZ0NiQTt5REEvQmIsOERBQUNqRSxxREFBSUE7d0NBRUg4RSxXQUFXLGlEQUVWLE9BREMsQ0FBQ2IsYUFBYXhCLElBQUksR0FBRyxpREFBaUQ7d0NBRXhFK0MsU0FBUyxJQUFNeEIsd0JBQXdCQztrREFFdkMsNEVBQUNoRSw0REFBV0E7NENBQUM2RSxXQUFVO3NEQUNyQiw0RUFBQ087Z0RBQUlQLFdBQVU7O2tFQUNiLDhEQUFDTzt3REFBSVAsV0FBVTs7MEVBQ2IsOERBQUNPO2dFQUFJUCxXQUFVOzBFQUNaRCxvQkFBb0JaLGFBQWEzQixJQUFJOzs7Ozs7MEVBRXhDLDhEQUFDK0M7Z0VBQUlQLFdBQVU7O2tGQUNiLDhEQUFDTzt3RUFBSVAsV0FBVTs7MEZBQ2IsOERBQUNhO2dGQUFHYixXQUFXLGVBQXlELE9BQTFDLENBQUNiLGFBQWF4QixJQUFJLEdBQUcsa0JBQWtCOzBGQUNsRXdCLGFBQWExQixLQUFLOzs7Ozs7NEVBRXBCLENBQUMwQixhQUFheEIsSUFBSSxrQkFDakIsOERBQUM0QztnRkFBSVAsV0FBVTs7Ozs7OzRFQUVoQkMsaUJBQWlCZCxhQUFhbkIsUUFBUTs7Ozs7OztrRkFFekMsOERBQUN5Qzt3RUFBRVQsV0FBVTtrRkFDVmIsYUFBYXpCLE9BQU87Ozs7OztrRkFFdkIsOERBQUM2Qzt3RUFBSVAsV0FBVTs7MEZBQ2IsOERBQUNPO2dGQUFJUCxXQUFVOztrR0FDYiw4REFBQzlELG1LQUFRQTt3RkFBQzhELFdBQVU7Ozs7OztvRkFDbkJYLG1CQUFtQkYsYUFBYXZCLFNBQVM7Ozs7Ozs7NEVBRTNDdUIsRUFBQUEscUJBQUFBLGFBQWE5QixJQUFJLGNBQWpCOEIseUNBQUFBLG1CQUFtQmhCLGFBQWEsbUJBQy9CLDhEQUFDb0M7Z0ZBQUlQLFdBQVU7O2tHQUNiLDhEQUFDM0QsbUtBQUtBO3dGQUFDMkQsV0FBVTs7Ozs7O29GQUNoQmIsYUFBYTlCLElBQUksQ0FBQ2MsYUFBYTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrRUFNMUMsOERBQUNvQzt3REFBSVAsV0FBVTs7NERBQ1osQ0FBQ2IsYUFBYXhCLElBQUksa0JBQ2pCLDhEQUFDdkMseURBQU1BO2dFQUNMb0QsU0FBUTtnRUFDUnNDLE1BQUs7Z0VBQ0xKLFNBQVMsQ0FBQ0s7b0VBQ1JBLEVBQUVDLGVBQWU7b0VBQ2pCdkMsV0FBV1UsYUFBYTVCLEdBQUc7Z0VBQzdCOzBFQUVBLDRFQUFDekIsbUtBQUtBO29FQUFDa0UsV0FBVTs7Ozs7Ozs7Ozs7MEVBR3JCLDhEQUFDNUUseURBQU1BO2dFQUNMb0QsU0FBUTtnRUFDUnNDLE1BQUs7Z0VBQ0xKLFNBQVMsQ0FBQ0s7b0VBQ1JBLEVBQUVDLGVBQWU7b0VBQ2pCakMsbUJBQW1CSSxhQUFhNUIsR0FBRztnRUFDckM7MEVBRUEsNEVBQUN2QixtS0FBTUE7b0VBQUNnRSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3VDQTVEckJiLGFBQWE1QixHQUFHOzs7Ozs7Ozs7O3FEQXFFM0IsOERBQUNnRDtnQ0FBSVAsV0FBVTs7a0RBQ2IsOERBQUNuRSxtS0FBSUE7d0NBQUNtRSxXQUFVOzs7Ozs7a0RBQ2hCLDhEQUFDYTt3Q0FBR2IsV0FBVTtrREFBeUM7Ozs7OztrREFDdkQsOERBQUNTO3dDQUFFVCxXQUFVO2tEQUNWbEQsY0FBYyxXQUNYLDBCQUNBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBVXRCO0dBOVd3Qkw7O1FBSUpmLDhEQUFRQTtRQUNYQyxzREFBU0E7OztLQUxGYyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9hcHAvZ292ZXJubWVudC9ub3RpZmljYXRpb25zL3BhZ2UudHN4P2U4OGMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBDYXJkLCBDYXJkQ29udGVudCwgQ2FyZEhlYWRlciwgQ2FyZFRpdGxlLCBDYXJkRGVzY3JpcHRpb24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvY2FyZCdcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9idXR0b24nXG5pbXBvcnQgeyBCYWRnZSB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9iYWRnZSdcbmltcG9ydCB7IFRhYnMsIFRhYnNDb250ZW50LCBUYWJzTGlzdCwgVGFic1RyaWdnZXIgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvdGFicydcbmltcG9ydCB7IHVzZVRvYXN0IH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL3VzZS10b2FzdCdcblxuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJ1xuaW1wb3J0IGFwaSBmcm9tICdAL2xpYi9hcGknXG5pbXBvcnQgeyBcbiAgQmVsbCwgXG4gIEJlbGxSaW5nLFxuICBDaGVjayxcbiAgQ2hlY2tDaGVjayxcbiAgVHJhc2gyLFxuICBSZWZyZXNoQ3csXG4gIENhbGVuZGFyLFxuICBDbG9jayxcbiAgQWxlcnRDaXJjbGUsXG4gIEluZm8sXG4gIENoZWNrQ2lyY2xlLFxuICBYQ2lyY2xlLFxuICBGaWxlVGV4dCxcbiAgVXNlcnMsXG4gIEJ1aWxkaW5nLFxuICBTZW5kLFxuICBFeWVcbn0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuXG5pbnRlcmZhY2UgTm90aWZpY2F0aW9uIHtcbiAgX2lkOiBzdHJpbmdcbiAgdHlwZTogc3RyaW5nXG4gIHRpdGxlOiBzdHJpbmdcbiAgbWVzc2FnZTogc3RyaW5nXG4gIGRhdGE/OiBhbnlcbiAgcmVhZDogYm9vbGVhblxuICBjcmVhdGVkQXQ6IHN0cmluZ1xuICBwcmlvcml0eTogJ2xvdycgfCAnbWVkaXVtJyB8ICdoaWdoJ1xuICBjYXRlZ29yeTogc3RyaW5nXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEdvdmVybm1lbnROb3RpZmljYXRpb25zUGFnZSgpIHtcbiAgY29uc3QgW25vdGlmaWNhdGlvbnMsIHNldE5vdGlmaWNhdGlvbnNdID0gdXNlU3RhdGU8Tm90aWZpY2F0aW9uW10+KFtdKVxuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKVxuICBjb25zdCBbYWN0aXZlVGFiLCBzZXRBY3RpdmVUYWJdID0gdXNlU3RhdGUoJ2FsbCcpXG4gIGNvbnN0IHsgdG9hc3QgfSA9IHVzZVRvYXN0KClcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKClcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGxvYWROb3RpZmljYXRpb25zKClcbiAgfSwgW10pXG5cbiAgY29uc3QgbG9hZE5vdGlmaWNhdGlvbnMgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldExvYWRpbmcodHJ1ZSlcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpLmdldCgnL2dvdmVybm1lbnQvbm90aWZpY2F0aW9ucycpXG4gICAgICBcbiAgICAgIGlmIChyZXNwb25zZS5kYXRhLnN1Y2Nlc3MpIHtcbiAgICAgICAgc2V0Tm90aWZpY2F0aW9ucyhyZXNwb25zZS5kYXRhLmRhdGEubm90aWZpY2F0aW9ucylcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIC8vIEZhbGxiYWNrIHRvIHNhbXBsZSBkYXRhXG4gICAgICAgIHNldE5vdGlmaWNhdGlvbnMoW1xuICAgICAgICAgIHtcbiAgICAgICAgICAgIF9pZDogJzEnLFxuICAgICAgICAgICAgdHlwZTogJ25ld19hcHBsaWNhdGlvbicsXG4gICAgICAgICAgICB0aXRsZTogJ9i32YTYqCDYrNiv2YrYryDYudmE2Ykg2YXZhtin2YLYtdipJyxcbiAgICAgICAgICAgIG1lc3NhZ2U6ICfYqtmFINiq2YLYr9mK2YUg2LfZhNioINis2K/ZitivINi52YTZiSDZhdmG2KfZgti12Kkg2KjZhtin2KEg2KfZhNmF2LPYqti02YHZiSDZhdmGINi02LHZg9ipINin2YTYqNmG2KfYoSDYp9mE2YXYqtmC2K/ZhdipJyxcbiAgICAgICAgICAgIHJlYWQ6IGZhbHNlLFxuICAgICAgICAgICAgY3JlYXRlZEF0OiBuZXcgRGF0ZShEYXRlLm5vdygpIC0gMzAgKiA2MCAqIDEwMDApLnRvSVNPU3RyaW5nKCksXG4gICAgICAgICAgICBwcmlvcml0eTogJ2hpZ2gnLFxuICAgICAgICAgICAgY2F0ZWdvcnk6ICd0ZW5kZXInLFxuICAgICAgICAgICAgZGF0YTogeyB0ZW5kZXJJZDogJ3RlbmRlcjEyMycsIGFwcGxpY2FudE5hbWU6ICfYtNix2YPYqSDYp9mE2KjZhtin2KEg2KfZhNmF2KrZgtiv2YXYqScgfVxuICAgICAgICAgIH0sXG4gICAgICAgICAge1xuICAgICAgICAgICAgX2lkOiAnMicsXG4gICAgICAgICAgICB0eXBlOiAndGVuZGVyX2RlYWRsaW5lX2FwcHJvYWNoaW5nJyxcbiAgICAgICAgICAgIHRpdGxlOiAn2KfZgtiq2LHYp9ioINmF2YjYudivINin2YbYqtmH2KfYoSDZhdmG2KfZgti12KknLFxuICAgICAgICAgICAgbWVzc2FnZTogJ9mF2YbYp9mC2LXYqSDYqti32YjZitixINmG2LjYp9mFINil2K/Yp9ix2Kkg2KfZhNmF2K/Yp9ix2LMg2KrZhtiq2YfZiiDYrtmE2KfZhCAyNCDYs9in2LnYqScsXG4gICAgICAgICAgICByZWFkOiBmYWxzZSxcbiAgICAgICAgICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoRGF0ZS5ub3coKSAtIDIgKiA2MCAqIDYwICogMTAwMCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgICAgIHByaW9yaXR5OiAnbWVkaXVtJyxcbiAgICAgICAgICAgIGNhdGVnb3J5OiAndGVuZGVyJyxcbiAgICAgICAgICAgIGRhdGE6IHsgdGVuZGVySWQ6ICd0ZW5kZXI0NTYnIH1cbiAgICAgICAgICB9LFxuICAgICAgICAgIHtcbiAgICAgICAgICAgIF9pZDogJzMnLFxuICAgICAgICAgICAgdHlwZTogJ2FwcGxpY2F0aW9uX3Jldmlld19yZXF1aXJlZCcsXG4gICAgICAgICAgICB0aXRsZTogJ9mF2LHYp9is2LnYqSDYt9mE2Kgg2YXYt9mE2YjYqNipJyxcbiAgICAgICAgICAgIG1lc3NhZ2U6ICfZitiq2LfZhNioINi32YTYqCDYtNix2YPYqSDYp9mE2KrZgtmG2YrYqSDYp9mE2K3Yr9mK2KvYqSDZhdix2KfYrNi52Kkg2LnYp9is2YTYqSDZhNmF2YbYp9mC2LXYqSDYp9mE2KPZhti42YXYqSDYp9mE2LDZg9mK2KknLFxuICAgICAgICAgICAgcmVhZDogdHJ1ZSxcbiAgICAgICAgICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoRGF0ZS5ub3coKSAtIDYgKiA2MCAqIDYwICogMTAwMCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgICAgIHByaW9yaXR5OiAnaGlnaCcsXG4gICAgICAgICAgICBjYXRlZ29yeTogJ2FwcGxpY2F0aW9uJyxcbiAgICAgICAgICAgIGRhdGE6IHsgYXBwbGljYXRpb25JZDogJ2FwcDc4OScsIHRlbmRlcklkOiAndGVuZGVyNzg5JyB9XG4gICAgICAgICAgfSxcbiAgICAgICAgICB7XG4gICAgICAgICAgICBfaWQ6ICc0JyxcbiAgICAgICAgICAgIHR5cGU6ICd0ZW5kZXJfcHVibGlzaGVkJyxcbiAgICAgICAgICAgIHRpdGxlOiAn2KrZhSDZhti02LEg2YXZhtin2YLYtdipINis2K/Zitiv2KknLFxuICAgICAgICAgICAgbWVzc2FnZTogJ9iq2YUg2YbYtNixINmF2YbYp9mC2LXYqSDYqti32YjZitixINin2YTYqNmG2YrYqSDYp9mE2KrYrdiq2YrYqSDZhNmE2YbZgtmEINio2YbYrNin2K0nLFxuICAgICAgICAgICAgcmVhZDogdHJ1ZSxcbiAgICAgICAgICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoRGF0ZS5ub3coKSAtIDI0ICogNjAgKiA2MCAqIDEwMDApLnRvSVNPU3RyaW5nKCksXG4gICAgICAgICAgICBwcmlvcml0eTogJ2xvdycsXG4gICAgICAgICAgICBjYXRlZ29yeTogJ3RlbmRlcicsXG4gICAgICAgICAgICBkYXRhOiB7IHRlbmRlcklkOiAndGVuZGVyMTAxJyB9XG4gICAgICAgICAgfSxcbiAgICAgICAgICB7XG4gICAgICAgICAgICBfaWQ6ICc1JyxcbiAgICAgICAgICAgIHR5cGU6ICdzeXN0ZW1fdXBkYXRlJyxcbiAgICAgICAgICAgIHRpdGxlOiAn2KrYrdiv2YrYqyDYp9mE2YbYuNin2YUnLFxuICAgICAgICAgICAgbWVzc2FnZTogJ9iq2YUg2KrYrdiv2YrYqyDZhti42KfZhSDYpdiv2KfYsdipINin2YTZhdmG2KfZgti12KfYqiDYqNmF2YrYstin2Kog2KzYr9mK2K/YqSDZhNiq2K3Ys9mK2YYg2KfZhNij2K/Yp9ihJyxcbiAgICAgICAgICAgIHJlYWQ6IHRydWUsXG4gICAgICAgICAgICBjcmVhdGVkQXQ6IG5ldyBEYXRlKERhdGUubm93KCkgLSAzICogMjQgKiA2MCAqIDYwICogMTAwMCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgICAgIHByaW9yaXR5OiAnbG93JyxcbiAgICAgICAgICAgIGNhdGVnb3J5OiAnc3lzdGVtJyxcbiAgICAgICAgICAgIGRhdGE6IHt9XG4gICAgICAgICAgfVxuICAgICAgICBdKVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBsb2FkaW5nIG5vdGlmaWNhdGlvbnM6JywgZXJyb3IpXG4gICAgICB0b2FzdCh7XG4gICAgICAgIHRpdGxlOiAn2K7Yt9ijINmB2Yog2KfZhNiq2K3ZhdmK2YQnLFxuICAgICAgICBkZXNjcmlwdGlvbjogJ9it2K/YqyDYrti32KMg2YHZiiDYqtit2YXZitmEINin2YTYpdi02LnYp9ix2KfYqicsXG4gICAgICAgIHZhcmlhbnQ6ICdkZXN0cnVjdGl2ZSdcbiAgICAgIH0pXG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgbWFya0FzUmVhZCA9IGFzeW5jIChub3RpZmljYXRpb25JZDogc3RyaW5nKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IGFwaS5wYXRjaChgL2dvdmVybm1lbnQvbm90aWZpY2F0aW9ucy8ke25vdGlmaWNhdGlvbklkfS9yZWFkYClcbiAgICAgIHNldE5vdGlmaWNhdGlvbnMobm90aWZpY2F0aW9ucy5tYXAobm90aWYgPT4gXG4gICAgICAgIG5vdGlmLl9pZCA9PT0gbm90aWZpY2F0aW9uSWQgPyB7IC4uLm5vdGlmLCByZWFkOiB0cnVlIH0gOiBub3RpZlxuICAgICAgKSlcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogJ9iu2LfYoycsXG4gICAgICAgIGRlc2NyaXB0aW9uOiAn2K3Yr9irINiu2LfYoyDZgdmKINiq2K3Yr9mK2Ksg2KfZhNil2LTYudin2LEnLFxuICAgICAgICB2YXJpYW50OiAnZGVzdHJ1Y3RpdmUnXG4gICAgICB9KVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IG1hcmtBbGxBc1JlYWQgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IGFwaS5wYXRjaCgnL2dvdmVybm1lbnQvbm90aWZpY2F0aW9ucy9tYXJrLWFsbC1yZWFkJylcbiAgICAgIHNldE5vdGlmaWNhdGlvbnMobm90aWZpY2F0aW9ucy5tYXAobm90aWYgPT4gKHsgLi4ubm90aWYsIHJlYWQ6IHRydWUgfSkpKVxuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogJ9iq2YUg2KfZhNiq2K3Yr9mK2KsnLFxuICAgICAgICBkZXNjcmlwdGlvbjogJ9iq2YUg2KrYrdiv2YrYryDYrNmF2YrYuSDYp9mE2KXYtNi52KfYsdin2Kog2YPZhdmC2LHZiNih2KknXG4gICAgICB9KVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICB0b2FzdCh7XG4gICAgICAgIHRpdGxlOiAn2K7Yt9ijJyxcbiAgICAgICAgZGVzY3JpcHRpb246ICfYrdiv2Ksg2K7Yt9ijINmB2Yog2KrYrdiv2YrYqyDYp9mE2KXYtNi52KfYsdin2KonLFxuICAgICAgICB2YXJpYW50OiAnZGVzdHJ1Y3RpdmUnXG4gICAgICB9KVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGRlbGV0ZU5vdGlmaWNhdGlvbiA9IGFzeW5jIChub3RpZmljYXRpb25JZDogc3RyaW5nKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IGFwaS5kZWxldGUoYC9nb3Zlcm5tZW50L25vdGlmaWNhdGlvbnMvJHtub3RpZmljYXRpb25JZH1gKVxuICAgICAgc2V0Tm90aWZpY2F0aW9ucyhub3RpZmljYXRpb25zLmZpbHRlcihub3RpZiA9PiBub3RpZi5faWQgIT09IG5vdGlmaWNhdGlvbklkKSlcbiAgICAgIHRvYXN0KHtcbiAgICAgICAgdGl0bGU6ICfYqtmFINin2YTYrdiw2YEnLFxuICAgICAgICBkZXNjcmlwdGlvbjogJ9iq2YUg2K3YsNmBINin2YTYpdi02LnYp9ixINio2YbYrNin2K0nXG4gICAgICB9KVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICB0b2FzdCh7XG4gICAgICAgIHRpdGxlOiAn2K7Yt9ijJyxcbiAgICAgICAgZGVzY3JpcHRpb246ICfYrdiv2Ksg2K7Yt9ijINmB2Yog2K3YsNmBINin2YTYpdi02LnYp9ixJyxcbiAgICAgICAgdmFyaWFudDogJ2Rlc3RydWN0aXZlJ1xuICAgICAgfSlcbiAgICB9XG4gIH1cblxuICBjb25zdCBoYW5kbGVOb3RpZmljYXRpb25DbGljayA9IChub3RpZmljYXRpb246IE5vdGlmaWNhdGlvbikgPT4ge1xuICAgIGlmICghbm90aWZpY2F0aW9uLnJlYWQpIHtcbiAgICAgIG1hcmtBc1JlYWQobm90aWZpY2F0aW9uLl9pZClcbiAgICB9XG5cbiAgICAvLyBOYXZpZ2F0ZSBiYXNlZCBvbiBub3RpZmljYXRpb24gdHlwZVxuICAgIGlmIChub3RpZmljYXRpb24uZGF0YSkge1xuICAgICAgc3dpdGNoIChub3RpZmljYXRpb24udHlwZSkge1xuICAgICAgICBjYXNlICduZXdfYXBwbGljYXRpb24nOlxuICAgICAgICBjYXNlICdhcHBsaWNhdGlvbl9yZXZpZXdfcmVxdWlyZWQnOlxuICAgICAgICAgIGlmIChub3RpZmljYXRpb24uZGF0YS5hcHBsaWNhdGlvbklkKSB7XG4gICAgICAgICAgICByb3V0ZXIucHVzaChgL2dvdmVybm1lbnQvYXBwbGljYXRpb25zLyR7bm90aWZpY2F0aW9uLmRhdGEuYXBwbGljYXRpb25JZH1gKVxuICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICByb3V0ZXIucHVzaCgnL2dvdmVybm1lbnQvYXBwbGljYXRpb25zJylcbiAgICAgICAgICB9XG4gICAgICAgICAgYnJlYWtcbiAgICAgICAgY2FzZSAndGVuZGVyX2RlYWRsaW5lX2FwcHJvYWNoaW5nJzpcbiAgICAgICAgY2FzZSAndGVuZGVyX3B1Ymxpc2hlZCc6XG4gICAgICAgICAgcm91dGVyLnB1c2goYC90ZW5kZXJzLyR7bm90aWZpY2F0aW9uLmRhdGEudGVuZGVySWR9YClcbiAgICAgICAgICBicmVha1xuICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgIGJyZWFrXG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgY29uc3QgZm9ybWF0UmVsYXRpdmVUaW1lID0gKGRhdGVTdHJpbmc6IHN0cmluZykgPT4ge1xuICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKClcbiAgICBjb25zdCBkYXRlID0gbmV3IERhdGUoZGF0ZVN0cmluZylcbiAgICBjb25zdCBkaWZmID0gbm93LmdldFRpbWUoKSAtIGRhdGUuZ2V0VGltZSgpXG4gICAgXG4gICAgY29uc3QgbWludXRlcyA9IE1hdGguZmxvb3IoZGlmZiAvICgxMDAwICogNjApKVxuICAgIGNvbnN0IGhvdXJzID0gTWF0aC5mbG9vcihkaWZmIC8gKDEwMDAgKiA2MCAqIDYwKSlcbiAgICBjb25zdCBkYXlzID0gTWF0aC5mbG9vcihkaWZmIC8gKDEwMDAgKiA2MCAqIDYwICogMjQpKVxuICAgIFxuICAgIGlmIChkYXlzID4gMCkgcmV0dXJuIGDZhdmG2LAgJHtkYXlzfSDZitmI2YVgXG4gICAgaWYgKGhvdXJzID4gMCkgcmV0dXJuIGDZhdmG2LAgJHtob3Vyc30g2LPYp9i52KlgXG4gICAgaWYgKG1pbnV0ZXMgPiAwKSByZXR1cm4gYNmF2YbYsCAke21pbnV0ZXN9INiv2YLZitmC2KlgXG4gICAgcmV0dXJuICfYp9mE2KLZhidcbiAgfVxuXG4gIGNvbnN0IGdldE5vdGlmaWNhdGlvbkljb24gPSAodHlwZTogc3RyaW5nKSA9PiB7XG4gICAgc3dpdGNoICh0eXBlKSB7XG4gICAgICBjYXNlICduZXdfYXBwbGljYXRpb24nOlxuICAgICAgICByZXR1cm4gPFNlbmQgY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LWJsdWUtNjAwXCIgLz5cbiAgICAgIGNhc2UgJ3RlbmRlcl9kZWFkbGluZV9hcHByb2FjaGluZyc6XG4gICAgICAgIHJldHVybiA8Q2xvY2sgY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LW9yYW5nZS02MDBcIiAvPlxuICAgICAgY2FzZSAnYXBwbGljYXRpb25fcmV2aWV3X3JlcXVpcmVkJzpcbiAgICAgICAgcmV0dXJuIDxFeWUgY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LXJlZC02MDBcIiAvPlxuICAgICAgY2FzZSAndGVuZGVyX3B1Ymxpc2hlZCc6XG4gICAgICAgIHJldHVybiA8RmlsZVRleHQgY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LWdyZWVuLTYwMFwiIC8+XG4gICAgICBjYXNlICdzeXN0ZW1fdXBkYXRlJzpcbiAgICAgICAgcmV0dXJuIDxCdWlsZGluZyBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtcHVycGxlLTYwMFwiIC8+XG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gPEJlbGwgY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LWdyYXktNjAwXCIgLz5cbiAgICB9XG4gIH1cblxuICBjb25zdCBnZXRQcmlvcml0eUJhZGdlID0gKHByaW9yaXR5OiBzdHJpbmcpID0+IHtcbiAgICBzd2l0Y2ggKHByaW9yaXR5KSB7XG4gICAgICBjYXNlICdoaWdoJzpcbiAgICAgICAgcmV0dXJuIDxCYWRnZSB2YXJpYW50PVwiZGVzdHJ1Y3RpdmVcIj7Yudin2YTZitipPC9CYWRnZT5cbiAgICAgIGNhc2UgJ21lZGl1bSc6XG4gICAgICAgIHJldHVybiA8QmFkZ2UgY2xhc3NOYW1lPVwiYmcteWVsbG93LTEwMCB0ZXh0LXllbGxvdy04MDBcIj7Zhdiq2YjYs9i32Kk8L0JhZGdlPlxuICAgICAgY2FzZSAnbG93JzpcbiAgICAgICAgcmV0dXJuIDxCYWRnZSB2YXJpYW50PVwic2Vjb25kYXJ5XCI+2YXZhtiu2YHYttipPC9CYWRnZT5cbiAgICAgIGRlZmF1bHQ6XG4gICAgICAgIHJldHVybiBudWxsXG4gICAgfVxuICB9XG5cbiAgY29uc3QgZmlsdGVyZWROb3RpZmljYXRpb25zID0gbm90aWZpY2F0aW9ucy5maWx0ZXIobm90aWYgPT4ge1xuICAgIGlmIChhY3RpdmVUYWIgPT09ICdhbGwnKSByZXR1cm4gdHJ1ZVxuICAgIGlmIChhY3RpdmVUYWIgPT09ICd1bnJlYWQnKSByZXR1cm4gIW5vdGlmLnJlYWRcbiAgICBpZiAoYWN0aXZlVGFiID09PSAncmVhZCcpIHJldHVybiBub3RpZi5yZWFkXG4gICAgcmV0dXJuIG5vdGlmLmNhdGVnb3J5ID09PSBhY3RpdmVUYWJcbiAgfSlcblxuICBjb25zdCB1bnJlYWRDb3VudCA9IG5vdGlmaWNhdGlvbnMuZmlsdGVyKG5vdGlmID0+ICFub3RpZi5yZWFkKS5sZW5ndGhcblxuICByZXR1cm4gKFxuICAgIDxEYXNoYm9hcmRMYXlvdXQgYWxsb3dlZFJvbGVzPXtbJ2dvdmVybm1lbnQnXX0+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICB7LyogSGVhZGVyICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLXIgZnJvbS1ncmVlbi02MDAgdG8tZW1lcmFsZC02MDAgdGV4dC13aGl0ZSByb3VuZGVkLWxnIHAtNlwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkXCI+2KXYtNi52KfYsdin2Kog2KfZhNis2YfYqSDYp9mE2K3Zg9mI2YXZitipPC9oMT5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmVlbi0xMDAgbXQtMVwiPlxuICAgICAgICAgICAgICAgINmF2KrYp9io2LnYqSDYrNmF2YrYuSDYp9mE2KrYrdiv2YrYq9in2Kog2KfZhNmF2KrYudmE2YLYqSDYqNmF2YbYp9mC2LXYp9iq2YMg2YjYt9mE2KjYp9iqINin2YTZhdi02KfYsdmD2KlcbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC00XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZFwiPnt1bnJlYWRDb3VudH08L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmVlbi0xMDBcIj7YutmK2LEg2YXZgtix2YjYodipPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICA8QnV0dG9uIFxuICAgICAgICAgICAgICAgICAgb25DbGljaz17bG9hZE5vdGlmaWNhdGlvbnN9XG4gICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy13aGl0ZS8xMCBib3JkZXItd2hpdGUvMjAgdGV4dC13aGl0ZSBob3ZlcjpiZy13aGl0ZS8yMFwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPFJlZnJlc2hDdyBjbGFzc05hbWU9XCJoLTQgdy00IG1sLTJcIiAvPlxuICAgICAgICAgICAgICAgICAg2KrYrdiv2YrYq1xuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgIHt1bnJlYWRDb3VudCA+IDAgJiYgKFxuICAgICAgICAgICAgICAgICAgPEJ1dHRvbiBcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17bWFya0FsbEFzUmVhZH1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctd2hpdGUgdGV4dC1ncmVlbi02MDAgaG92ZXI6YmctZ3JheS0xMDBcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8Q2hlY2tDaGVjayBjbGFzc05hbWU9XCJoLTQgdy00IG1sLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgICDYqtit2K/ZitivINin2YTZg9mEINmD2YXZgtix2YjYoVxuICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIFRhYnMgYW5kIE5vdGlmaWNhdGlvbnMgKi99XG4gICAgICAgIDxUYWJzIHZhbHVlPXthY3RpdmVUYWJ9IG9uVmFsdWVDaGFuZ2U9e3NldEFjdGl2ZVRhYn0+XG4gICAgICAgICAgPFRhYnNMaXN0IGNsYXNzTmFtZT1cImdyaWQgdy1mdWxsIGdyaWQtY29scy01XCI+XG4gICAgICAgICAgICA8VGFic1RyaWdnZXIgdmFsdWU9XCJhbGxcIj7Yp9mE2YPZhCAoe25vdGlmaWNhdGlvbnMubGVuZ3RofSk8L1RhYnNUcmlnZ2VyPlxuICAgICAgICAgICAgPFRhYnNUcmlnZ2VyIHZhbHVlPVwidW5yZWFkXCI+2LrZitixINmF2YLYsdmI2KHYqSAoe3VucmVhZENvdW50fSk8L1RhYnNUcmlnZ2VyPlxuICAgICAgICAgICAgPFRhYnNUcmlnZ2VyIHZhbHVlPVwidGVuZGVyXCI+2YXZhtin2YLYtdin2Ko8L1RhYnNUcmlnZ2VyPlxuICAgICAgICAgICAgPFRhYnNUcmlnZ2VyIHZhbHVlPVwiYXBwbGljYXRpb25cIj7Yt9mE2KjYp9iqPC9UYWJzVHJpZ2dlcj5cbiAgICAgICAgICAgIDxUYWJzVHJpZ2dlciB2YWx1ZT1cInN5c3RlbVwiPtin2YTZhti42KfZhTwvVGFic1RyaWdnZXI+XG4gICAgICAgICAgPC9UYWJzTGlzdD5cblxuICAgICAgICAgIDxUYWJzQ29udGVudCB2YWx1ZT17YWN0aXZlVGFifSBjbGFzc05hbWU9XCJtdC02XCI+XG4gICAgICAgICAgICB7bG9hZGluZyA/IChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBweS0xMlwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxSZWZyZXNoQ3cgY2xhc3NOYW1lPVwiaC04IHctOCBhbmltYXRlLXNwaW4gdGV4dC1ncmVlbi02MDAgbXgtYXV0byBtYi00XCIgLz5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj7YrNin2LHZiiDYqtit2YXZitmEINin2YTYpdi02LnYp9ix2KfYqi4uLjwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApIDogZmlsdGVyZWROb3RpZmljYXRpb25zLmxlbmd0aCA+IDAgPyAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAge2ZpbHRlcmVkTm90aWZpY2F0aW9ucy5tYXAoKG5vdGlmaWNhdGlvbikgPT4gKFxuICAgICAgICAgICAgICAgICAgPENhcmQgXG4gICAgICAgICAgICAgICAgICAgIGtleT17bm90aWZpY2F0aW9uLl9pZH0gXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHRyYW5zaXRpb24tYWxsIGhvdmVyOnNoYWRvdy1tZCBjdXJzb3ItcG9pbnRlciAke1xuICAgICAgICAgICAgICAgICAgICAgICFub3RpZmljYXRpb24ucmVhZCA/ICdib3JkZXItbC00IGJvcmRlci1sLWdyZWVuLTUwMCBiZy1ncmVlbi01MC81MCcgOiAnJ1xuICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlTm90aWZpY2F0aW9uQ2xpY2sobm90aWZpY2F0aW9uKX1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInAtNFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBnYXAtMyBmbGV4LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2dldE5vdGlmaWNhdGlvbkljb24obm90aWZpY2F0aW9uLnR5cGUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIG1iLTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9e2Bmb250LW1lZGl1bSAkeyFub3RpZmljYXRpb24ucmVhZCA/ICdmb250LXNlbWlib2xkJyA6ICcnfWB9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7bm90aWZpY2F0aW9uLnRpdGxlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHshbm90aWZpY2F0aW9uLnJlYWQgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtMiB3LTIgYmctZ3JlZW4tNjAwIHJvdW5kZWQtZnVsbFwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtnZXRQcmlvcml0eUJhZGdlKG5vdGlmaWNhdGlvbi5wcmlvcml0eSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCB0ZXh0LXNtIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtub3RpZmljYXRpb24ubWVzc2FnZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtNCB0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENhbGVuZGFyIGNsYXNzTmFtZT1cImgtMyB3LTNcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0UmVsYXRpdmVUaW1lKG5vdGlmaWNhdGlvbi5jcmVhdGVkQXQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7bm90aWZpY2F0aW9uLmRhdGE/LmFwcGxpY2FudE5hbWUgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFVzZXJzIGNsYXNzTmFtZT1cImgtMyB3LTNcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtub3RpZmljYXRpb24uZGF0YS5hcHBsaWNhbnROYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHshbm90aWZpY2F0aW9uLnJlYWQgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZS5zdG9wUHJvcGFnYXRpb24oKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtYXJrQXNSZWFkKG5vdGlmaWNhdGlvbi5faWQpXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDaGVjayBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZS5zdG9wUHJvcGFnYXRpb24oKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGVsZXRlTm90aWZpY2F0aW9uKG5vdGlmaWNhdGlvbi5faWQpXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUcmFzaDIgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS0xMlwiPlxuICAgICAgICAgICAgICAgIDxCZWxsIGNsYXNzTmFtZT1cImgtMTYgdy0xNiB0ZXh0LWdyYXktNDAwIG14LWF1dG8gbWItNFwiIC8+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBtYi0yXCI+2YTYpyDYqtmI2KzYryDYpdi02LnYp9ix2KfYqjwvaDM+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAge2FjdGl2ZVRhYiA9PT0gJ3VucmVhZCcgXG4gICAgICAgICAgICAgICAgICAgID8gJ9is2YXZiti5INin2YTYpdi02LnYp9ix2KfYqiDZhdmC2LHZiNih2KknIFxuICAgICAgICAgICAgICAgICAgICA6ICfYs9iq2LjZh9ixINmH2YbYpyDYp9mE2KXYtNi52KfYsdin2Kog2KfZhNmF2KrYudmE2YLYqSDYqNij2YbYtNi32Kkg2KzZh9iq2YPZhSDYp9mE2K3Zg9mI2YXZitipJ1xuICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvVGFic0NvbnRlbnQ+XG4gICAgICAgIDwvVGFicz5cbiAgICAgIDwvZGl2PlxuICAgIDwvRGFzaGJvYXJkTGF5b3V0PlxuICApXG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIkNhcmQiLCJDYXJkQ29udGVudCIsIkJ1dHRvbiIsIkJhZGdlIiwiVGFicyIsIlRhYnNDb250ZW50IiwiVGFic0xpc3QiLCJUYWJzVHJpZ2dlciIsInVzZVRvYXN0IiwidXNlUm91dGVyIiwiYXBpIiwiQmVsbCIsIkNoZWNrIiwiQ2hlY2tDaGVjayIsIlRyYXNoMiIsIlJlZnJlc2hDdyIsIkNhbGVuZGFyIiwiQ2xvY2siLCJGaWxlVGV4dCIsIlVzZXJzIiwiQnVpbGRpbmciLCJTZW5kIiwiRXllIiwiR292ZXJubWVudE5vdGlmaWNhdGlvbnNQYWdlIiwibm90aWZpY2F0aW9ucyIsInNldE5vdGlmaWNhdGlvbnMiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImFjdGl2ZVRhYiIsInNldEFjdGl2ZVRhYiIsInRvYXN0Iiwicm91dGVyIiwibG9hZE5vdGlmaWNhdGlvbnMiLCJyZXNwb25zZSIsImdldCIsImRhdGEiLCJzdWNjZXNzIiwiX2lkIiwidHlwZSIsInRpdGxlIiwibWVzc2FnZSIsInJlYWQiLCJjcmVhdGVkQXQiLCJEYXRlIiwibm93IiwidG9JU09TdHJpbmciLCJwcmlvcml0eSIsImNhdGVnb3J5IiwidGVuZGVySWQiLCJhcHBsaWNhbnROYW1lIiwiYXBwbGljYXRpb25JZCIsImVycm9yIiwiY29uc29sZSIsImRlc2NyaXB0aW9uIiwidmFyaWFudCIsIm1hcmtBc1JlYWQiLCJub3RpZmljYXRpb25JZCIsInBhdGNoIiwibWFwIiwibm90aWYiLCJtYXJrQWxsQXNSZWFkIiwiZGVsZXRlTm90aWZpY2F0aW9uIiwiZGVsZXRlIiwiZmlsdGVyIiwiaGFuZGxlTm90aWZpY2F0aW9uQ2xpY2siLCJub3RpZmljYXRpb24iLCJwdXNoIiwiZm9ybWF0UmVsYXRpdmVUaW1lIiwiZGF0ZVN0cmluZyIsImRhdGUiLCJkaWZmIiwiZ2V0VGltZSIsIm1pbnV0ZXMiLCJNYXRoIiwiZmxvb3IiLCJob3VycyIsImRheXMiLCJnZXROb3RpZmljYXRpb25JY29uIiwiY2xhc3NOYW1lIiwiZ2V0UHJpb3JpdHlCYWRnZSIsImZpbHRlcmVkTm90aWZpY2F0aW9ucyIsInVucmVhZENvdW50IiwibGVuZ3RoIiwiRGFzaGJvYXJkTGF5b3V0IiwiYWxsb3dlZFJvbGVzIiwiZGl2IiwiaDEiLCJwIiwib25DbGljayIsInZhbHVlIiwib25WYWx1ZUNoYW5nZSIsImgzIiwic2l6ZSIsImUiLCJzdG9wUHJvcGFnYXRpb24iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/government/notifications/page.tsx\n"));

/***/ })

});