"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/tenders/[id]/page",{

/***/ "(app-pages-browser)/./app/tenders/[id]/page.tsx":
/*!***********************************!*\
  !*** ./app/tenders/[id]/page.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TenderDetailsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.tsx\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./components/DashboardLayout.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction TenderDetailsPage() {\n    var _tender_location, _tender_applications, _tender_organizer_profile, _tender_organizer, _tender_organizer_profile1, _tender_organizer1, _tender_organizer_profile2, _tender_organizer2;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const [tender, setTender] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [applying, setApplying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hasApplied, setHasApplied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFavorite, setIsFavorite] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [favoriteLoading, setFavoriteLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const userData = localStorage.getItem(\"user\");\n        if (userData) {\n            setUser(JSON.parse(userData));\n        }\n        loadTender();\n        checkFavoriteStatus();\n    }, [\n        params.id\n    ]);\n    const loadTender = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_9__[\"default\"].get(\"/tenders/\".concat(params.id));\n            if (response.data.success) {\n                setTender(response.data.data.tender);\n                // Check if user has already applied\n                if (user && response.data.data.tender.applications) {\n                    const userApplication = response.data.data.tender.applications.find((app)=>app.applicant && app.applicant._id === user._id);\n                    setHasApplied(!!userApplication);\n                }\n            } else {\n                toast({\n                    title: \"خطأ\",\n                    description: \"لم يتم العثور على المناقصة\",\n                    variant: \"destructive\"\n                });\n                router.push(\"/tenders\");\n            }\n        } catch (error) {\n            console.error(\"Error loading tender:\", error);\n            toast({\n                title: \"خطأ في التحميل\",\n                description: \"حدث خطأ في تحميل بيانات المناقصة\",\n                variant: \"destructive\"\n            });\n            router.push(\"/tenders\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const checkFavoriteStatus = async ()=>{\n        try {\n            if (!user) return;\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_9__[\"default\"].get(\"/favorites/check/tender/\".concat(params.id));\n            setIsFavorite(response.data.isFavorite);\n        } catch (error) {\n            console.error(\"Error checking favorite status:\", error);\n        }\n    };\n    const toggleFavorite = async ()=>{\n        try {\n            if (!user) {\n                toast({\n                    title: \"تسجيل الدخول مطلوب\",\n                    description: \"يجب تسجيل الدخول لإضافة العناصر للمفضلة\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            setFavoriteLoading(true);\n            if (isFavorite) {\n                // Remove from favorites\n                await _lib_api__WEBPACK_IMPORTED_MODULE_9__.favoritesAPI.removeFavorite(\"tender\", params.id);\n                setIsFavorite(false);\n                toast({\n                    title: \"❤️ تم إزالة من المفضلة\",\n                    description: 'تم إزالة \"'.concat(tender === null || tender === void 0 ? void 0 : tender.title, '\" من المفضلة')\n                });\n            } else {\n                // Add to favorites\n                await _lib_api__WEBPACK_IMPORTED_MODULE_9__.favoritesAPI.addFavorite({\n                    itemType: \"tender\",\n                    itemId: params.id,\n                    notifications: {\n                        bidUpdates: true,\n                        statusChanges: true,\n                        endingSoon: true\n                    }\n                });\n                setIsFavorite(true);\n                toast({\n                    title: \"\\uD83D\\uDC96 تم إضافة إلى المفضلة\",\n                    description: 'تم إضافة \"'.concat(tender === null || tender === void 0 ? void 0 : tender.title, '\" إلى المفضلة')\n                });\n            }\n        } catch (error) {\n            console.error(\"Error toggling favorite:\", error);\n            toast({\n                title: \"خطأ في المفضلة\",\n                description: \"حدث خطأ في تحديث المفضلة\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setFavoriteLoading(false);\n        }\n    };\n    const applyToTender = async ()=>{\n        if (!user) {\n            toast({\n                title: \"تسجيل الدخول مطلوب\",\n                description: \"يجب تسجيل الدخول للتقدم للمناقصة\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            setApplying(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_9__[\"default\"].post(\"/tenders/\".concat(params.id, \"/apply\"));\n            if (response.data.success) {\n                toast({\n                    title: \"تم التقديم\",\n                    description: \"تم تقديم طلبك للمناقصة بنجاح\"\n                });\n                setHasApplied(true);\n                loadTender() // Reload to get updated data\n                ;\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            toast({\n                title: \"خطأ في التقديم\",\n                description: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"حدث خطأ في تقديم الطلب\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setApplying(false);\n        }\n    };\n    const formatPrice = (price)=>{\n        return new Intl.NumberFormat(\"ar-SA\", {\n            style: \"currency\",\n            currency: \"SAR\",\n            minimumFractionDigits: 0\n        }).format(price);\n    };\n    const formatTimeRemaining = (deadline)=>{\n        const now = new Date();\n        const end = new Date(deadline);\n        const diff = end.getTime() - now.getTime();\n        if (diff <= 0) return \"انتهى\";\n        const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n        const hours = Math.floor(diff % (1000 * 60 * 60 * 24) / (1000 * 60 * 60));\n        if (days > 0) return \"\".concat(days, \" يوم \").concat(hours, \" ساعة\");\n        if (hours > 0) return \"\".concat(hours, \" ساعة\");\n        return \"أقل من ساعة\";\n    };\n    const getStatusBadge = (status)=>{\n        switch(status){\n            case \"active\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    className: \"bg-green-100 text-green-800\",\n                    children: \"نشط\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 16\n                }, this);\n            case \"completed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    className: \"bg-blue-100 text-blue-800\",\n                    children: \"مكتمل\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 16\n                }, this);\n            case \"review\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    className: \"bg-yellow-100 text-yellow-800\",\n                    children: \"قيد المراجعة\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                    lineNumber: 248,\n                    columnNumber: 16\n                }, this);\n            case \"cancelled\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"destructive\",\n                    children: \"ملغي\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"outline\",\n                    children: status\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                    lineNumber: 252,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getCategoryName = (category)=>{\n        const categories = {\n            \"construction\": \"إنشاءات\",\n            \"it_technology\": \"تقنية المعلومات\",\n            \"consulting\": \"استشارات\",\n            \"healthcare\": \"رعاية صحية\",\n            \"education\": \"تعليم\",\n            \"transportation\": \"نقل ومواصلات\",\n            \"other\": \"أخرى\"\n        };\n        return categories[category] || category;\n    };\n    const canEdit = user && tender && tender.organizer && user._id === tender.organizer._id;\n    const canApply = user && tender && tender.organizer && user._id !== tender.organizer._id && tender.status === \"active\" && !hasApplied;\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            allowedRoles: [\n                \"user\",\n                \"company\",\n                \"government\",\n                \"admin\"\n            ],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-96\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-4 text-gray-600\",\n                            children: \"جاري تحميل تفاصيل المناقصة...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                    lineNumber: 276,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                lineNumber: 275,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n            lineNumber: 274,\n            columnNumber: 7\n        }, this);\n    }\n    if (!tender) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            allowedRoles: [\n                \"user\",\n                \"company\",\n                \"government\",\n                \"admin\"\n            ],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                        children: \"المناقصة غير موجودة\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-6\",\n                        children: \"لم يتم العثور على المناقصة المطلوبة\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        onClick: ()=>router.push(\"/tenders\"),\n                        children: \"العودة للمناقصات\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                lineNumber: 288,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n            lineNumber: 287,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n        allowedRoles: [\n            \"user\",\n            \"company\",\n            \"government\",\n            \"admin\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            variant: \"ghost\",\n                            onClick: ()=>router.push(\"/tenders\"),\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 13\n                                }, this),\n                                \"العودة للمناقصات\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    onClick: toggleFavorite,\n                                    disabled: favoriteLoading,\n                                    className: isFavorite ? \"text-red-600 border-red-200 bg-red-50\" : \"\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 ml-2 \".concat(isFavorite ? \"fill-red-600\" : \"\")\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 15\n                                        }, this),\n                                        favoriteLoading ? \"جاري التحديث...\" : isFavorite ? \"إزالة من المفضلة\" : \"إضافة للمفضلة\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"مشاركة\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 13\n                                }, this),\n                                canEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>router.push(\"/tenders/\".concat(tender._id, \"/edit\")),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"تعديل\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                    lineNumber: 304,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2 space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                            className: \"text-2xl\",\n                                                            children: tender.title\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 345,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-4 mt-3\",\n                                                            children: [\n                                                                getStatusBadge(tender.status),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-1 text-gray-600\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                            lineNumber: 349,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: [\n                                                                                tender.viewsCount,\n                                                                                \" مشاهدة\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                            lineNumber: 350,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 348,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-1 text-gray-600\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                            lineNumber: 353,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: new Date(tender.createdAt).toLocaleDateString(\"ar-SA\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                            lineNumber: 354,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 352,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 346,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-3xl font-bold text-green-600\",\n                                                            children: formatPrice(tender.budget)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 359,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"الميزانية المخصصة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 362,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"وصف المناقصة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-700 leading-relaxed whitespace-pre-line\",\n                                                children: tender.description\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 13\n                                }, this),\n                                tender.requirements && tender.requirements.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"المتطلبات والشروط\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                            lineNumber: 383,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-2\",\n                                                children: tender.requirements.map((requirement, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-start gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"h-5 w-5 text-green-600 mt-0.5 flex-shrink-0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                lineNumber: 390,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-700\",\n                                                                children: requirement\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                lineNumber: 391,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                    lineNumber: 382,\n                                    columnNumber: 15\n                                }, this),\n                                tender.documents && tender.documents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"المستندات المرفقة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: tender.documents.map((document, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                        lineNumber: 410,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: [\n                                                                            \"مستند \",\n                                                                            index + 1\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                        lineNumber: 411,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                lineNumber: 409,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-4 w-4 ml-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                        lineNumber: 414,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"تحميل\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                        lineNumber: 408,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                            lineNumber: 405,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                    lineNumber: 401,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                canApply && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: applyToTender,\n                                            disabled: applying,\n                                            className: \"w-full h-12 text-lg bg-green-600 hover:bg-green-700\",\n                                            children: applying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                        lineNumber: 438,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    \"جاري التقديم...\"\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-5 w-5 ml-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    \"تقدم للمناقصة\"\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 15\n                                }, this),\n                                hasApplied && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"border-green-200 bg-green-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"p-6 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-12 w-12 text-green-600 mx-auto mb-3\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                lineNumber: 455,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium text-green-900 mb-2\",\n                                                children: \"تم التقديم\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-green-700\",\n                                                children: \"لقد تقدمت لهذه المناقصة بالفعل\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                    lineNumber: 453,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"معلومات المناقصة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                lineNumber: 465,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                            lineNumber: 464,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"h-5 w-5 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 469,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"الموعد النهائي\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 471,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: new Date(tender.deadline).toLocaleDateString(\"ar-SA\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 472,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-orange-600 font-medium\",\n                                                                    children: formatTimeRemaining(tender.deadline)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 475,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 470,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                    lineNumber: 468,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                    lineNumber: 481,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                            className: \"h-5 w-5 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 484,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"الفئة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 486,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: getCategoryName(tender.category)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 487,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 485,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                    lineNumber: 483,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                    lineNumber: 491,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            className: \"h-5 w-5 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 494,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"الموقع\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 496,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: typeof tender.location === \"object\" && ((_tender_location = tender.location) === null || _tender_location === void 0 ? void 0 : _tender_location.country) ? tender.location.country : tender.location || \"غير محدد\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 497,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 495,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                    lineNumber: 493,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                    lineNumber: 505,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            className: \"h-5 w-5 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 508,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"عدد المتقدمين\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 510,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: [\n                                                                        ((_tender_applications = tender.applications) === null || _tender_applications === void 0 ? void 0 : _tender_applications.length) || 0,\n                                                                        \" متقدم\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 511,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 509,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                    lineNumber: 507,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                            lineNumber: 467,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                    lineNumber: 463,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"الجهة المنظمة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                lineNumber: 520,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                            lineNumber: 519,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                            className: \"h-5 w-5 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 524,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium\",\n                                                                    children: ((_tender_organizer = tender.organizer) === null || _tender_organizer === void 0 ? void 0 : (_tender_organizer_profile = _tender_organizer.profile) === null || _tender_organizer_profile === void 0 ? void 0 : _tender_organizer_profile.governmentEntity) || \"غير محدد\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 526,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: \"جهة حكومية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 529,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 525,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                    lineNumber: 523,\n                                                    columnNumber: 17\n                                                }, this),\n                                                ((_tender_organizer1 = tender.organizer) === null || _tender_organizer1 === void 0 ? void 0 : (_tender_organizer_profile1 = _tender_organizer1.profile) === null || _tender_organizer_profile1 === void 0 ? void 0 : _tender_organizer_profile1.phone) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 535,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-gray-600\",\n                                                                    children: \"الهاتف\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 537,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: tender.organizer.profile.phone\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 538,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 536,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true),\n                                                ((_tender_organizer2 = tender.organizer) === null || _tender_organizer2 === void 0 ? void 0 : (_tender_organizer_profile2 = _tender_organizer2.profile) === null || _tender_organizer_profile2 === void 0 ? void 0 : _tender_organizer_profile2.email) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 545,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-gray-600\",\n                                                                    children: \"البريد الإلكتروني\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 547,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: tender.organizer.profile.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 548,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 546,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                            lineNumber: 522,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                    lineNumber: 518,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                            lineNumber: 426,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                    lineNumber: 337,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n            lineNumber: 302,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n        lineNumber: 301,\n        columnNumber: 5\n    }, this);\n}\n_s(TenderDetailsPage, \"ALHWcXOa+/Iw5P7KP8VB6qumn5k=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = TenderDetailsPage;\nvar _c;\n$RefreshReg$(_c, \"TenderDetailsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/tenders/[id]/page.tsx\n"));

/***/ })

});