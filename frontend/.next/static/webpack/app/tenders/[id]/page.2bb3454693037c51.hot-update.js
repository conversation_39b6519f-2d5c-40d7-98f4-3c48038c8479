"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/tenders/[id]/page",{

/***/ "(app-pages-browser)/./app/tenders/[id]/page.tsx":
/*!***********************************!*\
  !*** ./app/tenders/[id]/page.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TenderDetailsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.tsx\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./components/DashboardLayout.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction TenderDetailsPage() {\n    var _tender_location, _tender_applications, _tender_organizer_profile, _tender_organizer, _tender_organizer_profile1, _tender_organizer1, _tender_organizer_profile2, _tender_organizer2;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const [tender, setTender] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [applying, setApplying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hasApplied, setHasApplied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFavorite, setIsFavorite] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [favoriteLoading, setFavoriteLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const userData = localStorage.getItem(\"user\");\n        if (userData) {\n            setUser(JSON.parse(userData));\n        }\n        loadTender();\n        checkFavoriteStatus();\n    }, [\n        params.id\n    ]);\n    const loadTender = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_9__[\"default\"].get(\"/tenders/\".concat(params.id));\n            if (response.data.success) {\n                setTender(response.data.data.tender);\n                // Check if user has already applied\n                if (user && response.data.data.tender.applications) {\n                    const userApplication = response.data.data.tender.applications.find((app)=>app.applicant && app.applicant._id === user._id);\n                    setHasApplied(!!userApplication);\n                }\n            } else {\n                toast({\n                    title: \"خطأ\",\n                    description: \"لم يتم العثور على المناقصة\",\n                    variant: \"destructive\"\n                });\n                router.push(\"/tenders\");\n            }\n        } catch (error) {\n            console.error(\"Error loading tender:\", error);\n            toast({\n                title: \"خطأ في التحميل\",\n                description: \"حدث خطأ في تحميل بيانات المناقصة\",\n                variant: \"destructive\"\n            });\n            router.push(\"/tenders\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const checkFavoriteStatus = async ()=>{\n        try {\n            if (!user) return;\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_9__.favoritesAPI.checkFavorite(\"tender\", params.id);\n            setIsFavorite(response.data.isFavorite);\n        } catch (error) {\n            console.error(\"Error checking favorite status:\", error);\n        }\n    };\n    const toggleFavorite = async ()=>{\n        try {\n            if (!user) {\n                toast({\n                    title: \"تسجيل الدخول مطلوب\",\n                    description: \"يجب تسجيل الدخول لإضافة العناصر للمفضلة\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            setFavoriteLoading(true);\n            if (isFavorite) {\n                // Remove from favorites\n                await _lib_api__WEBPACK_IMPORTED_MODULE_9__.favoritesAPI.removeFavorite(\"tender\", params.id);\n                setIsFavorite(false);\n                toast({\n                    title: \"❤️ تم إزالة من المفضلة\",\n                    description: 'تم إزالة \"'.concat(tender === null || tender === void 0 ? void 0 : tender.title, '\" من المفضلة')\n                });\n            } else {\n                // Add to favorites\n                await _lib_api__WEBPACK_IMPORTED_MODULE_9__.favoritesAPI.addFavorite({\n                    itemType: \"tender\",\n                    itemId: params.id,\n                    notifications: {\n                        bidUpdates: true,\n                        statusChanges: true,\n                        endingSoon: true\n                    }\n                });\n                setIsFavorite(true);\n                toast({\n                    title: \"\\uD83D\\uDC96 تم إضافة إلى المفضلة\",\n                    description: 'تم إضافة \"'.concat(tender === null || tender === void 0 ? void 0 : tender.title, '\" إلى المفضلة')\n                });\n            }\n        } catch (error) {\n            console.error(\"Error toggling favorite:\", error);\n            toast({\n                title: \"خطأ في المفضلة\",\n                description: \"حدث خطأ في تحديث المفضلة\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setFavoriteLoading(false);\n        }\n    };\n    const applyToTender = async ()=>{\n        if (!user) {\n            toast({\n                title: \"تسجيل الدخول مطلوب\",\n                description: \"يجب تسجيل الدخول للتقدم للمناقصة\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            setApplying(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_9__[\"default\"].post(\"/tenders/\".concat(params.id, \"/apply\"));\n            if (response.data.success) {\n                toast({\n                    title: \"تم التقديم\",\n                    description: \"تم تقديم طلبك للمناقصة بنجاح\"\n                });\n                setHasApplied(true);\n                loadTender() // Reload to get updated data\n                ;\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            toast({\n                title: \"خطأ في التقديم\",\n                description: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"حدث خطأ في تقديم الطلب\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setApplying(false);\n        }\n    };\n    const formatPrice = (price)=>{\n        return new Intl.NumberFormat(\"ar-SA\", {\n            style: \"currency\",\n            currency: \"SAR\",\n            minimumFractionDigits: 0\n        }).format(price);\n    };\n    const formatTimeRemaining = (deadline)=>{\n        const now = new Date();\n        const end = new Date(deadline);\n        const diff = end.getTime() - now.getTime();\n        if (diff <= 0) return \"انتهى\";\n        const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n        const hours = Math.floor(diff % (1000 * 60 * 60 * 24) / (1000 * 60 * 60));\n        if (days > 0) return \"\".concat(days, \" يوم \").concat(hours, \" ساعة\");\n        if (hours > 0) return \"\".concat(hours, \" ساعة\");\n        return \"أقل من ساعة\";\n    };\n    const getStatusBadge = (status)=>{\n        switch(status){\n            case \"active\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    className: \"bg-green-100 text-green-800\",\n                    children: \"نشط\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 16\n                }, this);\n            case \"completed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    className: \"bg-blue-100 text-blue-800\",\n                    children: \"مكتمل\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 16\n                }, this);\n            case \"review\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    className: \"bg-yellow-100 text-yellow-800\",\n                    children: \"قيد المراجعة\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                    lineNumber: 248,\n                    columnNumber: 16\n                }, this);\n            case \"cancelled\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"destructive\",\n                    children: \"ملغي\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"outline\",\n                    children: status\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                    lineNumber: 252,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getCategoryName = (category)=>{\n        const categories = {\n            \"construction\": \"إنشاءات\",\n            \"it_technology\": \"تقنية المعلومات\",\n            \"consulting\": \"استشارات\",\n            \"healthcare\": \"رعاية صحية\",\n            \"education\": \"تعليم\",\n            \"transportation\": \"نقل ومواصلات\",\n            \"other\": \"أخرى\"\n        };\n        return categories[category] || category;\n    };\n    const canEdit = user && tender && tender.organizer && user._id === tender.organizer._id;\n    const canApply = user && tender && tender.organizer && user._id !== tender.organizer._id && tender.status === \"active\" && !hasApplied;\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            allowedRoles: [\n                \"user\",\n                \"company\",\n                \"government\",\n                \"admin\"\n            ],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-96\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-4 text-gray-600\",\n                            children: \"جاري تحميل تفاصيل المناقصة...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                    lineNumber: 276,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                lineNumber: 275,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n            lineNumber: 274,\n            columnNumber: 7\n        }, this);\n    }\n    if (!tender) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            allowedRoles: [\n                \"user\",\n                \"company\",\n                \"government\",\n                \"admin\"\n            ],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                        children: \"المناقصة غير موجودة\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-6\",\n                        children: \"لم يتم العثور على المناقصة المطلوبة\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        onClick: ()=>router.push(\"/tenders\"),\n                        children: \"العودة للمناقصات\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                lineNumber: 288,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n            lineNumber: 287,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n        allowedRoles: [\n            \"user\",\n            \"company\",\n            \"government\",\n            \"admin\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            variant: \"ghost\",\n                            onClick: ()=>router.push(\"/tenders\"),\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 13\n                                }, this),\n                                \"العودة للمناقصات\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"إضافة للمفضلة\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"مشاركة\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 13\n                                }, this),\n                                canEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>router.push(\"/tenders/\".concat(tender._id, \"/edit\")),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"تعديل\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                    lineNumber: 304,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2 space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                            className: \"text-2xl\",\n                                                            children: tender.title\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 340,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-4 mt-3\",\n                                                            children: [\n                                                                getStatusBadge(tender.status),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-1 text-gray-600\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                            lineNumber: 344,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: [\n                                                                                tender.viewsCount,\n                                                                                \" مشاهدة\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                            lineNumber: 345,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 343,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-1 text-gray-600\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                            lineNumber: 348,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: new Date(tender.createdAt).toLocaleDateString(\"ar-SA\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                            lineNumber: 349,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 347,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 341,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-3xl font-bold text-green-600\",\n                                                            children: formatPrice(tender.budget)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"الميزانية المخصصة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 357,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"وصف المناقصة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                lineNumber: 366,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-700 leading-relaxed whitespace-pre-line\",\n                                                children: tender.description\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                lineNumber: 369,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 13\n                                }, this),\n                                tender.requirements && tender.requirements.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"المتطلبات والشروط\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                            lineNumber: 378,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-2\",\n                                                children: tender.requirements.map((requirement, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-start gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"h-5 w-5 text-green-600 mt-0.5 flex-shrink-0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                lineNumber: 385,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-700\",\n                                                                children: requirement\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                lineNumber: 386,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                            lineNumber: 381,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                    lineNumber: 377,\n                                    columnNumber: 15\n                                }, this),\n                                tender.documents && tender.documents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"المستندات المرفقة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: tender.documents.map((document, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                        lineNumber: 405,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: [\n                                                                            \"مستند \",\n                                                                            index + 1\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                        lineNumber: 406,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                lineNumber: 404,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-4 w-4 ml-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                        lineNumber: 409,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"تحميل\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                lineNumber: 408,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                        lineNumber: 403,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                            lineNumber: 400,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                            lineNumber: 334,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                canApply && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: applyToTender,\n                                            disabled: applying,\n                                            className: \"w-full h-12 text-lg bg-green-600 hover:bg-green-700\",\n                                            children: applying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                        lineNumber: 433,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    \"جاري التقديم...\"\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-5 w-5 ml-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                        lineNumber: 438,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    \"تقدم للمناقصة\"\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                            lineNumber: 426,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                    lineNumber: 424,\n                                    columnNumber: 15\n                                }, this),\n                                hasApplied && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"border-green-200 bg-green-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"p-6 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-12 w-12 text-green-600 mx-auto mb-3\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium text-green-900 mb-2\",\n                                                children: \"تم التقديم\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-green-700\",\n                                                children: \"لقد تقدمت لهذه المناقصة بالفعل\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                lineNumber: 452,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                    lineNumber: 448,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"معلومات المناقصة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"h-5 w-5 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 464,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"الموعد النهائي\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 466,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: new Date(tender.deadline).toLocaleDateString(\"ar-SA\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 467,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-orange-600 font-medium\",\n                                                                    children: formatTimeRemaining(tender.deadline)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 470,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 465,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                    lineNumber: 463,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                            className: \"h-5 w-5 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 479,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"الفئة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 481,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: getCategoryName(tender.category)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 482,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 480,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                    lineNumber: 478,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                    lineNumber: 486,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            className: \"h-5 w-5 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 489,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"الموقع\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 491,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: typeof tender.location === \"object\" && ((_tender_location = tender.location) === null || _tender_location === void 0 ? void 0 : _tender_location.country) ? tender.location.country : tender.location || \"غير محدد\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 492,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 490,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                    lineNumber: 488,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                    lineNumber: 500,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            className: \"h-5 w-5 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 503,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"عدد المتقدمين\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 505,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: [\n                                                                        ((_tender_applications = tender.applications) === null || _tender_applications === void 0 ? void 0 : _tender_applications.length) || 0,\n                                                                        \" متقدم\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 506,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 504,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                    lineNumber: 502,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                            lineNumber: 462,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                    lineNumber: 458,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"الجهة المنظمة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                lineNumber: 515,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                            lineNumber: 514,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                            className: \"h-5 w-5 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 519,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium\",\n                                                                    children: ((_tender_organizer = tender.organizer) === null || _tender_organizer === void 0 ? void 0 : (_tender_organizer_profile = _tender_organizer.profile) === null || _tender_organizer_profile === void 0 ? void 0 : _tender_organizer_profile.governmentEntity) || \"غير محدد\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 521,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: \"جهة حكومية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 524,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 520,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                    lineNumber: 518,\n                                                    columnNumber: 17\n                                                }, this),\n                                                ((_tender_organizer1 = tender.organizer) === null || _tender_organizer1 === void 0 ? void 0 : (_tender_organizer_profile1 = _tender_organizer1.profile) === null || _tender_organizer_profile1 === void 0 ? void 0 : _tender_organizer_profile1.phone) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 530,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-gray-600\",\n                                                                    children: \"الهاتف\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 532,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: tender.organizer.profile.phone\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 533,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 531,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true),\n                                                ((_tender_organizer2 = tender.organizer) === null || _tender_organizer2 === void 0 ? void 0 : (_tender_organizer_profile2 = _tender_organizer2.profile) === null || _tender_organizer_profile2 === void 0 ? void 0 : _tender_organizer_profile2.email) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 540,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-gray-600\",\n                                                                    children: \"البريد الإلكتروني\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 542,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: tender.organizer.profile.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 543,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 541,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                            lineNumber: 517,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                    lineNumber: 513,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                            lineNumber: 421,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                    lineNumber: 332,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n            lineNumber: 302,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n        lineNumber: 301,\n        columnNumber: 5\n    }, this);\n}\n_s(TenderDetailsPage, \"ALHWcXOa+/Iw5P7KP8VB6qumn5k=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = TenderDetailsPage;\nvar _c;\n$RefreshReg$(_c, \"TenderDetailsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/tenders/[id]/page.tsx\n"));

/***/ })

});