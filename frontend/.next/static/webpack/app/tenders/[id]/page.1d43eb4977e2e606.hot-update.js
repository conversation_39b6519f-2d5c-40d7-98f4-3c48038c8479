"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/tenders/[id]/page",{

/***/ "(app-pages-browser)/./app/tenders/[id]/page.tsx":
/*!***********************************!*\
  !*** ./app/tenders/[id]/page.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TenderDetailsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.tsx\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./components/DashboardLayout.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building,Calendar,CheckCircle,Clock,Download,Edit,Eye,FileText,Heart,MapPin,Send,Share2,Tag,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction TenderDetailsPage() {\n    var _tender_applications;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const [tender, setTender] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [applying, setApplying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hasApplied, setHasApplied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const userData = localStorage.getItem(\"user\");\n        if (userData) {\n            setUser(JSON.parse(userData));\n        }\n        loadTender();\n    }, [\n        params.id\n    ]);\n    const loadTender = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_9__[\"default\"].get(\"/tenders/\".concat(params.id));\n            if (response.data.success) {\n                setTender(response.data.data);\n                // Check if user has already applied\n                if (user && response.data.data.applications) {\n                    const userApplication = response.data.data.applications.find((app)=>app.applicant && app.applicant._id === user._id);\n                    setHasApplied(!!userApplication);\n                }\n            } else {\n                toast({\n                    title: \"خطأ\",\n                    description: \"لم يتم العثور على المناقصة\",\n                    variant: \"destructive\"\n                });\n                router.push(\"/tenders\");\n            }\n        } catch (error) {\n            console.error(\"Error loading tender:\", error);\n            toast({\n                title: \"خطأ في التحميل\",\n                description: \"حدث خطأ في تحميل بيانات المناقصة\",\n                variant: \"destructive\"\n            });\n            router.push(\"/tenders\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const applyToTender = async ()=>{\n        if (!user) {\n            toast({\n                title: \"تسجيل الدخول مطلوب\",\n                description: \"يجب تسجيل الدخول للتقدم للمناقصة\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            setApplying(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_9__[\"default\"].post(\"/tenders/\".concat(params.id, \"/apply\"));\n            if (response.data.success) {\n                toast({\n                    title: \"تم التقديم\",\n                    description: \"تم تقديم طلبك للمناقصة بنجاح\"\n                });\n                setHasApplied(true);\n                loadTender() // Reload to get updated data\n                ;\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            toast({\n                title: \"خطأ في التقديم\",\n                description: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"حدث خطأ في تقديم الطلب\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setApplying(false);\n        }\n    };\n    const formatPrice = (price)=>{\n        return new Intl.NumberFormat(\"ar-SA\", {\n            style: \"currency\",\n            currency: \"SAR\",\n            minimumFractionDigits: 0\n        }).format(price);\n    };\n    const formatTimeRemaining = (deadline)=>{\n        const now = new Date();\n        const end = new Date(deadline);\n        const diff = end.getTime() - now.getTime();\n        if (diff <= 0) return \"انتهى\";\n        const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n        const hours = Math.floor(diff % (1000 * 60 * 60 * 24) / (1000 * 60 * 60));\n        if (days > 0) return \"\".concat(days, \" يوم \").concat(hours, \" ساعة\");\n        if (hours > 0) return \"\".concat(hours, \" ساعة\");\n        return \"أقل من ساعة\";\n    };\n    const getStatusBadge = (status)=>{\n        switch(status){\n            case \"active\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    className: \"bg-green-100 text-green-800\",\n                    children: \"نشط\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                    lineNumber: 181,\n                    columnNumber: 16\n                }, this);\n            case \"completed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    className: \"bg-blue-100 text-blue-800\",\n                    children: \"مكتمل\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                    lineNumber: 183,\n                    columnNumber: 16\n                }, this);\n            case \"review\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    className: \"bg-yellow-100 text-yellow-800\",\n                    children: \"قيد المراجعة\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 16\n                }, this);\n            case \"cancelled\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"destructive\",\n                    children: \"ملغي\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"outline\",\n                    children: status\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                    lineNumber: 189,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getCategoryName = (category)=>{\n        const categories = {\n            \"construction\": \"إنشاءات\",\n            \"it_technology\": \"تقنية المعلومات\",\n            \"consulting\": \"استشارات\",\n            \"healthcare\": \"رعاية صحية\",\n            \"education\": \"تعليم\",\n            \"transportation\": \"نقل ومواصلات\",\n            \"other\": \"أخرى\"\n        };\n        return categories[category] || category;\n    };\n    const canEdit = user && tender && tender.organizer && user._id === tender.organizer._id;\n    const canApply = user && tender && tender.organizer && user._id !== tender.organizer._id && tender.status === \"active\" && !hasApplied;\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            allowedRoles: [\n                \"user\",\n                \"company\",\n                \"government\",\n                \"admin\"\n            ],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-96\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-4 text-gray-600\",\n                            children: \"جاري تحميل تفاصيل المناقصة...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                lineNumber: 212,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n            lineNumber: 211,\n            columnNumber: 7\n        }, this);\n    }\n    if (!tender) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            allowedRoles: [\n                \"user\",\n                \"company\",\n                \"government\",\n                \"admin\"\n            ],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                        children: \"المناقصة غير موجودة\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-6\",\n                        children: \"لم يتم العثور على المناقصة المطلوبة\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        onClick: ()=>router.push(\"/tenders\"),\n                        children: \"العودة للمناقصات\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                lineNumber: 225,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n            lineNumber: 224,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n        allowedRoles: [\n            \"user\",\n            \"company\",\n            \"government\",\n            \"admin\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            variant: \"ghost\",\n                            onClick: ()=>router.push(\"/tenders\"),\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, this),\n                                \"العودة للمناقصات\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"إضافة للمفضلة\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"مشاركة\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 13\n                                }, this),\n                                canEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"outline\",\n                                            onClick: ()=>router.push(\"/tenders/\".concat(tender._id, \"/edit\")),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-4 w-4 ml-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"تعديل\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"destructive\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-4 w-4 ml-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"حذف\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2 space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                            className: \"text-2xl\",\n                                                            children: tender.title\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-4 mt-3\",\n                                                            children: [\n                                                                getStatusBadge(tender.status),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-1 text-gray-600\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                            lineNumber: 287,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: [\n                                                                                tender.viewsCount,\n                                                                                \" مشاهدة\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                            lineNumber: 288,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 286,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-1 text-gray-600\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                            lineNumber: 291,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: new Date(tender.createdAt).toLocaleDateString(\"ar-SA\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                            lineNumber: 292,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 290,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 284,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-3xl font-bold text-green-600\",\n                                                            children: formatPrice(tender.budget)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"الميزانية المخصصة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"وصف المناقصة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-700 leading-relaxed whitespace-pre-line\",\n                                                children: tender.description\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 13\n                                }, this),\n                                tender.requirements && tender.requirements.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"المتطلبات والشروط\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-2\",\n                                                children: tender.requirements.map((requirement, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-start gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-5 w-5 text-green-600 mt-0.5 flex-shrink-0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-700\",\n                                                                children: requirement\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 15\n                                }, this),\n                                tender.documents && tender.documents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"المستندات المرفقة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: tender.documents.map((document, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                        lineNumber: 348,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: [\n                                                                            \"مستند \",\n                                                                            index + 1\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                        lineNumber: 349,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                lineNumber: 347,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"h-4 w-4 ml-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                        lineNumber: 352,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"تحميل\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                lineNumber: 351,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                canApply && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: applyToTender,\n                                            disabled: applying,\n                                            className: \"w-full h-12 text-lg bg-green-600 hover:bg-green-700\",\n                                            children: applying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    \"جاري التقديم...\"\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"h-5 w-5 ml-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    \"تقدم للمناقصة\"\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 15\n                                }, this),\n                                hasApplied && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"border-green-200 bg-green-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"p-6 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-12 w-12 text-green-600 mx-auto mb-3\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium text-green-900 mb-2\",\n                                                children: \"تم التقديم\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-green-700\",\n                                                children: \"لقد تقدمت لهذه المناقصة بالفعل\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"معلومات المناقصة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                            className: \"h-5 w-5 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 407,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"الموعد النهائي\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 409,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: new Date(tender.deadline).toLocaleDateString(\"ar-SA\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 410,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-orange-600 font-medium\",\n                                                                    children: formatTimeRemaining(tender.deadline)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 413,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 408,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                    lineNumber: 406,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            className: \"h-5 w-5 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 422,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"الفئة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 424,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: getCategoryName(tender.category)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 425,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 423,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            className: \"h-5 w-5 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 432,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"الموقع\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 434,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: tender.location\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 435,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 433,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                    lineNumber: 439,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                            className: \"h-5 w-5 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 442,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"عدد المتقدمين\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 444,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: [\n                                                                        ((_tender_applications = tender.applications) === null || _tender_applications === void 0 ? void 0 : _tender_applications.length) || 0,\n                                                                        \" متقدم\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 445,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                    lineNumber: 441,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                            lineNumber: 405,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                    lineNumber: 401,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"الجهة المنظمة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                            lineNumber: 453,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building_Calendar_CheckCircle_Clock_Download_Edit_Eye_FileText_Heart_MapPin_Send_Share2_Tag_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                            className: \"h-5 w-5 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 458,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium\",\n                                                                    children: tender.organizer.profile.governmentEntity\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 460,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: \"جهة حكومية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 461,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 459,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 17\n                                                }, this),\n                                                tender.organizer.profile.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 467,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-gray-600\",\n                                                                    children: \"الهاتف\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 469,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: tender.organizer.profile.phone\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 470,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 468,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true),\n                                                tender.organizer.profile.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 477,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-gray-600\",\n                                                                    children: \"البريد الإلكتروني\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 479,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: tender.organizer.profile.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                                    lineNumber: 480,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                                            lineNumber: 478,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                            lineNumber: 456,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                                    lineNumber: 452,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                            lineNumber: 364,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n                    lineNumber: 275,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n            lineNumber: 239,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/tenders/[id]/page.tsx\",\n        lineNumber: 238,\n        columnNumber: 5\n    }, this);\n}\n_s(TenderDetailsPage, \"eXPyCIXXrzH+42SCOiZYrGQFOG8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = TenderDetailsPage;\nvar _c;\n$RefreshReg$(_c, \"TenderDetailsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/tenders/[id]/page.tsx\n"));

/***/ })

});