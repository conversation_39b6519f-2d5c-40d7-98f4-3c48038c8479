"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/company/dashboard/page",{

/***/ "(app-pages-browser)/./app/company/dashboard/page.tsx":
/*!****************************************!*\
  !*** ./app/company/dashboard/page.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CompanyDashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.tsx\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./components/DashboardLayout.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,DollarSign,Eye,Gavel,LineChart,PieChart,PlusCircle,RefreshCw,Target,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,DollarSign,Eye,Gavel,LineChart,PieChart,PlusCircle,RefreshCw,Target,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gavel.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,DollarSign,Eye,Gavel,LineChart,PieChart,PlusCircle,RefreshCw,Target,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,DollarSign,Eye,Gavel,LineChart,PieChart,PlusCircle,RefreshCw,Target,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,DollarSign,Eye,Gavel,LineChart,PieChart,PlusCircle,RefreshCw,Target,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,DollarSign,Eye,Gavel,LineChart,PieChart,PlusCircle,RefreshCw,Target,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/line-chart.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,DollarSign,Eye,Gavel,LineChart,PieChart,PlusCircle,RefreshCw,Target,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pie-chart.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,DollarSign,Eye,Gavel,LineChart,PieChart,PlusCircle,RefreshCw,Target,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,DollarSign,Eye,Gavel,LineChart,PieChart,PlusCircle,RefreshCw,Target,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,DollarSign,Eye,Gavel,LineChart,PieChart,PlusCircle,RefreshCw,Target,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,DollarSign,Eye,Gavel,LineChart,PieChart,PlusCircle,RefreshCw,Target,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,DollarSign,Eye,Gavel,LineChart,PieChart,PlusCircle,RefreshCw,Target,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,DollarSign,Eye,Gavel,LineChart,PieChart,PlusCircle,RefreshCw,Target,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/AreaChart.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Area.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/PieChart.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/polar/Pie.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Cell.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction CompanyDashboard() {\n    var _user_profile, _chartData_auctionPerformance;\n    _s();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [recentAuctions, setRecentAuctions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [recentBids, setRecentBids] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [chartData, setChartData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        revenueOverTime: [],\n        auctionPerformance: [],\n        categoryDistribution: [],\n        bidderActivity: []\n    });\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    // Chart colors\n    const COLORS = {\n        primary: \"#3B82F6\",\n        secondary: \"#8B5CF6\",\n        success: \"#10B981\",\n        warning: \"#F59E0B\",\n        danger: \"#EF4444\",\n        info: \"#06B6D4\"\n    };\n    const PIE_COLORS = [\n        \"#3B82F6\",\n        \"#8B5CF6\",\n        \"#10B981\",\n        \"#F59E0B\",\n        \"#EF4444\",\n        \"#06B6D4\"\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const userData = localStorage.getItem(\"user\");\n        if (userData) {\n            setUser(JSON.parse(userData));\n        }\n        loadDashboardData();\n    }, []);\n    const loadDashboardData = async ()=>{\n        try {\n            setLoading(true);\n            // Load dashboard stats\n            const statsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_8__[\"default\"].get(\"/company/dashboard/stats\");\n            setStats(statsResponse.data);\n            // Load recent auctions\n            const auctionsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_8__[\"default\"].get(\"/company/auctions?limit=5\");\n            setRecentAuctions(auctionsResponse.data.auctions || []);\n            // Load recent bids\n            const bidsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_8__[\"default\"].get(\"/company/bids/recent?limit=5\");\n            setRecentBids(bidsResponse.data.bids || []);\n            // Load notifications\n            const notificationsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_8__[\"default\"].get(\"/notifications?limit=5\");\n            setNotifications(notificationsResponse.data.notifications || []);\n            // Load chart data\n            const chartResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_8__[\"default\"].get(\"/company/dashboard/charts\");\n            setChartData(chartResponse.data);\n        } catch (error) {\n            console.error(\"Error loading dashboard data:\", error);\n            toast({\n                title: \"خطأ\",\n                description: \"حدث خطأ في تحميل بيانات لوحة التحكم\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const formatPrice = (price)=>{\n        return new Intl.NumberFormat(\"ar-SA\", {\n            style: \"currency\",\n            currency: \"SAR\",\n            minimumFractionDigits: 0\n        }).format(price);\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"ar-SA\", {\n            year: \"numeric\",\n            month: \"short\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    const getTimeAgo = (dateString)=>{\n        const now = new Date();\n        const date = new Date(dateString);\n        const diff = now.getTime() - date.getTime();\n        const minutes = Math.floor(diff / (1000 * 60));\n        const hours = Math.floor(diff / (1000 * 60 * 60));\n        const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n        if (days > 0) return \"منذ \".concat(days, \" يوم\");\n        if (hours > 0) return \"منذ \".concat(hours, \" ساعة\");\n        if (minutes > 0) return \"منذ \".concat(minutes, \" دقيقة\");\n        return \"الآن\";\n    };\n    const getAuctionStatusBadge = (status)=>{\n        switch(status){\n            case \"active\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: \"bg-green-100 text-green-800\",\n                    children: \"نشط\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                    lineNumber: 208,\n                    columnNumber: 16\n                }, this);\n            case \"completed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: \"bg-blue-100 text-blue-800\",\n                    children: \"مكتمل\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                    lineNumber: 210,\n                    columnNumber: 16\n                }, this);\n            case \"ended\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    variant: \"secondary\",\n                    children: \"منتهي\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                    lineNumber: 212,\n                    columnNumber: 16\n                }, this);\n            case \"cancelled\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    variant: \"destructive\",\n                    children: \"ملغي\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    variant: \"outline\",\n                    children: status\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-96\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"جاري تحميل بيانات الشركة...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                lineNumber: 223,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n            lineNumber: 222,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        allowedRoles: [\n            \"company\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: \"لوحة تحكم الشركة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: [\n                                        \"مرحباً بك \",\n                                        (user === null || user === void 0 ? void 0 : (_user_profile = user.profile) === null || _user_profile === void 0 ? void 0 : _user_profile.fullName) || \"في لوحة تحكم شركتك\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            onClick: loadDashboardData,\n                            variant: \"outline\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-4 h-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 13\n                                }, this),\n                                \"تحديث البيانات\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                    lineNumber: 235,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-gradient-to-r from-blue-500 to-blue-600 text-white\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-100\",\n                                                    children: \"مزاداتي\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-3xl font-bold\",\n                                                    children: (stats === null || stats === void 0 ? void 0 : stats.myAuctions) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-blue-100\",\n                                                    children: \"إجمالي المزادات\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-12 h-12 text-blue-200\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-gradient-to-r from-green-500 to-green-600 text-white\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-green-100\",\n                                                    children: \"العطاءات النشطة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-3xl font-bold\",\n                                                    children: (stats === null || stats === void 0 ? void 0 : stats.activeBids) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-green-100\",\n                                                    children: \"عطاءات جارية\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-12 h-12 text-green-200\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-gradient-to-r from-purple-500 to-purple-600 text-white\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-purple-100\",\n                                                    children: \"الإيرادات\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-3xl font-bold\",\n                                                    children: formatPrice((stats === null || stats === void 0 ? void 0 : stats.totalRevenue) || 0)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-purple-100\",\n                                                    children: \"إجمالي الإيرادات\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-12 h-12 text-purple-200\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-gradient-to-r from-orange-500 to-orange-600 text-white\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-orange-100\",\n                                                    children: \"معدل النجاح\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-3xl font-bold\",\n                                                    children: [\n                                                        (stats === null || stats === void 0 ? void 0 : stats.successRate) || 0,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-orange-100\",\n                                                    children: \"نسبة الفوز\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-12 h-12 text-orange-200\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"الإيرادات الشهرية\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"تطور الإيرادات خلال الأشهر الماضية\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-80\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_15__.ResponsiveContainer, {\n                                            width: \"100%\",\n                                            height: \"100%\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_16__.AreaChart, {\n                                                data: chartData.revenueOverTime,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_17__.CartesianGrid, {\n                                                        strokeDasharray: \"3 3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_18__.XAxis, {\n                                                        dataKey: \"month\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_19__.YAxis, {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_20__.Tooltip, {\n                                                        formatter: (value)=>[\n                                                                formatPrice(Number(value)),\n                                                                \"الإيرادات\"\n                                                            ]\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_21__.Area, {\n                                                        type: \"monotone\",\n                                                        dataKey: \"revenue\",\n                                                        stroke: COLORS.primary,\n                                                        fill: COLORS.primary,\n                                                        fillOpacity: 0.3\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"أداء المزادات\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                            lineNumber: 336,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"توزيع المزادات حسب الحالة\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-80\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_15__.ResponsiveContainer, {\n                                            width: \"100%\",\n                                            height: \"100%\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_23__.PieChart, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_24__.Pie, {\n                                                        data: chartData.auctionPerformance,\n                                                        cx: \"50%\",\n                                                        cy: \"50%\",\n                                                        labelLine: false,\n                                                        label: (param)=>{\n                                                            let { name, percent } = param;\n                                                            return \"\".concat(name, \" \").concat((percent * 100).toFixed(0), \"%\");\n                                                        },\n                                                        outerRadius: 80,\n                                                        fill: \"#8884d8\",\n                                                        dataKey: \"value\",\n                                                        children: (_chartData_auctionPerformance = chartData.auctionPerformance) === null || _chartData_auctionPerformance === void 0 ? void 0 : _chartData_auctionPerformance.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_25__.Cell, {\n                                                                fill: PIE_COLORS[index % PIE_COLORS.length]\n                                                            }, \"cell-\".concat(index), false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                                lineNumber: 357,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_20__.Tooltip, {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                        lineNumber: 360,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                    lineNumber: 342,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                            lineNumber: 334,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                    lineNumber: 302,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"المزادات الأخيرة\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"آخر المزادات التي أنشأتها\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                            lineNumber: 377,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: recentAuctions.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 383,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"لا توجد مزادات حديثة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 384,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    className: \"mt-4\",\n                                                    onClick: ()=>router.push(\"/company/create-auction\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                            lineNumber: 389,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"إنشاء مزاد جديد\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 19\n                                        }, this) : recentAuctions.map((auction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-gray-900\",\n                                                                children: auction.title\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                                lineNumber: 397,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600 mt-1\",\n                                                                children: auction.category\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                                lineNumber: 398,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-4 mt-2 text-sm text-gray-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"flex items-center gap-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                className: \"w-3 h-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                                                lineNumber: 401,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            formatPrice(auction.currentBid)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                                        lineNumber: 400,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"flex items-center gap-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                                className: \"w-3 h-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                                                lineNumber: 405,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            auction.bidsCount,\n                                                                            \" عطاء\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                                        lineNumber: 404,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"flex items-center gap-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                                className: \"w-3 h-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                                                lineNumber: 409,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            auction.viewsCount,\n                                                                            \" مشاهدة\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                                        lineNumber: 408,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                                lineNumber: 399,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                        lineNumber: 396,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            getAuctionStatusBadge(auction.status),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"ghost\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>router.push(\"/auctions/\".concat(auction._id)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                                    lineNumber: 421,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                                lineNumber: 416,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                        lineNumber: 414,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, auction._id, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                            lineNumber: 371,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 435,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"العطاءات الأخيرة\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                            lineNumber: 434,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"آخر العطاءات المقدمة\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                            lineNumber: 438,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                    lineNumber: 433,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: recentBids.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                    className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"لا توجد عطاءات حديثة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 445,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    className: \"mt-4\",\n                                                    onClick: ()=>router.push(\"/auctions\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                            lineNumber: 450,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"تصفح المزادات\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 19\n                                        }, this) : recentBids.map((bid)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-gray-900\",\n                                                                children: bid.auction.title\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                                lineNumber: 458,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600 mt-1\",\n                                                                children: [\n                                                                    \"عطاء بقيمة \",\n                                                                    formatPrice(bid.amount)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                                lineNumber: 459,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 mt-1\",\n                                                                children: getTimeAgo(bid.createdAt)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                                lineNumber: 460,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                        lineNumber: 457,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>router.push(\"/auctions/\".concat(bid.auction._id)),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                            lineNumber: 467,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, bid._id, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                    lineNumber: 440,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                            lineNumber: 432,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                    lineNumber: 369,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                            lineNumber: 481,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"إجراءات سريعة\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                    lineNumber: 480,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    children: \"الإجراءات الأكثر استخداماً\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                    lineNumber: 484,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                            lineNumber: 479,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        className: \"h-20 flex-col gap-2\",\n                                        variant: \"outline\",\n                                        onClick: ()=>router.push(\"/company/create-auction\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                className: \"w-6 h-6\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"إنشاء مزاد جديد\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                        lineNumber: 488,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        className: \"h-20 flex-col gap-2\",\n                                        variant: \"outline\",\n                                        onClick: ()=>router.push(\"/company/auctions\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-6 h-6\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"إدارة المزادات\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                        lineNumber: 496,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        className: \"h-20 flex-col gap-2\",\n                                        variant: \"outline\",\n                                        onClick: ()=>router.push(\"/company/bids\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                className: \"w-6 h-6\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                lineNumber: 509,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"متابعة العطاءات\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                        lineNumber: 504,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                lineNumber: 487,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                            lineNumber: 486,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                    lineNumber: 478,\n                    columnNumber: 9\n                }, this),\n                notifications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                        lineNumber: 521,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"الإشعارات الأخيرة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                lineNumber: 520,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                            lineNumber: 519,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    notifications.slice(0, 5).map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start gap-3 p-3 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: notification.title\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                            lineNumber: 530,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 mt-1\",\n                                                            children: notification.message\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                            lineNumber: 531,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500 mt-1\",\n                                                            children: getTimeAgo(notification.createdAt)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                            lineNumber: 532,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 529,\n                                                    columnNumber: 21\n                                                }, this),\n                                                !notification.read && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-blue-600 rounded-full mt-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 535,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, notification._id, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                            lineNumber: 528,\n                                            columnNumber: 19\n                                        }, this)),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        className: \"w-full\",\n                                        onClick: ()=>router.push(\"/company/notifications\"),\n                                        children: \"عرض جميع الإشعارات\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                        lineNumber: 539,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                lineNumber: 526,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                            lineNumber: 525,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                    lineNumber: 518,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n            lineNumber: 233,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n        lineNumber: 232,\n        columnNumber: 5\n    }, this);\n}\n_s(CompanyDashboard, \"h/++I3knGfQrKq9Q/qIbFa2sqhU=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast,\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter\n    ];\n});\n_c = CompanyDashboard;\nvar _c;\n$RefreshReg$(_c, \"CompanyDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/company/dashboard/page.tsx\n"));

/***/ })

});