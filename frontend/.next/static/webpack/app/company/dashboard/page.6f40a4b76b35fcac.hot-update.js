"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/company/dashboard/page",{

/***/ "(app-pages-browser)/./app/company/dashboard/page.tsx":
/*!****************************************!*\
  !*** ./app/company/dashboard/page.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CompanyDashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.tsx\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./components/DashboardLayout.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,DollarSign,Eye,Gavel,LineChart,PieChart,PlusCircle,RefreshCw,Target,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,DollarSign,Eye,Gavel,LineChart,PieChart,PlusCircle,RefreshCw,Target,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gavel.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,DollarSign,Eye,Gavel,LineChart,PieChart,PlusCircle,RefreshCw,Target,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,DollarSign,Eye,Gavel,LineChart,PieChart,PlusCircle,RefreshCw,Target,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,DollarSign,Eye,Gavel,LineChart,PieChart,PlusCircle,RefreshCw,Target,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,DollarSign,Eye,Gavel,LineChart,PieChart,PlusCircle,RefreshCw,Target,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/line-chart.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,DollarSign,Eye,Gavel,LineChart,PieChart,PlusCircle,RefreshCw,Target,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pie-chart.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,DollarSign,Eye,Gavel,LineChart,PieChart,PlusCircle,RefreshCw,Target,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,DollarSign,Eye,Gavel,LineChart,PieChart,PlusCircle,RefreshCw,Target,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,DollarSign,Eye,Gavel,LineChart,PieChart,PlusCircle,RefreshCw,Target,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,DollarSign,Eye,Gavel,LineChart,PieChart,PlusCircle,RefreshCw,Target,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,DollarSign,Eye,Gavel,LineChart,PieChart,PlusCircle,RefreshCw,Target,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,DollarSign,Eye,Gavel,LineChart,PieChart,PlusCircle,RefreshCw,Target,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/AreaChart.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Area.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/PieChart.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/polar/Pie.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Cell.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction CompanyDashboard() {\n    var _user_profile, _chartData_auctionPerformance;\n    _s();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [recentAuctions, setRecentAuctions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [recentBids, setRecentBids] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [chartData, setChartData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        revenueOverTime: [],\n        auctionPerformance: [],\n        categoryDistribution: [],\n        bidderActivity: []\n    });\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    // Chart colors\n    const COLORS = {\n        primary: \"#3B82F6\",\n        secondary: \"#8B5CF6\",\n        success: \"#10B981\",\n        warning: \"#F59E0B\",\n        danger: \"#EF4444\",\n        info: \"#06B6D4\"\n    };\n    const PIE_COLORS = [\n        \"#3B82F6\",\n        \"#8B5CF6\",\n        \"#10B981\",\n        \"#F59E0B\",\n        \"#EF4444\",\n        \"#06B6D4\"\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const userData = localStorage.getItem(\"user\");\n        if (userData) {\n            setUser(JSON.parse(userData));\n        }\n        loadDashboardData();\n    }, []);\n    const loadDashboardData = async ()=>{\n        try {\n            setLoading(true);\n            // Load dashboard stats\n            const statsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_8__[\"default\"].get(\"/company/dashboard/stats\");\n            setStats(statsResponse.data);\n            // Load recent auctions\n            const auctionsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_8__[\"default\"].get(\"/company/auctions?limit=5\");\n            setRecentAuctions(auctionsResponse.data.auctions || []);\n            // Load recent bids\n            const bidsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_8__[\"default\"].get(\"/company/bids/recent?limit=5\");\n            setRecentBids(bidsResponse.data.bids || []);\n            // Load notifications\n            const notificationsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_8__[\"default\"].get(\"/notifications?limit=5\");\n            setNotifications(notificationsResponse.data.notifications || []);\n            // Load chart data\n            const chartResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_8__[\"default\"].get(\"/company/dashboard/charts\");\n            setChartData(chartResponse.data);\n        } catch (error) {\n            console.error(\"Error loading dashboard data:\", error);\n            toast({\n                title: \"خطأ\",\n                description: \"حدث خطأ في تحميل بيانات لوحة التحكم\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const formatPrice = (price)=>{\n        return new Intl.NumberFormat(\"ar-SA\", {\n            style: \"currency\",\n            currency: \"SAR\",\n            minimumFractionDigits: 0\n        }).format(price);\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"ar-SA\", {\n            year: \"numeric\",\n            month: \"short\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    const getTimeAgo = (dateString)=>{\n        const now = new Date();\n        const date = new Date(dateString);\n        const diff = now.getTime() - date.getTime();\n        const minutes = Math.floor(diff / (1000 * 60));\n        const hours = Math.floor(diff / (1000 * 60 * 60));\n        const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n        if (days > 0) return \"منذ \".concat(days, \" يوم\");\n        if (hours > 0) return \"منذ \".concat(hours, \" ساعة\");\n        if (minutes > 0) return \"منذ \".concat(minutes, \" دقيقة\");\n        return \"الآن\";\n    };\n    const getAuctionStatusBadge = (status)=>{\n        switch(status){\n            case \"active\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: \"bg-green-100 text-green-800\",\n                    children: \"نشط\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                    lineNumber: 208,\n                    columnNumber: 16\n                }, this);\n            case \"completed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: \"bg-blue-100 text-blue-800\",\n                    children: \"مكتمل\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                    lineNumber: 210,\n                    columnNumber: 16\n                }, this);\n            case \"ended\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    variant: \"secondary\",\n                    children: \"منتهي\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                    lineNumber: 212,\n                    columnNumber: 16\n                }, this);\n            case \"cancelled\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    variant: \"destructive\",\n                    children: \"ملغي\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    variant: \"outline\",\n                    children: status\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-96\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"جاري تحميل بيانات الشركة...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                lineNumber: 223,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n            lineNumber: 222,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        allowedRoles: [\n            \"company\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: \"لوحة تحكم الشركة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: [\n                                        \"مرحباً بك \",\n                                        (user === null || user === void 0 ? void 0 : (_user_profile = user.profile) === null || _user_profile === void 0 ? void 0 : _user_profile.fullName) || \"في لوحة تحكم شركتك\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            onClick: loadDashboardData,\n                            variant: \"outline\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-4 h-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 13\n                                }, this),\n                                \"تحديث البيانات\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                    lineNumber: 235,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SearchWidget, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-gradient-to-r from-blue-500 to-blue-600 text-white\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-100\",\n                                                    children: \"مزاداتي\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-3xl font-bold\",\n                                                    children: (stats === null || stats === void 0 ? void 0 : stats.myAuctions) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-blue-100\",\n                                                    children: \"إجمالي المزادات\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-12 h-12 text-blue-200\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-gradient-to-r from-green-500 to-green-600 text-white\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-green-100\",\n                                                    children: \"العطاءات النشطة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-3xl font-bold\",\n                                                    children: (stats === null || stats === void 0 ? void 0 : stats.activeBids) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-green-100\",\n                                                    children: \"عطاءات جارية\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-12 h-12 text-green-200\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-gradient-to-r from-purple-500 to-purple-600 text-white\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-purple-100\",\n                                                    children: \"الإيرادات\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-3xl font-bold\",\n                                                    children: formatPrice((stats === null || stats === void 0 ? void 0 : stats.totalRevenue) || 0)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-purple-100\",\n                                                    children: \"إجمالي الإيرادات\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-12 h-12 text-purple-200\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-gradient-to-r from-orange-500 to-orange-600 text-white\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-orange-100\",\n                                                    children: \"معدل النجاح\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-3xl font-bold\",\n                                                    children: [\n                                                        (stats === null || stats === void 0 ? void 0 : stats.successRate) || 0,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-orange-100\",\n                                                    children: \"نسبة الفوز\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-12 h-12 text-orange-200\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"الإيرادات الشهرية\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"تطور الإيرادات خلال الأشهر الماضية\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-80\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_15__.ResponsiveContainer, {\n                                            width: \"100%\",\n                                            height: \"100%\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_16__.AreaChart, {\n                                                data: chartData.revenueOverTime,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_17__.CartesianGrid, {\n                                                        strokeDasharray: \"3 3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_18__.XAxis, {\n                                                        dataKey: \"month\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_19__.YAxis, {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_20__.Tooltip, {\n                                                        formatter: (value)=>[\n                                                                formatPrice(Number(value)),\n                                                                \"الإيرادات\"\n                                                            ]\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_21__.Area, {\n                                                        type: \"monotone\",\n                                                        dataKey: \"revenue\",\n                                                        stroke: COLORS.primary,\n                                                        fill: COLORS.primary,\n                                                        fillOpacity: 0.3\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                            lineNumber: 307,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"أداء المزادات\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"توزيع المزادات حسب الحالة\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-80\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_15__.ResponsiveContainer, {\n                                            width: \"100%\",\n                                            height: \"100%\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_23__.PieChart, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_24__.Pie, {\n                                                        data: chartData.auctionPerformance,\n                                                        cx: \"50%\",\n                                                        cy: \"50%\",\n                                                        labelLine: false,\n                                                        label: (param)=>{\n                                                            let { name, percent } = param;\n                                                            return \"\".concat(name, \" \").concat((percent * 100).toFixed(0), \"%\");\n                                                        },\n                                                        outerRadius: 80,\n                                                        fill: \"#8884d8\",\n                                                        dataKey: \"value\",\n                                                        children: (_chartData_auctionPerformance = chartData.auctionPerformance) === null || _chartData_auctionPerformance === void 0 ? void 0 : _chartData_auctionPerformance.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_25__.Cell, {\n                                                                fill: PIE_COLORS[index % PIE_COLORS.length]\n                                                            }, \"cell-\".concat(index), false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                                lineNumber: 360,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_20__.Tooltip, {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                            lineNumber: 337,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                    lineNumber: 305,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"المزادات الأخيرة\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                            lineNumber: 376,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"آخر المزادات التي أنشأتها\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                    lineNumber: 375,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: recentAuctions.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 386,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"لا توجد مزادات حديثة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    className: \"mt-4\",\n                                                    onClick: ()=>router.push(\"/company/create-auction\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                            lineNumber: 392,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"إنشاء مزاد جديد\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 388,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 19\n                                        }, this) : recentAuctions.map((auction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-gray-900\",\n                                                                children: auction.title\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                                lineNumber: 400,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600 mt-1\",\n                                                                children: auction.category\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                                lineNumber: 401,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-4 mt-2 text-sm text-gray-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"flex items-center gap-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                className: \"w-3 h-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                                                lineNumber: 404,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            formatPrice(auction.currentBid)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                                        lineNumber: 403,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"flex items-center gap-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                                className: \"w-3 h-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                                                lineNumber: 408,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            auction.bidsCount,\n                                                                            \" عطاء\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                                        lineNumber: 407,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"flex items-center gap-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                                className: \"w-3 h-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                                                lineNumber: 412,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            auction.viewsCount,\n                                                                            \" مشاهدة\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                                        lineNumber: 411,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                                lineNumber: 402,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                        lineNumber: 399,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            getAuctionStatusBadge(auction.status),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"ghost\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>router.push(\"/auctions/\".concat(auction._id)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                                    lineNumber: 424,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                                lineNumber: 419,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, auction._id, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                    lineNumber: 382,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                            lineNumber: 374,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 438,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"العطاءات الأخيرة\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"آخر العطاءات المقدمة\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                            lineNumber: 441,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: recentBids.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                    className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 447,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"لا توجد عطاءات حديثة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 448,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    className: \"mt-4\",\n                                                    onClick: ()=>router.push(\"/auctions\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                            lineNumber: 453,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"تصفح المزادات\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                            lineNumber: 446,\n                                            columnNumber: 19\n                                        }, this) : recentBids.map((bid)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-gray-900\",\n                                                                children: bid.auction.title\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                                lineNumber: 461,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600 mt-1\",\n                                                                children: [\n                                                                    \"عطاء بقيمة \",\n                                                                    formatPrice(bid.amount)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                                lineNumber: 462,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 mt-1\",\n                                                                children: getTimeAgo(bid.createdAt)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                                lineNumber: 463,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                        lineNumber: 460,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>router.push(\"/auctions/\".concat(bid.auction._id)),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                            lineNumber: 470,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                        lineNumber: 465,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, bid._id, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                    lineNumber: 443,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                            lineNumber: 435,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                    lineNumber: 372,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                            lineNumber: 484,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"إجراءات سريعة\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                    lineNumber: 483,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    children: \"الإجراءات الأكثر استخداماً\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                    lineNumber: 487,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                            lineNumber: 482,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        className: \"h-20 flex-col gap-2\",\n                                        variant: \"outline\",\n                                        onClick: ()=>router.push(\"/company/create-auction\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                className: \"w-6 h-6\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                lineNumber: 496,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"إنشاء مزاد جديد\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        className: \"h-20 flex-col gap-2\",\n                                        variant: \"outline\",\n                                        onClick: ()=>router.push(\"/company/auctions\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-6 h-6\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                lineNumber: 504,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"إدارة المزادات\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                        lineNumber: 499,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        className: \"h-20 flex-col gap-2\",\n                                        variant: \"outline\",\n                                        onClick: ()=>router.push(\"/company/bids\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                className: \"w-6 h-6\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                lineNumber: 512,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"متابعة العطاءات\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                        lineNumber: 507,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                lineNumber: 490,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                            lineNumber: 489,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                    lineNumber: 481,\n                    columnNumber: 9\n                }, this),\n                notifications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_DollarSign_Eye_Gavel_LineChart_PieChart_PlusCircle_RefreshCw_Target_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                        lineNumber: 524,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"الإشعارات الأخيرة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                lineNumber: 523,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                            lineNumber: 522,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    notifications.slice(0, 5).map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start gap-3 p-3 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: notification.title\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                            lineNumber: 533,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 mt-1\",\n                                                            children: notification.message\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                            lineNumber: 534,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500 mt-1\",\n                                                            children: getTimeAgo(notification.createdAt)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                            lineNumber: 535,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 532,\n                                                    columnNumber: 21\n                                                }, this),\n                                                !notification.read && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-blue-600 rounded-full mt-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                                    lineNumber: 538,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, notification._id, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                            lineNumber: 531,\n                                            columnNumber: 19\n                                        }, this)),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        className: \"w-full\",\n                                        onClick: ()=>router.push(\"/company/notifications\"),\n                                        children: \"عرض جميع الإشعارات\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                        lineNumber: 542,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                                lineNumber: 529,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                            lineNumber: 528,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n                    lineNumber: 521,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n            lineNumber: 233,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/company/dashboard/page.tsx\",\n        lineNumber: 232,\n        columnNumber: 5\n    }, this);\n}\n_s(CompanyDashboard, \"h/++I3knGfQrKq9Q/qIbFa2sqhU=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast,\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter\n    ];\n});\n_c = CompanyDashboard;\nvar _c;\n$RefreshReg$(_c, \"CompanyDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/company/dashboard/page.tsx\n"));

/***/ })

});