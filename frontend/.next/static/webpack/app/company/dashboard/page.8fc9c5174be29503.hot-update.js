"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/company/dashboard/page",{

/***/ "(app-pages-browser)/./components/Sidebar.tsx":
/*!********************************!*\
  !*** ./components/Sidebar.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Sidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,Heart,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,Heart,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,Heart,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,Heart,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gavel.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,Heart,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,Heart,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,Heart,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,Heart,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,Heart,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,Heart,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,Heart,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,Heart,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,Heart,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,Heart,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,Heart,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,Heart,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,Heart,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,Heart,LayoutDashboard,LogOut,PlusCircle,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst navItems = [\n    // Admin and Super Admin navigation\n    {\n        href: \"/admin/dashboard\",\n        label: \"لوحة التحكم\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 45,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"admin\",\n            \"super_admin\"\n        ]\n    },\n    {\n        href: \"/admin/pending-accounts\",\n        label: \"الحسابات المعلقة\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 51,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"admin\",\n            \"super_admin\"\n        ]\n    },\n    {\n        href: \"/admin/users\",\n        label: \"إدارة المستخدمين\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 57,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"admin\",\n            \"super_admin\"\n        ]\n    },\n    {\n        href: \"/admin/auctions\",\n        label: \"إدارة المزادات\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 63,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"admin\",\n            \"super_admin\"\n        ]\n    },\n    {\n        href: \"/admin/tenders\",\n        label: \"إدارة المناقصات\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 69,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"admin\",\n            \"super_admin\"\n        ]\n    },\n    {\n        href: \"/admin/create-tender\",\n        label: \"إنشاء مناقصة\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 75,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"admin\",\n            \"super_admin\"\n        ]\n    },\n    {\n        href: \"/favorites\",\n        label: \"المفضلة\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 81,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"admin\",\n            \"super_admin\"\n        ]\n    },\n    {\n        href: \"/admin/settings\",\n        label: \"الإعدادات\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 87,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"super_admin\"\n        ]\n    },\n    // Company navigation\n    {\n        href: \"/company/dashboard\",\n        label: \"لوحة التحكم\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 95,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"company\"\n        ]\n    },\n    {\n        href: \"/company/auctions\",\n        label: \"مزاداتي\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 101,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"company\"\n        ]\n    },\n    {\n        href: \"/company/tenders\",\n        label: \"مناقصاتي\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 107,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"company\"\n        ]\n    },\n    {\n        href: \"/company/create-auction\",\n        label: \"إنشاء مزاد\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 113,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"company\"\n        ]\n    },\n    {\n        href: \"/company/bids\",\n        label: \"عطاءاتي\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 119,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"company\"\n        ]\n    },\n    {\n        href: \"/company/notifications\",\n        label: \"الإشعارات\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 125,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"company\"\n        ]\n    },\n    {\n        href: \"/favorites\",\n        label: \"المفضلة\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 131,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"company\"\n        ]\n    },\n    {\n        href: \"/company/profile\",\n        label: \"الملف الشخصي\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 137,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"company\"\n        ]\n    },\n    // Individual user navigation\n    {\n        href: \"/user/dashboard\",\n        label: \"لوحة التحكم\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 145,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"individual\"\n        ]\n    },\n    {\n        href: \"/user/leaderboard\",\n        label: \"لوحة الصدارة\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 151,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"individual\"\n        ]\n    },\n    {\n        href: \"/user/auctions\",\n        label: \"المزادات المتاحة\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 157,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"individual\"\n        ]\n    },\n    {\n        href: \"/user/my-bids\",\n        label: \"مزايداتي\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 163,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"individual\"\n        ]\n    },\n    {\n        href: \"/user/notifications\",\n        label: \"الإشعارات\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 169,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"individual\"\n        ]\n    },\n    {\n        href: \"/favorites\",\n        label: \"المفضلة\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 175,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"individual\"\n        ]\n    },\n    {\n        href: \"/user/profile\",\n        label: \"الملف الشخصي\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 181,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"individual\"\n        ]\n    },\n    // Government navigation\n    {\n        href: \"/government/dashboard\",\n        label: \"لوحة التحكم\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 189,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"government\"\n        ]\n    },\n    {\n        href: \"/government/tenders\",\n        label: \"مناقصاتي\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 195,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"government\"\n        ]\n    },\n    {\n        href: \"/government/applications\",\n        label: \"طلبات المشاركة\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 201,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"government\"\n        ]\n    },\n    {\n        href: \"/government/notifications\",\n        label: \"الإشعارات\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 207,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"government\"\n        ]\n    },\n    {\n        href: \"/favorites\",\n        label: \"المفضلة\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 213,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"government\"\n        ]\n    },\n    {\n        href: \"/government/profile\",\n        label: \"الملف الشخصي\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 219,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"government\"\n        ]\n    }\n];\nfunction Sidebar(param) {\n    let { userRole } = param;\n    var _user_profile, _user_profile1, _user_profile2;\n    _s();\n    const [isCollapsed, setIsCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const userData = localStorage.getItem(\"user\");\n        if (userData) {\n            setUser(JSON.parse(userData));\n        }\n    }, []);\n    const handleLogout = ()=>{\n        localStorage.removeItem(\"token\");\n        localStorage.removeItem(\"user\");\n        router.push(\"/auth/login\");\n    };\n    const roleIcon = ()=>{\n        switch(user === null || user === void 0 ? void 0 : user.role){\n            case \"super_admin\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                    className: \"h-6 w-6 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 16\n                }, this);\n            case \"admin\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-6 w-6 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                    lineNumber: 248,\n                    columnNumber: 16\n                }, this);\n            case \"company\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    className: \"h-6 w-6 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 16\n                }, this);\n            case \"government\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-6 w-6 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                    lineNumber: 252,\n                    columnNumber: 16\n                }, this);\n            case \"individual\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                    className: \"h-6 w-6 text-purple-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                    className: \"h-6 w-6\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                    lineNumber: 256,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getRoleLabel = (role)=>{\n        switch(role){\n            case \"super_admin\":\n                return \"مدير عام\";\n            case \"admin\":\n                return \"مدير\";\n            case \"company\":\n                return \"شركة\";\n            case \"government\":\n                return \"جهة حكومية\";\n            case \"individual\":\n                return \"فرد\";\n            default:\n                return role;\n        }\n    };\n    const filteredNavItems = navItems.filter((item)=>item.roles.includes((user === null || user === void 0 ? void 0 : user.role) || \"\"));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(isCollapsed ? \"w-16\" : \"w-56\", \" transition-all duration-300 backdrop-blur-xl bg-white/90 border-r border-white/20 shadow-xl flex flex-col h-screen relative z-10\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-gray-200/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: \"/\",\n                            className: \"flex items-center gap-3 group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 rounded-xl flex items-center justify-center group-hover:scale-110 group-hover:rotate-3 transition-all duration-300 shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"w-4 h-4 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-base font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent\",\n                                            children: \"المنصة\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-600 font-medium\",\n                                            children: \"المزادات والمناقصات\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 13\n                        }, this),\n                        isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 rounded-xl flex items-center justify-center shadow-lg mx-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                className: \"w-4 h-4 text-white\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            onClick: ()=>setIsCollapsed(!isCollapsed),\n                            className: \"p-1.5 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors duration-200\",\n                            children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                className: \"h-3 w-3\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 28\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                className: \"h-3 w-3\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 66\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                            lineNumber: 302,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                    lineNumber: 285,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                lineNumber: 284,\n                columnNumber: 7\n            }, this),\n            user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-4 border-b border-gray-200/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center \".concat(isCollapsed ? \"justify-center\" : \"gap-3\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 rounded-xl flex items-center justify-center shadow-lg\",\n                            children: roleIcon()\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 13\n                        }, this),\n                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-bold text-gray-900 truncate\",\n                                    children: ((_user_profile = user.profile) === null || _user_profile === void 0 ? void 0 : _user_profile.fullName) || ((_user_profile1 = user.profile) === null || _user_profile1 === void 0 ? void 0 : _user_profile1.companyName) || ((_user_profile2 = user.profile) === null || _user_profile2 === void 0 ? void 0 : _user_profile2.governmentEntity) || \"المدير\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-600 font-medium truncate\",\n                                    children: getRoleLabel(user.role)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-1.5 mt-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: \"متصل\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                    lineNumber: 316,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                lineNumber: 315,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex-1 px-4 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: filteredNavItems.map((item)=>{\n                        const isActive = pathname === item.href;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: item.href,\n                            className: \"group flex items-center \".concat(isCollapsed ? \"justify-center px-2 py-3\" : \"gap-3 px-4 py-3\", \" rounded-xl transition-all duration-300 relative overflow-hidden \").concat(isActive ? \"bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-indigo-500/10 border border-blue-300/30 shadow-md backdrop-blur-sm\" : \"hover:bg-white/60 hover:shadow-md hover:scale-[1.02] border border-transparent\"),\n                            children: [\n                                isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"\".concat(isCollapsed ? \"w-6 h-6\" : \"w-8 h-8\", \" rounded-lg flex items-center justify-center transition-all duration-300 relative z-10 \").concat(isActive ? \"bg-gradient-to-br from-blue-500 to-purple-600 shadow-lg scale-105\" : \"bg-gray-100 group-hover:bg-gradient-to-br group-hover:from-gray-200 group-hover:to-gray-300\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"transition-all duration-300 \".concat(isActive ? \"text-white scale-105\" : \"text-gray-600 group-hover:text-gray-700\"),\n                                        children: item.icon\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 17\n                                }, this),\n                                !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-semibold transition-all duration-300 \".concat(isActive ? \"text-gray-900\" : \"text-gray-700 group-hover:text-gray-900\"),\n                                            children: item.label\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 21\n                                        }, this),\n                                        isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-1.5 h-1.5 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mt-0.5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                    lineNumber: 371,\n                                    columnNumber: 19\n                                }, this),\n                                isActive && !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                    className: \"w-4 h-4 text-blue-600 mr-auto relative z-10\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, item.href, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                            lineNumber: 344,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                    lineNumber: 340,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                lineNumber: 339,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 pb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-gray-200/50 pt-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        onClick: handleLogout,\n                        variant: \"ghost\",\n                        className: \"w-full group flex items-center \".concat(isCollapsed ? \"justify-center px-2 py-3\" : \"gap-3 px-4 py-3\", \" rounded-xl hover:bg-red-50/80 hover:shadow-md transition-all duration-300 border border-transparent hover:border-red-200/50\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat(isCollapsed ? \"w-6 h-6\" : \"w-8 h-8\", \" rounded-lg bg-red-100 group-hover:bg-gradient-to-br group-hover:from-red-500 group-hover:to-pink-500 flex items-center justify-center transition-all duration-300\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    className: \"\".concat(isCollapsed ? \"w-3 h-3\" : \"w-4 h-4\", \" text-red-600 group-hover:text-white transition-all duration-300\")\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                    lineNumber: 403,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 13\n                            }, this),\n                            !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-semibold text-red-700 group-hover:text-red-800 transition-all duration-300\",\n                                children: \"تسجيل الخروج\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                        lineNumber: 397,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                    lineNumber: 396,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                lineNumber: 395,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n        lineNumber: 282,\n        columnNumber: 5\n    }, this);\n}\n_s(Sidebar, \"8D+h9wKtrXb9BO+L1pAtHWiT9Do=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/Sidebar.tsx\n"));

/***/ })

});