"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/auctions/page",{

/***/ "(app-pages-browser)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   activityAPI: function() { return /* binding */ activityAPI; },\n/* harmony export */   adminAPI: function() { return /* binding */ adminAPI; },\n/* harmony export */   auctionAPI: function() { return /* binding */ auctionAPI; },\n/* harmony export */   authAPI: function() { return /* binding */ authAPI; },\n/* harmony export */   companyAPI: function() { return /* binding */ companyAPI; },\n/* harmony export */   favoritesAPI: function() { return /* binding */ favoritesAPI; },\n/* harmony export */   governmentAPI: function() { return /* binding */ governmentAPI; },\n/* harmony export */   leaderboardAPI: function() { return /* binding */ leaderboardAPI; },\n/* harmony export */   messagesAPI: function() { return /* binding */ messagesAPI; },\n/* harmony export */   tenderAPI: function() { return /* binding */ tenderAPI; },\n/* harmony export */   userAPI: function() { return /* binding */ userAPI; }\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n\nconst API_BASE_URL = \"http://localhost:5000/api\" || 0;\nconst api = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    headers: {\n        \"Content-Type\": \"application/json\"\n    },\n    withCredentials: true\n});\n// Request interceptor to add auth token\napi.interceptors.request.use((config)=>{\n    const token = localStorage.getItem(\"token\");\n    if (token) {\n        config.headers.Authorization = \"Bearer \".concat(token);\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Response interceptor to handle errors and token refresh\napi.interceptors.response.use((response)=>response, async (error)=>{\n    var _error_response, _error_response1;\n    const originalRequest = error.config;\n    if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401 && !originalRequest._retry) {\n        originalRequest._retry = true;\n        try {\n            const refreshToken = localStorage.getItem(\"refreshToken\");\n            if (refreshToken) {\n                // Try to refresh the token\n                const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"\".concat(API_BASE_URL, \"/auth/refresh\"), {\n                    refreshToken\n                });\n                const { accessToken, refreshToken: newRefreshToken } = response.data.data;\n                // Update stored tokens\n                localStorage.setItem(\"token\", accessToken);\n                localStorage.setItem(\"refreshToken\", newRefreshToken);\n                // Update the authorization header and retry the original request\n                originalRequest.headers.Authorization = \"Bearer \".concat(accessToken);\n                return api(originalRequest);\n            }\n        } catch (refreshError) {\n            // Refresh failed, redirect to login\n            localStorage.removeItem(\"token\");\n            localStorage.removeItem(\"refreshToken\");\n            localStorage.removeItem(\"user\");\n            window.location.href = \"/auth/login\";\n            return Promise.reject(refreshError);\n        }\n    }\n    // If not a 401 or refresh failed, just redirect to login for 401s\n    if (((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) === 401) {\n        localStorage.removeItem(\"token\");\n        localStorage.removeItem(\"refreshToken\");\n        localStorage.removeItem(\"user\");\n        window.location.href = \"/auth/login\";\n    }\n    return Promise.reject(error);\n});\nconst authAPI = {\n    register: (data)=>api.post(\"/auth/register\", data),\n    login: (data)=>api.post(\"/auth/login\", data),\n    logout: ()=>api.post(\"/auth/logout\"),\n    verifyEmail: (token)=>api.post(\"/auth/verify-email\", {\n            token\n        }),\n    resendVerification: (email)=>api.post(\"/auth/resend-verification\", {\n            email\n        }),\n    forgotPassword: (email)=>api.post(\"/auth/forgot-password\", {\n            email\n        }),\n    resetPassword: (data)=>api.post(\"/auth/reset-password\", data)\n};\nconst userAPI = {\n    getProfile: ()=>api.get(\"/users/profile\"),\n    updateProfile: (data)=>api.put(\"/users/profile\", data),\n    uploadDocuments: (data)=>api.post(\"/users/documents\", data, {\n            headers: {\n                \"Content-Type\": \"multipart/form-data\"\n            }\n        })\n};\nconst auctionAPI = {\n    getAll: ()=>api.get(\"/auctions\"),\n    getById: (id)=>api.get(\"/auctions/\".concat(id)),\n    create: (data)=>api.post(\"/auctions\", data),\n    update: (id, data)=>api.put(\"/auctions/\".concat(id), data),\n    delete: (id)=>api.delete(\"/auctions/\".concat(id)),\n    placeBid: (id, amount)=>api.post(\"/auctions/\".concat(id, \"/bid\"), {\n            amount\n        })\n};\nconst tenderAPI = {\n    getAll: ()=>api.get(\"/tenders\"),\n    getById: (id)=>api.get(\"/tenders/\".concat(id)),\n    create: (data)=>api.post(\"/tenders\", data),\n    update: (id, data)=>api.put(\"/tenders/\".concat(id), data),\n    delete: (id)=>api.delete(\"/tenders/\".concat(id)),\n    submitProposal: (id, data)=>api.post(\"/tenders/\".concat(id, \"/proposal\"), data)\n};\n// Favorites/Watchlist API\nconst favoritesAPI = {\n    // Get user's favorites\n    getFavorites: (params)=>api.get(\"/favorites\", {\n            params\n        }),\n    // Add item to favorites\n    addFavorite: (data)=>api.post(\"/favorites\", data),\n    // Remove item from favorites\n    removeFavorite: (itemType, itemId)=>api.delete(\"/favorites/\".concat(itemType, \"/\").concat(itemId)),\n    // Update favorite settings\n    updateFavorite: (itemType, itemId, data)=>api.put(\"/favorites/\".concat(itemType, \"/\").concat(itemId), data),\n    // Check if item is favorited\n    checkFavorite: (itemType, itemId)=>api.get(\"/favorites/check/\".concat(itemType, \"/\").concat(itemId))\n};\n// Activity Logs API\nconst activityAPI = {\n    // Get user's activity logs\n    getUserActivities: (params)=>api.get(\"/activity/user\", {\n            params\n        }),\n    // Get admin activity logs (admin only)\n    getAdminActivities: (params)=>api.get(\"/activity/admin\", {\n            params\n        }),\n    // Get activity statistics\n    getActivityStats: (params)=>api.get(\"/activity/stats\", {\n            params\n        })\n};\n// Messaging API\nconst messagesAPI = {\n    // Conversation management\n    conversations: {\n        // Get user's conversations\n        getAll: (params)=>api.get(\"/messages/conversations\", {\n                params\n            }),\n        // Get conversation by ID\n        getById: (conversationId)=>api.get(\"/messages/conversations/\".concat(conversationId)),\n        // Create new conversation\n        create: (data)=>api.post(\"/messages/conversations\", data),\n        // Update conversation\n        update: (conversationId, data)=>api.put(\"/messages/conversations/\".concat(conversationId), data),\n        // Add participants\n        addParticipants: (conversationId, userIds)=>api.post(\"/messages/conversations/\".concat(conversationId, \"/participants\"), {\n                userIds\n            }),\n        // Remove participants\n        removeParticipants: (conversationId, userIds)=>api.delete(\"/messages/conversations/\".concat(conversationId, \"/participants\"), {\n                data: {\n                    userIds\n                }\n            }),\n        // Archive/unarchive conversation\n        archive: (conversationId)=>api.post(\"/messages/conversations/\".concat(conversationId, \"/archive\")),\n        unarchive: (conversationId)=>api.post(\"/messages/conversations/\".concat(conversationId, \"/unarchive\"))\n    },\n    // Message management\n    messages: {\n        // Get messages in conversation\n        getByConversation: (conversationId, params)=>api.get(\"/messages/conversations/\".concat(conversationId, \"/messages\"), {\n                params\n            }),\n        // Send message\n        send: (conversationId, data)=>api.post(\"/messages/conversations/\".concat(conversationId, \"/messages\"), data),\n        // Edit message\n        edit: (conversationId, messageId, data)=>api.put(\"/messages/conversations/\".concat(conversationId, \"/messages/\").concat(messageId), data),\n        // Delete message\n        delete: (conversationId, messageId)=>api.delete(\"/messages/conversations/\".concat(conversationId, \"/messages/\").concat(messageId)),\n        // Mark messages as read\n        markAsRead: (conversationId, messageIds)=>api.post(\"/messages/conversations/\".concat(conversationId, \"/read\"), {\n                messageIds\n            }),\n        // Add reaction to message\n        react: (conversationId, messageId, emoji)=>api.post(\"/messages/conversations/\".concat(conversationId, \"/messages/\").concat(messageId, \"/react\"), {\n                emoji\n            }),\n        // Remove reaction from message\n        unreact: (conversationId, messageId, emoji)=>api.delete(\"/messages/conversations/\".concat(conversationId, \"/messages/\").concat(messageId, \"/react\"), {\n                data: {\n                    emoji\n                }\n            })\n    },\n    // Search messages\n    search: (params)=>api.get(\"/messages/search\", {\n            params\n        })\n};\n// Admin API\nconst adminAPI = {\n    // Dashboard statistics\n    getDashboardStats: ()=>api.get(\"/admin/dashboard\"),\n    // Pending accounts\n    getPendingAccounts: ()=>api.get(\"/admin/pending-accounts\"),\n    approvePendingAccount: (accountId)=>api.post(\"/admin/pending-accounts/\".concat(accountId, \"/approve\")),\n    rejectPendingAccount: (accountId, reason)=>api.post(\"/admin/pending-accounts/\".concat(accountId, \"/reject\"), {\n            reason\n        }),\n    // User management\n    users: {\n        getAll: (params)=>api.get(\"/admin/users\", {\n                params\n            }),\n        getById: (userId)=>api.get(\"/admin/users/\".concat(userId)),\n        update: (userId, data)=>api.put(\"/admin/users/\".concat(userId), data),\n        delete: (userId)=>api.delete(\"/admin/users/\".concat(userId)),\n        activate: (userId)=>api.post(\"/admin/users/\".concat(userId, \"/activate\")),\n        deactivate: (userId)=>api.post(\"/admin/users/\".concat(userId, \"/deactivate\"))\n    },\n    // Auction management\n    auctions: {\n        getAll: (params)=>api.get(\"/admin/auctions\", {\n                params\n            }),\n        getById: (auctionId)=>api.get(\"/admin/auctions/\".concat(auctionId)),\n        approve: (auctionId)=>api.post(\"/admin/auctions/\".concat(auctionId, \"/approve\")),\n        reject: (auctionId, reason)=>api.post(\"/admin/auctions/\".concat(auctionId, \"/reject\"), {\n                reason\n            }),\n        suspend: (auctionId, reason)=>api.post(\"/admin/auctions/\".concat(auctionId, \"/suspend\"), {\n                reason\n            }),\n        delete: (auctionId)=>api.delete(\"/admin/auctions/\".concat(auctionId))\n    },\n    // Tender management\n    tenders: {\n        getAll: (params)=>api.get(\"/admin/tenders\", {\n                params\n            }),\n        getById: (tenderId)=>api.get(\"/admin/tenders/\".concat(tenderId)),\n        approve: (tenderId)=>api.post(\"/admin/tenders/\".concat(tenderId, \"/approve\")),\n        reject: (tenderId, reason)=>api.post(\"/admin/tenders/\".concat(tenderId, \"/reject\"), {\n                reason\n            }),\n        suspend: (tenderId, reason)=>api.post(\"/admin/tenders/\".concat(tenderId, \"/suspend\"), {\n                reason\n            }),\n        delete: (tenderId)=>api.delete(\"/admin/tenders/\".concat(tenderId))\n    },\n    // Individual tender methods (for backward compatibility)\n    getTender: (tenderId)=>api.get(\"/admin/tenders/\".concat(tenderId)),\n    getTenderSubmissions: (tenderId, params)=>api.get(\"/admin/tenders/\".concat(tenderId, \"/submissions\"), {\n            params\n        }),\n    updateTenderStatus: (tenderId, data)=>api.put(\"/admin/tenders/\".concat(tenderId, \"/status\"), data),\n    updateTenderSubmissionStatus: (tenderId, submissionId, data)=>api.put(\"/admin/tenders/\".concat(tenderId, \"/submissions/\").concat(submissionId, \"/status\"), data),\n    // Reports and analytics\n    reports: {\n        getFinancialReport: (params)=>api.get(\"/admin/reports/financial\", {\n                params\n            }),\n        getUserReport: (params)=>api.get(\"/admin/reports/users\", {\n                params\n            }),\n        getActivityReport: (params)=>api.get(\"/admin/reports/activity\", {\n                params\n            }),\n        getAuctionReport: (params)=>api.get(\"/admin/reports/auctions\", {\n                params\n            }),\n        getTenderReport: (params)=>api.get(\"/admin/reports/tenders\", {\n                params\n            })\n    },\n    // System settings\n    settings: {\n        getAll: ()=>api.get(\"/admin/settings\"),\n        update: (data)=>api.put(\"/admin/settings\", data),\n        backup: ()=>api.post(\"/admin/settings/backup\"),\n        restore: (backupId)=>api.post(\"/admin/settings/restore/\".concat(backupId))\n    }\n};\n// Government API\nconst governmentAPI = {\n    // Tender management\n    tenders: {\n        getAll: (params)=>api.get(\"/government/tenders\", {\n                params\n            }),\n        getById: (tenderId)=>api.get(\"/tenders/\".concat(tenderId)),\n        create: (data)=>api.post(\"/government/tenders\", data),\n        update: (tenderId, data)=>api.put(\"/tenders/\".concat(tenderId), data),\n        delete: (tenderId)=>api.delete(\"/tenders/\".concat(tenderId)),\n        publish: (tenderId)=>api.post(\"/government/tenders/\".concat(tenderId, \"/publish\")),\n        close: (tenderId)=>api.post(\"/government/tenders/\".concat(tenderId, \"/close\")),\n        cancel: (tenderId, reason)=>api.post(\"/government/tenders/\".concat(tenderId, \"/cancel\"), {\n                reason\n            })\n    },\n    // Proposal management\n    proposals: {\n        getByTender: (tenderId, params)=>api.get(\"/government/tenders/\".concat(tenderId, \"/proposals\"), {\n                params\n            }),\n        getById: (tenderId, proposalId)=>api.get(\"/government/tenders/\".concat(tenderId, \"/proposals/\").concat(proposalId)),\n        evaluate: (tenderId, proposalId, data)=>api.post(\"/government/tenders/\".concat(tenderId, \"/proposals/\").concat(proposalId, \"/evaluate\"), data),\n        shortlist: (tenderId, proposalId)=>api.post(\"/government/tenders/\".concat(tenderId, \"/proposals/\").concat(proposalId, \"/shortlist\")),\n        award: (tenderId, proposalId, data)=>api.post(\"/government/tenders/\".concat(tenderId, \"/proposals/\").concat(proposalId, \"/award\"), data),\n        reject: (tenderId, proposalId, reason)=>api.post(\"/government/tenders/\".concat(tenderId, \"/proposals/\").concat(proposalId, \"/reject\"), {\n                reason\n            })\n    },\n    // Contract management\n    contracts: {\n        getAll: (params)=>api.get(\"/government/contracts\", {\n                params\n            }),\n        getById: (contractId)=>api.get(\"/government/contracts/\".concat(contractId)),\n        create: (data)=>api.post(\"/government/contracts\", data),\n        update: (contractId, data)=>api.put(\"/government/contracts/\".concat(contractId), data),\n        approve: (contractId)=>api.post(\"/government/contracts/\".concat(contractId, \"/approve\")),\n        terminate: (contractId, reason)=>api.post(\"/government/contracts/\".concat(contractId, \"/terminate\"), {\n                reason\n            })\n    },\n    // Reports and analytics\n    reports: {\n        getDashboard: ()=>api.get(\"/government/reports/dashboard\"),\n        getTenderReport: (params)=>api.get(\"/government/reports/tenders\", {\n                params\n            }),\n        getContractReport: (params)=>api.get(\"/government/reports/contracts\", {\n                params\n            }),\n        getVendorReport: (params)=>api.get(\"/government/reports/vendors\", {\n                params\n            })\n    }\n};\n// Leaderboard API\nconst leaderboardAPI = {\n    getLeaderboard: (params)=>api.get(\"/leaderboard\", {\n            params\n        }),\n    getUserRank: (userId)=>api.get(\"/leaderboard/rank\".concat(userId ? \"/\".concat(userId) : \"\")),\n    getTopBidders: (params)=>api.get(\"/leaderboard/bidders\", {\n            params\n        }),\n    getTopSellers: (params)=>api.get(\"/leaderboard/sellers\", {\n            params\n        })\n};\n// Company API\nconst companyAPI = {\n    getProfile: ()=>api.get(\"/company/profile\"),\n    updateProfile: (data)=>api.put(\"/company/profile\", data),\n    getEmployees: (params)=>api.get(\"/company/employees\", {\n            params\n        }),\n    addEmployee: (data)=>api.post(\"/company/employees\", data),\n    updateEmployee: (employeeId, data)=>api.put(\"/company/employees/\".concat(employeeId), data),\n    removeEmployee: (employeeId)=>api.delete(\"/company/employees/\".concat(employeeId)),\n    getAuctions: (params)=>api.get(\"/company/auctions\", {\n            params\n        }),\n    getTenders: (params)=>api.get(\"/company/tenders\", {\n            params\n        }),\n    getContracts: (params)=>api.get(\"/company/contracts\", {\n            params\n        })\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (api);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api.ts\n"));

/***/ })

});