"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/messages/page",{

/***/ "(app-pages-browser)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   activityAPI: function() { return /* binding */ activityAPI; },\n/* harmony export */   adminAPI: function() { return /* binding */ adminAPI; },\n/* harmony export */   auctionAPI: function() { return /* binding */ auctionAPI; },\n/* harmony export */   authAPI: function() { return /* binding */ authAPI; },\n/* harmony export */   companyAPI: function() { return /* binding */ companyAPI; },\n/* harmony export */   favoritesAPI: function() { return /* binding */ favoritesAPI; },\n/* harmony export */   governmentAPI: function() { return /* binding */ governmentAPI; },\n/* harmony export */   leaderboardAPI: function() { return /* binding */ leaderboardAPI; },\n/* harmony export */   messagesAPI: function() { return /* binding */ messagesAPI; },\n/* harmony export */   tenderAPI: function() { return /* binding */ tenderAPI; },\n/* harmony export */   userAPI: function() { return /* binding */ userAPI; }\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n\nconst API_BASE_URL = \"http://localhost:5000/api\" || 0;\nconst api = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    headers: {\n        \"Content-Type\": \"application/json\"\n    },\n    withCredentials: true\n});\n// Request interceptor to add auth token\napi.interceptors.request.use((config)=>{\n    const token = localStorage.getItem(\"token\");\n    if (token) {\n        config.headers.Authorization = \"Bearer \".concat(token);\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Response interceptor to handle errors\napi.interceptors.response.use((response)=>response, (error)=>{\n    var _error_response;\n    if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n        localStorage.removeItem(\"token\");\n        window.location.href = \"/auth/login\";\n    }\n    return Promise.reject(error);\n});\nconst authAPI = {\n    register: (data)=>api.post(\"/auth/register\", data),\n    login: (data)=>api.post(\"/auth/login\", data),\n    logout: ()=>api.post(\"/auth/logout\"),\n    verifyEmail: (token)=>api.post(\"/auth/verify-email\", {\n            token\n        }),\n    resendVerification: (email)=>api.post(\"/auth/resend-verification\", {\n            email\n        }),\n    forgotPassword: (email)=>api.post(\"/auth/forgot-password\", {\n            email\n        }),\n    resetPassword: (data)=>api.post(\"/auth/reset-password\", data)\n};\nconst userAPI = {\n    getProfile: ()=>api.get(\"/users/profile\"),\n    updateProfile: (data)=>api.put(\"/users/profile\", data),\n    uploadDocuments: (data)=>api.post(\"/users/documents\", data, {\n            headers: {\n                \"Content-Type\": \"multipart/form-data\"\n            }\n        })\n};\nconst auctionAPI = {\n    getAll: ()=>api.get(\"/auctions\"),\n    getById: (id)=>api.get(\"/auctions/\".concat(id)),\n    create: (data)=>api.post(\"/auctions\", data),\n    update: (id, data)=>api.put(\"/auctions/\".concat(id), data),\n    delete: (id)=>api.delete(\"/auctions/\".concat(id)),\n    placeBid: (id, amount)=>api.post(\"/auctions/\".concat(id, \"/bid\"), {\n            amount\n        })\n};\nconst tenderAPI = {\n    getAll: ()=>api.get(\"/tenders\"),\n    getById: (id)=>api.get(\"/tenders/\".concat(id)),\n    create: (data)=>api.post(\"/tenders\", data),\n    update: (id, data)=>api.put(\"/tenders/\".concat(id), data),\n    delete: (id)=>api.delete(\"/tenders/\".concat(id)),\n    submitProposal: (id, data)=>api.post(\"/tenders/\".concat(id, \"/proposal\"), data)\n};\n// Favorites/Watchlist API\nconst favoritesAPI = {\n    // Get user's favorites\n    getFavorites: (params)=>api.get(\"/favorites\", {\n            params\n        }),\n    // Add item to favorites\n    addFavorite: (data)=>api.post(\"/favorites\", data),\n    // Remove item from favorites\n    removeFavorite: (itemType, itemId)=>api.delete(\"/favorites/\".concat(itemType, \"/\").concat(itemId)),\n    // Update favorite settings\n    updateFavorite: (itemType, itemId, data)=>api.put(\"/favorites/\".concat(itemType, \"/\").concat(itemId), data),\n    // Check if item is favorited\n    checkFavorite: (itemType, itemId)=>api.get(\"/favorites/check/\".concat(itemType, \"/\").concat(itemId))\n};\n// Activity Logs API\nconst activityAPI = {\n    // Get user's activity logs\n    getUserActivities: (params)=>api.get(\"/activity/user\", {\n            params\n        }),\n    // Get admin activity logs (admin only)\n    getAdminActivities: (params)=>api.get(\"/activity/admin\", {\n            params\n        }),\n    // Get activity statistics\n    getActivityStats: (params)=>api.get(\"/activity/stats\", {\n            params\n        })\n};\n// Messaging API\nconst messagesAPI = {\n    // Conversation management\n    conversations: {\n        // Get user's conversations\n        getAll: (params)=>api.get(\"/messages/conversations\", {\n                params\n            }),\n        // Get conversation by ID\n        getById: (conversationId)=>api.get(\"/messages/conversations/\".concat(conversationId)),\n        // Create new conversation\n        create: (data)=>api.post(\"/messages/conversations\", data),\n        // Update conversation\n        update: (conversationId, data)=>api.put(\"/messages/conversations/\".concat(conversationId), data),\n        // Add participants\n        addParticipants: (conversationId, userIds)=>api.post(\"/messages/conversations/\".concat(conversationId, \"/participants\"), {\n                userIds\n            }),\n        // Remove participants\n        removeParticipants: (conversationId, userIds)=>api.delete(\"/messages/conversations/\".concat(conversationId, \"/participants\"), {\n                data: {\n                    userIds\n                }\n            }),\n        // Archive/unarchive conversation\n        archive: (conversationId)=>api.post(\"/messages/conversations/\".concat(conversationId, \"/archive\")),\n        unarchive: (conversationId)=>api.post(\"/messages/conversations/\".concat(conversationId, \"/unarchive\"))\n    },\n    // Message management\n    messages: {\n        // Get messages in conversation\n        getByConversation: (conversationId, params)=>api.get(\"/messages/conversations/\".concat(conversationId, \"/messages\"), {\n                params\n            }),\n        // Send message\n        send: (conversationId, data)=>api.post(\"/messages/conversations/\".concat(conversationId, \"/messages\"), data),\n        // Edit message\n        edit: (conversationId, messageId, data)=>api.put(\"/messages/conversations/\".concat(conversationId, \"/messages/\").concat(messageId), data),\n        // Delete message\n        delete: (conversationId, messageId)=>api.delete(\"/messages/conversations/\".concat(conversationId, \"/messages/\").concat(messageId)),\n        // Mark messages as read\n        markAsRead: (conversationId, messageIds)=>api.post(\"/messages/conversations/\".concat(conversationId, \"/read\"), {\n                messageIds\n            }),\n        // Add reaction to message\n        react: (conversationId, messageId, emoji)=>api.post(\"/messages/conversations/\".concat(conversationId, \"/messages/\").concat(messageId, \"/react\"), {\n                emoji\n            }),\n        // Remove reaction from message\n        unreact: (conversationId, messageId, emoji)=>api.delete(\"/messages/conversations/\".concat(conversationId, \"/messages/\").concat(messageId, \"/react\"), {\n                data: {\n                    emoji\n                }\n            })\n    },\n    // Search messages\n    search: (params)=>api.get(\"/messages/search\", {\n            params\n        })\n};\n// Admin API\nconst adminAPI = {\n    // Dashboard statistics\n    getDashboardStats: ()=>api.get(\"/admin/dashboard\"),\n    // Pending accounts\n    getPendingAccounts: ()=>api.get(\"/admin/pending-accounts\"),\n    approvePendingAccount: (accountId)=>api.post(\"/admin/pending-accounts/\".concat(accountId, \"/approve\")),\n    rejectPendingAccount: (accountId, reason)=>api.post(\"/admin/pending-accounts/\".concat(accountId, \"/reject\"), {\n            reason\n        }),\n    // User management\n    users: {\n        getAll: (params)=>api.get(\"/admin/users\", {\n                params\n            }),\n        getById: (userId)=>api.get(\"/admin/users/\".concat(userId)),\n        update: (userId, data)=>api.put(\"/admin/users/\".concat(userId), data),\n        delete: (userId)=>api.delete(\"/admin/users/\".concat(userId)),\n        activate: (userId)=>api.post(\"/admin/users/\".concat(userId, \"/activate\")),\n        deactivate: (userId)=>api.post(\"/admin/users/\".concat(userId, \"/deactivate\"))\n    },\n    // Auction management\n    auctions: {\n        getAll: (params)=>api.get(\"/admin/auctions\", {\n                params\n            }),\n        getById: (auctionId)=>api.get(\"/admin/auctions/\".concat(auctionId)),\n        approve: (auctionId)=>api.post(\"/admin/auctions/\".concat(auctionId, \"/approve\")),\n        reject: (auctionId, reason)=>api.post(\"/admin/auctions/\".concat(auctionId, \"/reject\"), {\n                reason\n            }),\n        suspend: (auctionId, reason)=>api.post(\"/admin/auctions/\".concat(auctionId, \"/suspend\"), {\n                reason\n            }),\n        delete: (auctionId)=>api.delete(\"/admin/auctions/\".concat(auctionId))\n    },\n    // Tender management\n    tenders: {\n        getAll: (params)=>api.get(\"/admin/tenders\", {\n                params\n            }),\n        getById: (tenderId)=>api.get(\"/admin/tenders/\".concat(tenderId)),\n        approve: (tenderId)=>api.post(\"/admin/tenders/\".concat(tenderId, \"/approve\")),\n        reject: (tenderId, reason)=>api.post(\"/admin/tenders/\".concat(tenderId, \"/reject\"), {\n                reason\n            }),\n        suspend: (tenderId, reason)=>api.post(\"/admin/tenders/\".concat(tenderId, \"/suspend\"), {\n                reason\n            }),\n        delete: (tenderId)=>api.delete(\"/admin/tenders/\".concat(tenderId))\n    },\n    // Individual tender methods (for backward compatibility)\n    getTender: (tenderId)=>api.get(\"/admin/tenders/\".concat(tenderId)),\n    getTenderSubmissions: (tenderId, params)=>api.get(\"/admin/tenders/\".concat(tenderId, \"/submissions\"), {\n            params\n        }),\n    updateTenderStatus: (tenderId, data)=>api.put(\"/admin/tenders/\".concat(tenderId, \"/status\"), data),\n    updateTenderSubmissionStatus: (tenderId, submissionId, data)=>api.put(\"/admin/tenders/\".concat(tenderId, \"/submissions/\").concat(submissionId, \"/status\"), data),\n    // Reports and analytics\n    reports: {\n        getFinancialReport: (params)=>api.get(\"/admin/reports/financial\", {\n                params\n            }),\n        getUserReport: (params)=>api.get(\"/admin/reports/users\", {\n                params\n            }),\n        getActivityReport: (params)=>api.get(\"/admin/reports/activity\", {\n                params\n            }),\n        getAuctionReport: (params)=>api.get(\"/admin/reports/auctions\", {\n                params\n            }),\n        getTenderReport: (params)=>api.get(\"/admin/reports/tenders\", {\n                params\n            })\n    },\n    // System settings\n    settings: {\n        getAll: ()=>api.get(\"/admin/settings\"),\n        update: (data)=>api.put(\"/admin/settings\", data),\n        backup: ()=>api.post(\"/admin/settings/backup\"),\n        restore: (backupId)=>api.post(\"/admin/settings/restore/\".concat(backupId))\n    }\n};\n// Government API\nconst governmentAPI = {\n    // Tender management\n    tenders: {\n        getAll: (params)=>api.get(\"/government/tenders\", {\n                params\n            }),\n        getById: (tenderId)=>api.get(\"/tenders/\".concat(tenderId)),\n        create: (data)=>api.post(\"/government/tenders\", data),\n        update: (tenderId, data)=>api.put(\"/government/tenders/\".concat(tenderId), data),\n        delete: (tenderId)=>api.delete(\"/government/tenders/\".concat(tenderId)),\n        publish: (tenderId)=>api.post(\"/government/tenders/\".concat(tenderId, \"/publish\")),\n        close: (tenderId)=>api.post(\"/government/tenders/\".concat(tenderId, \"/close\")),\n        cancel: (tenderId, reason)=>api.post(\"/government/tenders/\".concat(tenderId, \"/cancel\"), {\n                reason\n            })\n    },\n    // Proposal management\n    proposals: {\n        getByTender: (tenderId, params)=>api.get(\"/government/tenders/\".concat(tenderId, \"/proposals\"), {\n                params\n            }),\n        getById: (tenderId, proposalId)=>api.get(\"/government/tenders/\".concat(tenderId, \"/proposals/\").concat(proposalId)),\n        evaluate: (tenderId, proposalId, data)=>api.post(\"/government/tenders/\".concat(tenderId, \"/proposals/\").concat(proposalId, \"/evaluate\"), data),\n        shortlist: (tenderId, proposalId)=>api.post(\"/government/tenders/\".concat(tenderId, \"/proposals/\").concat(proposalId, \"/shortlist\")),\n        award: (tenderId, proposalId, data)=>api.post(\"/government/tenders/\".concat(tenderId, \"/proposals/\").concat(proposalId, \"/award\"), data),\n        reject: (tenderId, proposalId, reason)=>api.post(\"/government/tenders/\".concat(tenderId, \"/proposals/\").concat(proposalId, \"/reject\"), {\n                reason\n            })\n    },\n    // Contract management\n    contracts: {\n        getAll: (params)=>api.get(\"/government/contracts\", {\n                params\n            }),\n        getById: (contractId)=>api.get(\"/government/contracts/\".concat(contractId)),\n        create: (data)=>api.post(\"/government/contracts\", data),\n        update: (contractId, data)=>api.put(\"/government/contracts/\".concat(contractId), data),\n        approve: (contractId)=>api.post(\"/government/contracts/\".concat(contractId, \"/approve\")),\n        terminate: (contractId, reason)=>api.post(\"/government/contracts/\".concat(contractId, \"/terminate\"), {\n                reason\n            })\n    },\n    // Reports and analytics\n    reports: {\n        getDashboard: ()=>api.get(\"/government/reports/dashboard\"),\n        getTenderReport: (params)=>api.get(\"/government/reports/tenders\", {\n                params\n            }),\n        getContractReport: (params)=>api.get(\"/government/reports/contracts\", {\n                params\n            }),\n        getVendorReport: (params)=>api.get(\"/government/reports/vendors\", {\n                params\n            })\n    }\n};\n// Leaderboard API\nconst leaderboardAPI = {\n    getLeaderboard: (params)=>api.get(\"/leaderboard\", {\n            params\n        }),\n    getUserRank: (userId)=>api.get(\"/leaderboard/rank\".concat(userId ? \"/\".concat(userId) : \"\")),\n    getTopBidders: (params)=>api.get(\"/leaderboard/bidders\", {\n            params\n        }),\n    getTopSellers: (params)=>api.get(\"/leaderboard/sellers\", {\n            params\n        })\n};\n// Company API\nconst companyAPI = {\n    getProfile: ()=>api.get(\"/company/profile\"),\n    updateProfile: (data)=>api.put(\"/company/profile\", data),\n    getEmployees: (params)=>api.get(\"/company/employees\", {\n            params\n        }),\n    addEmployee: (data)=>api.post(\"/company/employees\", data),\n    updateEmployee: (employeeId, data)=>api.put(\"/company/employees/\".concat(employeeId), data),\n    removeEmployee: (employeeId)=>api.delete(\"/company/employees/\".concat(employeeId)),\n    getAuctions: (params)=>api.get(\"/company/auctions\", {\n            params\n        }),\n    getTenders: (params)=>api.get(\"/company/tenders\", {\n            params\n        }),\n    getContracts: (params)=>api.get(\"/company/contracts\", {\n            params\n        })\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (api);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api.ts\n"));

/***/ })

});